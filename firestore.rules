rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    function isSignedIn() { return request.auth != null; }

    // User profile docs: each user can read/write their own doc
    match /users/{uid} {
      allow read, write: if request.auth != null && request.auth.uid == uid;
    }

    // Default catch-all: authenticated users can read/write other app data
    // Tighten per-collection as needed
    match /{document=**} {
      allow read, write: if isSignedIn();
    }
  }
}
