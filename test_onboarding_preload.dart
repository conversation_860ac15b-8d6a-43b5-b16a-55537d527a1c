import 'package:supabase_flutter/supabase_flutter.dart';

void main() async {
  // Initialize Supabase
  await Supabase.initialize(
    url: 'https://xtazgqpcaujwwaswzeoh.supabase.co',
    anon<PERSON>ey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh0YXpncXBjYXVqd3dhc3d6ZW9oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzE4MTA5MDUsImV4cCI6MjA0NzM4NjkwNX0.nFutcV81_Na8L-wwxFRpYg7RhqmjMrYspP2LyKbE_q0',
  );

  final client = Supabase.instance.client;

  // Test the RPC function
  print('Testing RPC function get_onboarding_preload...');
  try {
    final rpcResult = await client.rpc('get_onboarding_preload');
    print('RPC Result: $rpcResult');
  } catch (e) {
    print('RPC Error: $e');
  }

  // Test the view
  print('\nTesting onboarding_preload view...');
  try {
    final viewResult = await client
        .from('onboarding_preload')
        .select()
        .limit(1);
    print('View Result: $viewResult');
  } catch (e) {
    print('View Error: $e');
  }

  // Test direct profile table access
  print('\nTesting profiles table...');
  try {
    final profileResult = await client
        .from('profiles')
        .select()
        .limit(1);
    print('Profile Result: $profileResult');
  } catch (e) {
    print('Profile Error: $e');
  }
}