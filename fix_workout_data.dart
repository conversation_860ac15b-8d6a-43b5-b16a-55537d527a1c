import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:openfitv4/config/supabase_config.dart';
import 'package:openfitv4/services/workout_service.dart';
import 'dart:developer' as developer;

/// Script to fix existing workout data and test the workout creation flow
void main() async {
  print('🔧 Starting workout data fix...');
  
  // Initialize Supabase
  await Supabase.initialize(
    url: SupabaseConfig.supabaseUrl,
    anonKey: SupabaseConfig.supabaseAnonKey,
  );
  
  final client = Supabase.instance.client;
  final workoutService = WorkoutService(client);
  
  try {
    // 1. Fix existing workout exercises with null reps/weight arrays
    await fixExistingWorkoutExercises(client);
    
    // 2. Test workout creation
    await testWorkoutCreation(workoutService, client);
    
    // 3. Test n8n webhook
    await testN8nWebhook(workoutService);
    
    print('✅ Workout data fix completed successfully!');
  } catch (e) {
    print('❌ Error during workout data fix: $e');
  }
}

Future<void> fixExistingWorkoutExercises(SupabaseClient client) async {
  print('🔍 Checking for workout exercises with null reps/weight arrays...');
  
  try {
    // Get all workout exercises with null reps or weight arrays
    final brokenExercises = await client
        .from('workout_exercises')
        .select('id, name, sets, reps, weight')
        .or('reps.is.null,weight.is.null');
    
    print('Found ${brokenExercises.length} workout exercises to fix');
    
    for (final exercise in brokenExercises) {
      final exerciseId = exercise['id'] as String;
      final sets = (exercise['sets'] as int?) ?? 3;
      final name = exercise['name'] as String;
      
      // Generate default reps and weights based on exercise type
      List<int> defaultReps;
      List<double> defaultWeights;
      
      if (name.toLowerCase().contains('plank') || name.toLowerCase().contains('hold')) {
        // Time-based exercises (seconds)
        defaultReps = List.generate(sets, (i) => 30 + (i * 5)); // 30, 35, 40 seconds
        defaultWeights = List.filled(sets, 0.0); // No weight
      } else if (name.toLowerCase().contains('cardio') || name.toLowerCase().contains('run')) {
        // Cardio exercises
        defaultReps = List.generate(sets, (i) => 20 - (i * 2)); // 20, 18, 16 reps
        defaultWeights = List.filled(sets, 0.0); // No weight
      } else {
        // Strength exercises
        defaultReps = List.generate(sets, (i) => 12 - (i * 2)); // 12, 10, 8 reps
        defaultWeights = List.generate(sets, (i) => 20.0 + (i * 5)); // 20, 25, 30 kg
      }
      
      // Convert to string arrays for PostgreSQL
      final repsArray = defaultReps.map((r) => r.toString()).toList();
      final weightsArray = defaultWeights.map((w) => w.toString()).toList();
      
      await client
          .from('workout_exercises')
          .update({
            'reps': repsArray,
            'weight': weightsArray,
          })
          .eq('id', exerciseId);
      
      print('✅ Fixed exercise: $name (${defaultReps.length} sets)');
    }
    
    print('✅ Fixed ${brokenExercises.length} workout exercises');
  } catch (e) {
    print('❌ Error fixing workout exercises: $e');
  }
}

Future<void> testWorkoutCreation(WorkoutService workoutService, SupabaseClient client) async {
  print('🧪 Testing workout creation...');
  
  try {
    // Get a test user ID (use the first user in the database)
    final users = await client.from('profiles').select('id').limit(1);
    if (users.isEmpty) {
      print('❌ No users found in database');
      return;
    }
    
    final userId = users.first['id'] as String;
    print('Using test user: $userId');
    
    // Create a test workout
    final workoutId = await workoutService.createWorkout(
      userId: userId,
      name: 'Test Workout - ${DateTime.now().toIso8601String()}',
      aiDescription: 'Test workout created by fix script',
      duration: 45,
    );
    
    if (workoutId == null) {
      print('❌ Failed to create test workout');
      return;
    }
    
    print('✅ Created test workout: $workoutId');
    
    // Add test exercises
    final exercises = [
      {
        'name': 'Push-ups',
        'reps': [12, 10, 8],
        'weights': [0.0, 0.0, 0.0],
      },
      {
        'name': 'Squats',
        'reps': [15, 12, 10],
        'weights': [0.0, 0.0, 0.0],
      },
    ];
    
    // Get some exercise IDs from the database
    final exerciseIds = await client
        .from('exercises')
        .select('id, name')
        .limit(10);
    
    for (int i = 0; i < exercises.length && i < exerciseIds.length; i++) {
      final exercise = exercises[i];
      final exerciseId = exerciseIds[i]['id'] as String;
      
      final success = await workoutService.addExerciseToWorkout(
        workoutId: workoutId,
        exerciseId: exerciseId,
        orderIndex: i,
        repsBySet: exercise['reps'] as List<int>,
        weightsBySet: exercise['weights'] as List<double>,
      );
      
      if (success) {
        print('✅ Added exercise: ${exercise['name']}');
      } else {
        print('❌ Failed to add exercise: ${exercise['name']}');
      }
    }
    
    // Test loading the workout
    final loadedWorkout = await workoutService.getWorkoutById(workoutId);
    if (loadedWorkout != null) {
      print('✅ Successfully loaded test workout: ${loadedWorkout.name}');
      print('   Exercises: ${loadedWorkout.exercises.length}');
      for (final ex in loadedWorkout.exercises) {
        print('   - ${ex.exercise.name}: ${ex.sets.length} sets');
      }
    } else {
      print('❌ Failed to load test workout');
    }
    
  } catch (e) {
    print('❌ Error testing workout creation: $e');
  }
}

Future<void> testN8nWebhook(WorkoutService workoutService) async {
  print('🌐 Testing n8n webhook integration...');
  
  try {
    // Get a test user ID
    final client = Supabase.instance.client;
    final users = await client.from('profiles').select('id').limit(1);
    if (users.isEmpty) {
      print('❌ No users found for webhook test');
      return;
    }
    
    final userId = users.first['id'] as String;
    final success = await workoutService.testN8nWebhook(userId);
    
    if (success) {
      print('✅ n8n webhook test successful');
    } else {
      print('❌ n8n webhook test failed');
    }
  } catch (e) {
    print('❌ Error testing n8n webhook: $e');
  }
}