class OnboardingData {
  // Page 1: Tell Us About Yourself
  String? fullName;
  String? gender;
  String? otherGenderText;
  int? age;
  double? heightInches; // Internal storage in inches
  double? weightPounds; // Internal storage in pounds
  bool isMetric;
  
  // Page 2: Fitness Goals
  List<String> fitnessGoals;
  List<String> goalPriorities; // Ordered list
  String? sportDetails;
  
  // Page 3: Current Fitness Levels
  int cardioLevel; // 0-4 (Low to Elite)
  String? cardioNotes;
  int weightliftingLevel; // 0-4
  String? additionalComments;
  
  // Page 4: Equipment
  List<String> selectedEquipment;
  
  // Page 5: Workout Schedule
  int workoutsPerWeek;
  int? sessionDuration; // in minutes
  bool noTimePreference;
  List<int> workoutDays; // 0-6 (Sunday to Saturday)
  bool noDayPreference;
  
  // Page 6: Additional Info
  String? additionalInfo;
  
  OnboardingData({
    this.fullName,
    this.gender,
    this.otherGenderText,
    this.age,
    this.heightInches,
    this.weightPounds,
    this.isMetric = false,
    List<String>? fitnessGoals,
    List<String>? goalPriorities,
    this.sportDetails,
    this.cardioLevel = 1, // Default to Beginner
    this.cardioNotes,
    this.weightliftingLevel = 1, // Default to Beginner
    this.additionalComments,
    List<String>? selectedEquipment,
    this.workoutsPerWeek = 3,
    this.sessionDuration = 45,
    this.noTimePreference = false,
    List<int>? workoutDays,
    this.noDayPreference = false,
    this.additionalInfo,
  }) : fitnessGoals = fitnessGoals ?? [],
       goalPriorities = goalPriorities ?? [],
       selectedEquipment = selectedEquipment ?? [],
       workoutDays = workoutDays ?? [];
  
  // Conversion helpers
  double get heightCm => (heightInches ?? 0) * 2.54;
  double get weightKg => (weightPounds ?? 0) * 0.453592;
  
  int get heightFeet => ((heightInches ?? 0) / 12).floor();
  int get heightInchesRemainder => ((heightInches ?? 0) % 12).round();
  
  void setHeightFromFeetInches(int feet, int inches) {
    heightInches = (feet * 12 + inches).toDouble();
  }
  
  void setHeightFromCm(double cm) {
    heightInches = cm / 2.54;
  }
  
  void setWeightFromKg(double kg) {
    weightPounds = kg / 0.453592;
  }
  
  void setWeightFromPounds(double pounds) {
    weightPounds = pounds;
  }
  
  // Validation helpers
  bool get isPage1Valid {
    return fullName != null && 
           fullName!.length >= 2 &&
           gender != null &&
           (gender != 'other' || (otherGenderText != null && otherGenderText!.length >= 2)) &&
           age != null &&
           age! >= 13 &&
           age! <= 100 &&
           heightInches != null &&
           heightInches! > 0 &&
           weightPounds != null &&
           weightPounds! > 0;
  }
  
  bool get isPage2Valid {
    return fitnessGoals.isNotEmpty &&
           (!fitnessGoals.contains('sport') || 
            (sportDetails != null && sportDetails!.length >= 2 && sportDetails!.length <= 60));
  }
  
  bool get isPage3Valid {
    return cardioLevel >= 0 && 
           cardioLevel <= 4 &&
           weightliftingLevel >= 0 &&
           weightliftingLevel <= 4 &&
           (cardioNotes == null || cardioNotes!.length <= 500) &&
           (additionalComments == null || additionalComments!.length <= 500);
  }
  
  bool get isPage4Valid => true; // No validation required
  
  bool get isPage5Valid {
    return workoutsPerWeek >= 1 &&
           workoutsPerWeek <= 7 &&
           (noTimePreference || (sessionDuration != null && sessionDuration! >= 15 && sessionDuration! <= 120));
  }
  
  bool get isPage6Valid {
    return additionalInfo == null || additionalInfo!.length <= 1000;
  }
  
  Map<String, dynamic> toJson() {
    return {
      'fullName': fullName,
      'gender': gender,
      'otherGenderText': otherGenderText,
      'age': age,
      'heightInches': heightInches,
      'weightPounds': weightPounds,
      'isMetric': isMetric,
      'fitnessGoals': fitnessGoals,
      'goalPriorities': goalPriorities,
      'sportDetails': sportDetails,
      'cardioLevel': cardioLevel,
      'cardioNotes': cardioNotes,
      'weightliftingLevel': weightliftingLevel,
      'additionalComments': additionalComments,
      'selectedEquipment': selectedEquipment,
      'workoutsPerWeek': workoutsPerWeek,
      'sessionDuration': sessionDuration,
      'noTimePreference': noTimePreference,
      'workoutDays': workoutDays,
      'noDayPreference': noDayPreference,
      'additionalInfo': additionalInfo,
    };
  }
  
  // Create prefilled test data
  factory OnboardingData.testData() {
    return OnboardingData(
      fullName: 'John Doe',
      gender: 'male',
      age: 28,
      heightInches: 70, // 5'10"
      weightPounds: 170,
      isMetric: false,
      fitnessGoals: ['strength', 'muscle'],
      goalPriorities: ['strength', 'muscle'],
      cardioLevel: 2, // Moderate
      weightliftingLevel: 1, // Beginner
      selectedEquipment: ['dumbbells_medium', 'bench', 'pullup_bar'],
      workoutsPerWeek: 4,
      sessionDuration: 45,
      workoutDays: [1, 3, 5, 6], // Mon, Wed, Fri, Sat
    );
  }
}

// Equipment catalog
class Equipment {
  static const List<Map<String, dynamic>> catalog = [
    {'id': 'bodyweight', 'name': 'Bodyweight', 'icon': 'fitness_center'},
    {'id': 'bands', 'name': 'Resistance Bands', 'icon': 'sports'},
    {'id': 'dumbbells_light', 'name': 'Dumbbells (Light)', 'icon': 'fitness_center'},
    {'id': 'dumbbells_medium', 'name': 'Dumbbells (Medium)', 'icon': 'fitness_center'},
    {'id': 'dumbbells_heavy', 'name': 'Dumbbells (Heavy)', 'icon': 'fitness_center'},
    {'id': 'kettlebell_single', 'name': 'Kettlebell', 'icon': 'fitness_center'},
    {'id': 'kettlebell_pair', 'name': 'Kettlebells (Pair)', 'icon': 'fitness_center'},
    {'id': 'barbell', 'name': 'Barbell', 'icon': 'fitness_center'},
    {'id': 'plates', 'name': 'Weight Plates', 'icon': 'fitness_center'},
    {'id': 'bench', 'name': 'Bench', 'icon': 'weekend'},
    {'id': 'rack', 'name': 'Squat Rack', 'icon': 'fitness_center'},
    {'id': 'pullup_bar', 'name': 'Pull-up Bar', 'icon': 'fitness_center'},
    {'id': 'trx', 'name': 'TRX Straps', 'icon': 'fitness_center'},
    {'id': 'cable_machine', 'name': 'Cable Machine', 'icon': 'fitness_center'},
    {'id': 'machines_selector', 'name': 'Gym Machines', 'icon': 'fitness_center'},
    {'id': 'treadmill', 'name': 'Treadmill', 'icon': 'directions_run'},
    {'id': 'bike', 'name': 'Exercise Bike', 'icon': 'directions_bike'},
    {'id': 'rower', 'name': 'Rowing Machine', 'icon': 'rowing'},
  ];
}

// Fitness Goals catalog
class FitnessGoal {
  static const List<Map<String, dynamic>> catalog = [
    {
      'id': 'sport',
      'title': 'Training for a specific sport',
      'description': 'Sport-specific training',
      'icon': 'sports_soccer',
      'requiresDetails': true,
    },
    {
      'id': 'strength',
      'title': 'Increase strength',
      'description': 'Low-rep, high-weight training',
      'icon': 'fitness_center',
    },
    {
      'id': 'stamina',
      'title': 'Increase stamina',
      'description': 'Endurance and cardiovascular fitness',
      'icon': 'directions_run',
    },
    {
      'id': 'health',
      'title': 'Optimize Health and Fitness',
      'description': 'Overall wellness and fitness',
      'icon': 'favorite',
    },
    {
      'id': 'muscle',
      'title': 'Build muscle mass and size',
      'description': 'Hypertrophy training',
      'icon': 'fitness_center',
    },
    {
      'id': 'weight_loss',
      'title': 'Weight loss',
      'description': 'Fat burning and weight reduction',
      'icon': 'trending_down',
    },
  ];
}

// Fitness Level labels
class FitnessLevel {
  static const List<String> labels = [
    'Low',
    'Beginner',
    'Moderate',
    'Advanced',
    'Elite',
  ];
}