import 'package:flutter/foundation.dart';
import '../models/workout_data.dart';
import '../services/workout_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class WorkoutProvider extends ChangeNotifier {
  late final WorkoutService _workoutService = WorkoutService(Supabase.instance.client);
  
  // State
  List<Workout> _allWorkouts = [];
  List<Workout> _activeWorkouts = [];
  List<Workout> _completedWorkouts = [];
  List<Exercise> _exercises = [];
  Workout? _currentWorkout;
  bool _isLoading = false;
  String? _error;
  Map<String, dynamic> _stats = {};

  // Getters
  List<Workout> get allWorkouts => _allWorkouts;
  List<Workout> get activeWorkouts => _activeWorkouts;
  List<Workout> get completedWorkouts => _completedWorkouts;
  List<Exercise> get exercises => _exercises;
  Workout? get currentWorkout => _currentWorkout;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Map<String, dynamic> get stats => _stats;

  // Get workouts for today
  List<Workout> get todayWorkouts {
    final today = DateTime.now();
    return _activeWorkouts.where((workout) {
      if (workout.scheduledDate == null) return false;
      return workout.scheduledDate!.year == today.year &&
             workout.scheduledDate!.month == today.month &&
             workout.scheduledDate!.day == today.day;
    }).toList();
  }

  // Get recent workouts (last 7 days)
  List<Workout> get recentWorkouts {
    final weekAgo = DateTime.now().subtract(const Duration(days: 7));
    return _allWorkouts.where((workout) {
      if (workout.scheduledDate == null) return false;
      return workout.scheduledDate!.isAfter(weekAgo);
    }).toList();
  }

  // Get popular workouts (most completed)
  List<Workout> get popularWorkouts {
    // For now, return completed workouts sorted by rating
    final sorted = List<Workout>.from(_completedWorkouts);
    sorted.sort((a, b) {
      // Sort by completion status and name frequency
      return b.name.compareTo(a.name);
    });
    
    // Return unique workouts by name
    final seen = <String>{};
    return sorted.where((workout) {
      if (seen.contains(workout.name)) return false;
      seen.add(workout.name);
      return true;
    }).take(5).toList();
  }

  // Initialize and load all data
  Future<void> initialize() async {
    await Future.wait([
      loadAllWorkouts(),
      loadExercises(),
      loadStats(),
    ]);
  }

  // Load all workouts
  Future<void> loadAllWorkouts() async {
    try {
      _setLoading(true);
      _clearError();
      
      final userId = Supabase.instance.client.auth.currentUser?.id;
      if (userId == null) {
        _allWorkouts = [];
      } else {
        _allWorkouts = await _workoutService.getUserWorkouts(userId);
      }
      
      // Separate active and completed
      _activeWorkouts = _allWorkouts
          .where((w) => !w.isCompleted && (w as dynamic).isActive != false)
          .toList();
      _completedWorkouts = _allWorkouts
          .where((w) => w.isCompleted)
          .toList();
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to load workouts: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load active workouts only
  Future<void> loadActiveWorkouts() async {
    try {
      _setLoading(true);
      _clearError();
      
      // Narrow active workouts from already-fetched list; service method not implemented
      await loadAllWorkouts();
      _activeWorkouts = _allWorkouts.where((w) => !w.isCompleted).toList();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load active workouts: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load completed workouts
  Future<void> loadCompletedWorkouts() async {
    try {
      _setLoading(true);
      _clearError();
      
      // Narrow completed workouts from already-fetched list; service method not implemented
      await loadAllWorkouts();
      _completedWorkouts = _allWorkouts.where((w) => w.isCompleted).toList();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load completed workouts: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load exercises
  Future<void> loadExercises() async {
    try {
      // Not implemented in WorkoutService yet; return unique exercises from workouts as a fallback
      if (_allWorkouts.isEmpty) {
        await loadAllWorkouts();
      }
      final seen = <String>{};
      final list = <Exercise>[];
      for (final w in _allWorkouts) {
        for (final we in w.exercises) {
          if (seen.add(we.exercise.id)) {
            list.add(we.exercise);
          }
        }
      }
      _exercises = list;
      notifyListeners();
    } catch (e) {
      print('Failed to load exercises: $e');
    }
  }

  // Search exercises
  Future<List<Exercise>> searchExercises({
    String? query,
    String? muscle,
    String? equipment,
    String? category,
  }) async {
    try {
      // Fallback search over local cache until WorkoutService adds server-side search
      if (_exercises.isEmpty) {
        await loadExercises();
      }
      return _exercises.where((e) {
        final q = (query ?? '').toLowerCase();
        final okQ = q.isEmpty || e.name.toLowerCase().contains(q);
        final okM = muscle == null || muscle.isEmpty || e.muscle.toLowerCase() == muscle.toLowerCase();
        final okE = equipment == null || equipment.isEmpty || e.equipment.toLowerCase() == equipment.toLowerCase();
        return okQ && okM && okE;
      }).toList();
    } catch (e) {
      print('Failed to search exercises: $e');
      return [];
    }
  }

  // Load workout by ID
  Future<void> loadWorkout(String workoutId) async {
    try {
      _setLoading(true);
      _clearError();
      
      _currentWorkout = await _workoutService.getWorkoutById(workoutId);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load workout: $e');
    } finally {
      _setLoading(false);
    }
  }

  // ========== New actions: reorder / delete / replace / suggestions / search ==========

  Future<bool> reorderExercises({
    required String workoutId,
    required List<String> orderedWorkoutExerciseIds,
  }) async {
    try {
      final ok = await _workoutService.reorderWorkoutExercises(
        workoutId: workoutId,
        orderedWorkoutExerciseIds: orderedWorkoutExerciseIds,
      );
      if (ok) {
        await loadWorkout(workoutId);
      }
      return ok;
    } catch (e) {
      _setError('Failed to reorder exercises: $e');
      return false;
    }
  }

  Future<bool> deleteExercise({
    required String workoutExerciseId,
    required String workoutId,
  }) async {
    try {
      final ok = await _workoutService.deleteWorkoutExercise(
        workoutExerciseId: workoutExerciseId,
      );
      if (ok) {
        await loadWorkout(workoutId);
      }
      return ok;
    } catch (e) {
      _setError('Failed to delete exercise: $e');
      return false;
    }
  }

  Future<bool> replaceExercise({
    required String workoutExerciseId,
    required String newExerciseId,
    required String workoutId,
    bool regenerateSets = true,
  }) async {
    try {
      final ok = await _workoutService.replaceWorkoutExercise(
        workoutExerciseId: workoutExerciseId,
        newExerciseId: newExerciseId,
        regenerateSets: regenerateSets,
      );
      if (ok) {
        await loadWorkout(workoutId);
      }
      return ok;
    } catch (e) {
      _setError('Failed to replace exercise: $e');
      return false;
    }
  }

  Future<List<Map<String, dynamic>>> suggestByMuscle(String muscle, {String? query}) async {
    try {
      return await _workoutService.suggestExercisesByMuscle(
        primaryMuscle: muscle,
        query: query,
      );
    } catch (e) {
      _setError('Failed to load suggestions: $e');
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> searchAllExercises(String query) async {
    try {
      return await _workoutService.searchExercises(
        query: query,
      );
    } catch (e) {
      _setError('Failed to search: $e');
      return [];
    }
  }

  // Create new workout
  Future<String?> createWorkout({
    required String name,
    String? category,
    int? duration,
    int? caloriesBurn,
  }) async {
    try {
      _setLoading(true);
      _clearError();
      
      final userId = Supabase.instance.client.auth.currentUser?.id;
      if (userId == null) {
        _setError('Not authenticated');
        return null;
      }

      final String? workoutId = await _workoutService.createWorkout(
        userId: userId,
        name: name,
        aiDescription: category, // Use category as description for now
        duration: duration,
      );
      
      if (workoutId != null) {
        // Reload workouts to include the new one
        await loadAllWorkouts();
      }
      
      return workoutId;
    } catch (e) {
      _setError('Failed to create workout: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  // Add exercise to workout
  Future<bool> addExerciseToWorkout({
    required String workoutId,
    required String exerciseId,
    required int orderIndex,
    required List<int> reps,
    List<double>? weights,
    int restIntervalSeconds = 60,
  }) async {
    try {
      final bool success = await _workoutService.addExerciseToWorkout(
        workoutId: workoutId,
        exerciseId: exerciseId,
        orderIndex: orderIndex,
        restTimeSeconds: restIntervalSeconds,
        repsBySet: reps,
        weightsBySet: weights,
      );
      
      if (success) {
        // Reload the current workout
        await loadWorkout(workoutId);
      }
      
      return success;
    } catch (e) {
      _setError('Failed to add exercise: $e');
      return false;
    }
  }

  // Complete workout
  Future<bool> completeWorkout({
    required String workoutId,
    required int duration,
    int? caloriesBurned,
    int? rating,
    String? feedback,
  }) async {
    try {
      _setLoading(true);
      _clearError();
      
      // Not implemented in WorkoutService; mark success false for now
      final bool success = false;
      
      // TODO: Implement workout deletion in WorkoutService
      // if (success) {
      //   // Reload workouts
      //   await loadAllWorkouts();
      //   await loadStats();
      // }
      
      return success;
    } catch (e) {
      _setError('Failed to complete workout: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Load workout statistics
  Future<void> loadStats() async {
    try {
      // Not implemented in WorkoutService; compute simple local stats
      final total = _allWorkouts.length;
      final completed = _allWorkouts.where((w) => w.isCompleted).length;
      _stats = {
        'total_workouts': total,
        'completed_workouts': completed,
      };
      notifyListeners();
    } catch (e) {
      print('Failed to load stats: $e');
    }
  }

  // Start a workout (set as current)
  void startWorkout(Workout workout) {
    _currentWorkout = workout;
    notifyListeners();
  }

  // Clear current workout
  void clearCurrentWorkout() {
    _currentWorkout = null;
    notifyListeners();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  // Clear all data
  void clear() {
    _allWorkouts = [];
    _activeWorkouts = [];
    _completedWorkouts = [];
    _exercises = [];
    _currentWorkout = null;
    _stats = {};
    _error = null;
    notifyListeners();
  }
}
