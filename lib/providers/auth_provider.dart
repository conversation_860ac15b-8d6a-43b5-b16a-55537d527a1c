import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/auth_service.dart';
import '../models/user_model.dart';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();
  
  UserModel? _user;
  bool _isLoading = false;
  String? _error;
  bool _isInitialized = false;

  // Getters
  UserModel? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isSignedIn => _user != null;
  bool get isInitialized => _isInitialized;
  bool get hasCompletedOnboarding => _user?.onboardingCompleted ?? false;
  
  // Derived getters for UI convenience
  String get userName {
    final display = _user?.displayName?.trim();
    if (display != null && display.isNotEmpty) return display;
    // Fallback to email username or generic
    final email = _user?.email ?? '';
    if (email.contains('@')) return email.split('@').first;
    return 'User';
  }

  String get userEmail => _user?.email ?? '';

  AuthProvider() {
    _initialize();
  }

  // Initialize auth state
  void _initialize() {
    // Listen to auth state changes
    _authService.authStateChanges.listen((AuthState state) {
      _handleAuthStateChange(state);
    });

    // Load current user if signed in
    _loadCurrentUser();
  }

  // Handle auth state changes
  Future<void> _handleAuthStateChange(AuthState state) async {
    if (state.event == AuthChangeEvent.signedIn && state.session?.user != null) {
      await _loadCurrentUser();
    } else if (state.event == AuthChangeEvent.signedOut) {
      _user = null;
      _setInitialized(true);
      notifyListeners();
    }
  }

  // Load current user profile
  Future<void> _loadCurrentUser() async {
    developer.log('👤 AuthProvider._loadCurrentUser() called', name: 'AuthProvider');
    
    try {
      developer.log('  🔍 Checking if user is signed in...', name: 'AuthProvider');
      if (_authService.isSignedIn) {
        developer.log('  ✅ User is signed in, fetching profile...', name: 'AuthProvider');
        final profile = await _authService.getCurrentUserProfile();
        developer.log('  📊 Profile received: ${profile != null ? 'success' : 'null'}', name: 'AuthProvider');
        _user = profile;
        if (profile != null) {
          developer.log('  👤 User loaded: ${profile.email}', name: 'AuthProvider');
          developer.log('  🎯 Onboarding completed: ${profile.onboardingCompleted}', name: 'AuthProvider');
        }
      } else {
        developer.log('  ❌ User not signed in, setting user to null', name: 'AuthProvider');
        _user = null;
      }
      
      developer.log('  🔄 Setting initialized to true...', name: 'AuthProvider');
      _setInitialized(true);
      developer.log('  🔔 Notifying listeners...', name: 'AuthProvider');
      notifyListeners();
      developer.log('👤 AuthProvider._loadCurrentUser() completed successfully', name: 'AuthProvider');
    } catch (e, stackTrace) {
      developer.log('❌ AuthProvider._loadCurrentUser() failed', name: 'AuthProvider');
      developer.log('  🔴 Error: ${e.toString()}', name: 'AuthProvider');
      developer.log('  🔴 Error type: ${e.runtimeType}', name: 'AuthProvider');
      developer.log('  📍 Stack trace: ${stackTrace.toString()}', name: 'AuthProvider');
      _user = null;
      _setError('Failed to load user profile: $e');
      _setInitialized(true);
    }
  }

  // Sign up
  Future<bool> signUp({
    required String email,
    required String password,
    String? displayName,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final response = await _authService.signUp(
        email: email,
        password: password,
        displayName: displayName,
      );

      if (response.user != null) {
        // Load the user profile
        await _loadCurrentUser();
        return true;
      }

      return false;
    } catch (e) {
      _setError(_getErrorMessage(e));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Sign in
  Future<bool> signIn({
    required String email,
    required String password,
  }) async {
    print('🚀 AUTHPROVIDER SIGNIN CALLED');
    developer.log('🚀 AuthProvider.signIn() called', name: 'AuthProvider');
    developer.log('  📧 Email: ${email.isNotEmpty ? 'provided' : 'empty'}', name: 'AuthProvider');
    developer.log('  🔑 Password: ${password.isNotEmpty ? 'provided' : 'empty'}', name: 'AuthProvider');
    
    try {
      print('🔄 SETTING LOADING TRUE');
      developer.log('  🔄 Setting loading state to true...', name: 'AuthProvider');
      _setLoading(true);
      _clearError();

      print('📞 CALLING AUTHSERVICE SIGNIN');
      developer.log('  📞 Calling AuthService.signIn()...', name: 'AuthProvider');
      final response = await _authService.signIn(
        email: email,
        password: password,
      );

      print('✅ AUTHSERVICE SIGNIN RETURNED');
      print('👤 Response user: ${response.user}');
      developer.log('  ✅ AuthService.signIn() returned', name: 'AuthProvider');
      developer.log('  👤 Response user: ${response.user != null ? 'present' : 'null'}', name: 'AuthProvider');

      if (response.user != null) {
        print('🔄 LOADING USER PROFILE');
        developer.log('  🔄 Loading current user profile...', name: 'AuthProvider');
        // Load the user profile
        await _loadCurrentUser();
        print('✅ USER PROFILE LOADED - SUCCESS');
        developer.log('  ✅ User profile loaded, signIn successful', name: 'AuthProvider');
        developer.log('🚀 AuthProvider.signIn() completed successfully', name: 'AuthProvider');
        return true;
      }

      print('❌ NO USER IN RESPONSE - SIGNIN FAILED');
      developer.log('  ❌ No user in response, signIn failed', name: 'AuthProvider');
      developer.log('🚀 AuthProvider.signIn() completed with failure', name: 'AuthProvider');
      return false;
    } catch (e, stackTrace) {
      print('❌ AUTHPROVIDER SIGNIN EXCEPTION: $e');
      developer.log('❌ AuthProvider.signIn() failed', name: 'AuthProvider');
      developer.log('  🔴 Error: ${e.toString()}', name: 'AuthProvider');
      developer.log('  🔴 Error type: ${e.runtimeType}', name: 'AuthProvider');
      developer.log('  📍 Stack trace: ${stackTrace.toString()}', name: 'AuthProvider');
      _setError(_getErrorMessage(e));
      return false;
    } finally {
      print('🔄 SETTING LOADING FALSE');
      developer.log('  🔄 Setting loading state to false...', name: 'AuthProvider');
      _setLoading(false);
      developer.log('🚀 AuthProvider.signIn() finally block completed', name: 'AuthProvider');
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      _setLoading(true);
      _clearError();
      
      await _authService.signOut();
      _user = null;
      notifyListeners();
    } catch (e) {
      _setError(_getErrorMessage(e));
    } finally {
      _setLoading(false);
    }
  }

  // Reset password
  Future<bool> resetPassword(String email) async {
    try {
      _setLoading(true);
      _clearError();

      await _authService.resetPassword(email);
      return true;
    } catch (e) {
      _setError(_getErrorMessage(e));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update user profile
  Future<bool> updateProfile(Map<String, dynamic> updates) async {
    try {
      _setLoading(true);
      _clearError();

      final updatedUser = await _authService.updateUserProfile(updates);
      if (updatedUser != null) {
        _user = updatedUser;
        notifyListeners();
        return true;
      }

      return false;
    } catch (e) {
      _setError(_getErrorMessage(e));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Complete onboarding
  Future<bool> completeOnboarding(Map<String, dynamic> onboardingData) async {
    try {
      _setLoading(true);
      _clearError();

      final updatedUser = await _authService.completeOnboarding(onboardingData);
      if (updatedUser != null) {
        _user = updatedUser;
        notifyListeners();
        return true;
      }

      return false;
    } catch (e) {
      _setError(_getErrorMessage(e));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Refresh user profile
  Future<void> refreshProfile() async {
    await _loadCurrentUser();
  }

  // Helper methods
  void _setLoading(bool loading) {
    developer.log('⏳ AuthProvider._setLoading($loading)', name: 'AuthProvider');
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  void _setInitialized(bool initialized) {
    _isInitialized = initialized;
    notifyListeners();
  }

  String _getErrorMessage(dynamic error) {
    developer.log('🔍 AuthProvider._getErrorMessage() called', name: 'AuthProvider');
    developer.log('  🔴 Error type: ${error.runtimeType}', name: 'AuthProvider');
    developer.log('  🔴 Error details: ${error.toString()}', name: 'AuthProvider');
    
    String message;
    if (error is AuthException) {
      developer.log('  📝 AuthException detected: ${error.message}', name: 'AuthProvider');
      message = error.message;
      
      // Provide more user-friendly messages for common auth errors
      switch (error.message.toLowerCase()) {
        case 'invalid login credentials':
          message = 'Invalid email or password. Please try again.';
          break;
        case 'email not confirmed':
          message = 'Please check your email and confirm your account before signing in.';
          break;
        case 'user not found':
          message = 'No account found with this email address.';
          break;
        case 'too many requests':
          message = 'Too many attempts. Please wait a moment and try again.';
          break;
        default:
          message = error.message;
      }
    } else if (error is PostgrestException) {
      developer.log('  📝 PostgrestException detected: ${error.message}', name: 'AuthProvider');
      message = 'Database error: ${error.message}';
    } else {
      developer.log('  📝 Generic error: ${error.toString()}', name: 'AuthProvider');
      message = 'An unexpected error occurred: ${error.toString()}';
    }
    
    developer.log('  📝 Final error message: $message', name: 'AuthProvider');
    return message;
  }

  // Clear error manually
  void clearError() {
    _clearError();
    notifyListeners();
  }
}