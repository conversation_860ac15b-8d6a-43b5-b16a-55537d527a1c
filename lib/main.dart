import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/providers/theme_provider.dart';
import 'package:openfitv4/providers/auth_provider.dart';
import 'package:openfitv4/config/supabase_config.dart';
import 'package:openfitv4/screens/welcome_screen.dart';
import 'package:openfitv4/screens/sign_in_screen.dart';
import 'package:openfitv4/screens/sign_up_screen.dart';
import 'package:openfitv4/screens/onboarding/onboarding_screen_v2.dart';
import 'package:openfitv4/screens/home_screen.dart';
import 'package:openfitv4/providers/workout_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Supabase
  await Supabase.initialize(
    url: SupabaseConfig.supabaseUrl,
    anonKey: SupabaseConfig.supabaseAnonKey,
  );

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => WorkoutProvider()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<ThemeProvider, AuthProvider>(
      builder: (context, themeProvider, authProvider, child) {
        return MaterialApp(
          title: 'OpenFit',
          theme: themeProvider.isInitialized 
            ? themeProvider.getTheme(context)
            : AppTheme.darkTheme,
          debugShowCheckedModeBanner: false,
          home: _getInitialScreen(authProvider),
          routes: {
            '/welcome': (context) => const WelcomeScreen(),
            '/home': (context) => const HomeScreen(),
            '/signin': (context) => const SignInScreen(),
            '/signup': (context) => const SignUpScreen(),
            '/onboarding': (context) => const OnboardingScreenV2(),
          },
        );
      },
    );
  }

  Widget _getInitialScreen(AuthProvider authProvider) {
    print('🏠 NAVIGATION CHECK:');
    print('  isInitialized: ${authProvider.isInitialized}');
    print('  isSignedIn: ${authProvider.isSignedIn}');
    print('  user: ${authProvider.user}');
    print('  hasCompletedOnboarding: ${authProvider.hasCompletedOnboarding}');
    
    // Show loading screen while initializing
    if (!authProvider.isInitialized) {
      print('  → Showing loading screen');
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // If user is signed in
    if (authProvider.isSignedIn) {
      // Check if onboarding is completed
      if (authProvider.hasCompletedOnboarding) {
        print('  → Navigating to HomeScreen');
        return const HomeScreen();
      } else {
        print('  → Navigating to OnboardingScreenV2');
        return const OnboardingScreenV2();
      }
    }

    // User not signed in, show welcome screen
    print('  → Showing WelcomeScreen');
    return const WelcomeScreen();
  }
}
