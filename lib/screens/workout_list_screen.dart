import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/models/workout_data.dart';
import 'package:openfitv4/services/workout_service.dart';
import 'package:openfitv4/screens/workout/workout_detail_screen.dart';
import 'package:openfitv4/screens/create_workout_screen.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:developer' as developer;

class WorkoutListScreen extends StatefulWidget {
  const WorkoutListScreen({super.key});

  @override
  State<WorkoutListScreen> createState() => _WorkoutListScreenState();
}

class _WorkoutListScreenState extends State<WorkoutListScreen> {
  List<Workout> _allWorkouts = [];
  List<Workout> _filteredWorkouts = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedFilter = 'All';
  final TextEditingController _searchController = TextEditingController();
  
  // Bulk selection state
  bool _isSelectionMode = false;
  Set<String> _selectedWorkoutIds = <String>{};

  final List<String> _filterOptions = [
    'All',
    'Upper Body',
    'Lower Body', 
    'Full Body',
    'Cardio',
    'Strength',
  ];

  @override
  void initState() {
    super.initState();
    _loadWorkouts();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedWorkoutIds.clear();
      }
    });
  }

  void _selectAll() {
    setState(() {
      if (_selectedWorkoutIds.length == _filteredWorkouts.length) {
        _selectedWorkoutIds.clear();
      } else {
        _selectedWorkoutIds = _filteredWorkouts.map((w) => w.id).toSet();
      }
    });
  }

  void _toggleWorkoutSelection(String workoutId) {
    setState(() {
      if (_selectedWorkoutIds.contains(workoutId)) {
        _selectedWorkoutIds.remove(workoutId);
      } else {
        _selectedWorkoutIds.add(workoutId);
      }
    });
  }

  Future<void> _createNewWorkout() async {
    // Show options: Create from scratch or use template
    final choice = await showModalBottomSheet<String>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _WorkoutCreationOptionsSheet(),
    );
    
    if (choice == null || !mounted) return;
    
    if (choice == 'custom') {
      final result = await Navigator.push(
        context,
        MaterialPageRoute(builder: (_) => const CreateWorkoutScreen()),
      );
      
      if (result == true && mounted) {
        _loadWorkouts();
      }
    } else if (choice == 'template') {
      _showWorkoutTemplates();
    }
  }
  
  void _showWorkoutTemplates() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => _WorkoutTemplatesSheet(
        onTemplateSelected: (template) async {
          Navigator.pop(context);
          await _createWorkoutFromTemplate(template);
        },
      ),
    );
  }
  
  Future<void> _createWorkoutFromTemplate(WorkoutTemplate template) async {
    try {
      final userId = Supabase.instance.client.auth.currentUser?.id;
      if (userId == null) return;
      
      final service = WorkoutService(Supabase.instance.client);
      
      // Create the workout
      final workoutId = await service.createWorkout(
        userId: userId,
        name: template.name,
        aiDescription: template.description,
      );
      
      if (workoutId == null) {
        throw Exception('Failed to create workout');
      }
      
      // Add exercises from template
      for (int i = 0; i < template.exercises.length; i++) {
        final exercise = template.exercises[i];
        await service.addExerciseToWorkout(
          workoutId: workoutId,
          exerciseId: exercise.exerciseId,
          orderIndex: i,
          restTimeSeconds: exercise.restTime,
          repsBySet: exercise.reps,
          weightsBySet: exercise.weights,
        );
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✅ Created "${template.name}" workout!'),
            backgroundColor: Colors.green,
          ),
        );
        _loadWorkouts();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error creating workout: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteSelectedWorkouts() async {
    if (_selectedWorkoutIds.isEmpty) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Workouts'),
        content: Text('Are you sure you want to delete ${_selectedWorkoutIds.length} workout${_selectedWorkoutIds.length != 1 ? 's' : ''}?\n\nThis will also delete all related exercises and data.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete All'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      // Show progress indicator
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                const SizedBox(width: 16),
                Text('Deleting ${_selectedWorkoutIds.length} workouts...'),
              ],
            ),
            duration: const Duration(seconds: 30),
          ),
        );
      }

      final service = WorkoutService(Supabase.instance.client);
      int deletedCount = 0;
      int failedCount = 0;
      final selectedIds = Set<String>.from(_selectedWorkoutIds); // Copy to avoid modification during iteration
      
      for (final workoutId in selectedIds) {
        try {
          final success = await service.deleteWorkout(workoutId);
          if (success) {
            deletedCount++;
          } else {
            failedCount++;
          }
        } catch (e) {
          failedCount++;
          developer.log('Failed to delete workout $workoutId: $e', name: 'WorkoutListScreen');
        }
      }

      setState(() {
        _allWorkouts.removeWhere((w) => selectedIds.contains(w.id));
        _filterWorkouts();
        _selectedWorkoutIds.clear();
        _isSelectionMode = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        
        if (failedCount == 0) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('✅ Successfully deleted $deletedCount workout${deletedCount != 1 ? 's' : ''}'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('⚠️ Deleted $deletedCount, failed $failedCount workout${failedCount != 1 ? 's' : ''}'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    }
  }

  Future<void> _loadWorkouts() async {
    setState(() => _isLoading = true);
    
    try {
      final userId = Supabase.instance.client.auth.currentUser?.id;
      if (userId != null) {
        final service = WorkoutService(Supabase.instance.client);
        
        // In single workout system, get the active workout
        final activeWorkout = await service.getActiveWorkout(userId);
        final workouts = activeWorkout != null ? [activeWorkout] : <Workout>[];
        
        setState(() {
          _allWorkouts = workouts;
          _filteredWorkouts = workouts;
          _isLoading = false;
        });
      } else {
        setState(() => _isLoading = false);
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading workout: $e')),
        );
      }
    }
  }

  void _filterWorkouts() {
    setState(() {
      _filteredWorkouts = _allWorkouts.where((workout) {
        final matchesSearch = workout.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                            workout.category.toLowerCase().contains(_searchQuery.toLowerCase());
        
        final matchesFilter = _selectedFilter == 'All' || 
                            workout.category.toLowerCase().contains(_selectedFilter.toLowerCase());
        
        return matchesSearch && matchesFilter;
      }).toList();
    });
  }

  Future<void> _deleteWorkout(Workout workout) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Workout'),
        content: Text('Are you sure you want to delete "${workout.name}"?\n\nThis will also delete all related exercises and data.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      // Show loading indicator
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 16),
                Text('Deleting workout...'),
              ],
            ),
            duration: Duration(seconds: 10),
          ),
        );
      }

      try {
        final service = WorkoutService(Supabase.instance.client);
        final success = await service.deleteWorkout(workout.id);
        
        if (success) {
          setState(() {
            _allWorkouts.removeWhere((w) => w.id == workout.id);
            _filterWorkouts();
          });
          
          if (mounted) {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('✅ Deleted "${workout.name}"'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('❌ Failed to delete workout'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('❌ Error deleting workout: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(_isSelectionMode 
          ? '${_selectedWorkoutIds.length} selected'
          : 'My Workouts'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: _isSelectionMode 
          ? IconButton(
              onPressed: _toggleSelectionMode,
              icon: const Icon(Icons.close),
            )
          : null,
        actions: [
          if (_isSelectionMode) ...[
            IconButton(
              onPressed: _selectAll,
              icon: Icon(_selectedWorkoutIds.length == _filteredWorkouts.length 
                ? Icons.deselect 
                : Icons.select_all),
              tooltip: _selectedWorkoutIds.length == _filteredWorkouts.length 
                ? 'Deselect All' 
                : 'Select All',
            ),
            IconButton(
              onPressed: _selectedWorkoutIds.isNotEmpty ? _deleteSelectedWorkouts : null,
              icon: const Icon(Icons.delete, color: Colors.red),
              tooltip: 'Delete Selected',
            ),
          ] else ...[
            IconButton(
              onPressed: _filteredWorkouts.isNotEmpty ? _toggleSelectionMode : null,
              icon: const Icon(Icons.checklist),
              tooltip: 'Select Multiple',
            ),
            IconButton(
              onPressed: _createNewWorkout,
              icon: const Icon(Icons.add),
              tooltip: 'Create Workout',
            ),
            IconButton(
              onPressed: _loadWorkouts,
              icon: const Icon(Icons.refresh),
            ),
          ],
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Padding(
            padding: const EdgeInsets.all(AppSpacing.md),
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search workouts...',
                    prefixIcon: const Icon(Icons.search),
                    filled: true,
                    fillColor: AppColors.surfaceDark,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                  ),
                  onChanged: (value) {
                    setState(() => _searchQuery = value);
                    _filterWorkouts();
                  },
                ),
                
                const SizedBox(height: AppSpacing.sm),
                
                // Filter Chips
                SizedBox(
                  height: 40,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _filterOptions.length,
                    itemBuilder: (context, index) {
                      final filter = _filterOptions[index];
                      final isSelected = _selectedFilter == filter;
                      
                      return Padding(
                        padding: const EdgeInsets.only(right: AppSpacing.xs),
                        child: FilterChip(
                          label: Text(filter),
                          selected: isSelected,
                          onSelected: (selected) {
                            setState(() => _selectedFilter = filter);
                            _filterWorkouts();
                          },
                          backgroundColor: AppColors.surfaceDark,
                          selectedColor: theme.colorScheme.primary.withValues(alpha: 0.2),
                          checkmarkColor: theme.colorScheme.primary,
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          
          // Workout Count and Selection Info
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${_filteredWorkouts.length} workout${_filteredWorkouts.length != 1 ? 's' : ''}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: AppColors.grayLight,
                  ),
                ),
                if (_isSelectionMode && _selectedWorkoutIds.isNotEmpty)
                  Text(
                    '${_selectedWorkoutIds.length} selected',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
              ],
            ),
          ),

          // Select All Row (only in selection mode)
          if (_isSelectionMode && _filteredWorkouts.isNotEmpty) ...[
            const SizedBox(height: AppSpacing.sm),
            Container(
              margin: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
              padding: const EdgeInsets.all(AppSpacing.sm),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.primary.withValues(alpha: 0.2),
                ),
              ),
              child: Row(
                children: [
                  Checkbox(
                    value: _selectedWorkoutIds.length == _filteredWorkouts.length,
                    tristate: true,
                    onChanged: (_) => _selectAll(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: AppSpacing.xs),
                  Expanded(
                    child: GestureDetector(
                      onTap: _selectAll,
                      child: Text(
                        _selectedWorkoutIds.length == _filteredWorkouts.length
                          ? 'Deselect All'
                          : _selectedWorkoutIds.isEmpty
                            ? 'Select All'
                            : 'Select All (${_filteredWorkouts.length - _selectedWorkoutIds.length} more)',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ),
                  ),
                  if (_selectedWorkoutIds.isNotEmpty)
                    TextButton.icon(
                      onPressed: _deleteSelectedWorkouts,
                      icon: const Icon(Icons.delete, size: 18),
                      label: const Text('Delete'),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.red,
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                    ),
                ],
              ),
            ),
          ],
          
          const SizedBox(height: AppSpacing.sm),
          
          // Workout List
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredWorkouts.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
                        itemCount: _filteredWorkouts.length,
                        itemBuilder: (context, index) {
                          final workout = _filteredWorkouts[index];
                          return _buildWorkoutCard(workout);
                        },
                      ),
          ),
        ],
      ),
      floatingActionButton: _isSelectionMode && _selectedWorkoutIds.isNotEmpty
        ? FloatingActionButton.extended(
            onPressed: _deleteSelectedWorkouts,
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
            icon: const Icon(Icons.delete),
            label: Text('Delete ${_selectedWorkoutIds.length}'),
          )
        : FloatingActionButton.extended(
            onPressed: _createNewWorkout,
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: Colors.white,
            icon: const Icon(Icons.add),
            label: const Text('Create Workout'),
          ),
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.fitness_center,
              size: 64,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: AppSpacing.lg),
          Text(
            'No active workout',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Create your next workout or let our AI generate one for you',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: AppColors.grayLight,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.xl),
          if (_searchQuery.isEmpty && _selectedFilter == 'All') ...[
            ElevatedButton.icon(
              onPressed: _createNewWorkout,
              icon: const Icon(Icons.add),
              label: const Text('Create Custom Workout'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            const SizedBox(height: AppSpacing.md),
            OutlinedButton.icon(
              onPressed: () {
                // TODO: Add AI workout generation
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('AI workout generation coming soon!'),
                  ),
                );
              },
              icon: const Icon(Icons.auto_awesome),
              label: const Text('Generate AI Workout'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildWorkoutCard(Workout workout) {
    final theme = Theme.of(context);
    final isSelected = _selectedWorkoutIds.contains(workout.id);
    
    return Card(
      margin: const EdgeInsets.only(bottom: AppSpacing.sm),
      color: isSelected 
        ? theme.colorScheme.primary.withValues(alpha: 0.1)
        : AppColors.surfaceDark,
      child: ListTile(
        contentPadding: const EdgeInsets.all(AppSpacing.md),
        leading: _isSelectionMode
          ? Checkbox(
              value: isSelected,
              onChanged: (_) => _toggleWorkoutSelection(workout.id),
              activeColor: theme.colorScheme.primary,
            )
          : Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.fitness_center,
                color: theme.colorScheme.primary,
              ),
            ),
        title: Text(
          workout.name,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              workout.category,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: AppColors.grayLight,
                ),
                const SizedBox(width: 4),
                Text(
                  '${workout.duration} min',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: AppColors.grayLight,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                Icon(
                  Icons.local_fire_department,
                  size: 16,
                  color: AppColors.grayLight,
                ),
                const SizedBox(width: 4),
                Text(
                  '${workout.caloriesBurn} cal',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: AppColors.grayLight,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: _isSelectionMode 
          ? null
          : PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'view':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => WorkoutDetailScreen(workout: workout),
                      ),
                    );
                    break;
                  case 'delete':
                    _deleteWorkout(workout);
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'view',
                  child: Row(
                    children: [
                      Icon(Icons.visibility),
                      SizedBox(width: 8),
                      Text('View Details'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Delete', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
            ),
        onTap: () {
          if (_isSelectionMode) {
            _toggleWorkoutSelection(workout.id);
          } else {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) => WorkoutDetailScreen(workout: workout),
              ),
            );
          }
        },
      ),
    );
  }
}

// Workout Template System
class WorkoutTemplate {
  final String name;
  final String description;
  final String category;
  final int estimatedDuration;
  final List<TemplateExercise> exercises;
  final IconData icon;
  final Color color;

  WorkoutTemplate({
    required this.name,
    required this.description,
    required this.category,
    required this.estimatedDuration,
    required this.exercises,
    required this.icon,
    required this.color,
  });
}

class TemplateExercise {
  final String exerciseId;
  final String name;
  final List<int> reps;
  final List<double> weights;
  final int restTime;

  TemplateExercise({
    required this.exerciseId,
    required this.name,
    required this.reps,
    required this.weights,
    this.restTime = 60,
  });
}

class _WorkoutCreationOptionsSheet extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: theme.colorScheme.outline.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Create New Workout',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Choose how you\'d like to create your workout',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppColors.grayLight,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          
          // Custom workout option
          _OptionCard(
            icon: Icons.build,
            title: 'Custom Workout',
            subtitle: 'Build from scratch with your own exercises',
            color: theme.colorScheme.primary,
            onTap: () => Navigator.pop(context, 'custom'),
          ),
          
          const SizedBox(height: 16),
          
          // Template option
          _OptionCard(
            icon: Icons.library_books,
            title: 'Use Template',
            subtitle: 'Start with a proven workout template',
            color: const Color(0xFF10B981),
            onTap: () => Navigator.pop(context, 'template'),
          ),
          
          const SizedBox(height: 24),
        ],
      ),
    );
  }
}

class _OptionCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;
  final VoidCallback onTap;

  const _OptionCard({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppColors.surfaceDark,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: AppColors.grayLight,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppColors.grayLight,
            ),
          ],
        ),
      ),
    );
  }
}

class _WorkoutTemplatesSheet extends StatelessWidget {
  final Function(WorkoutTemplate) onTemplateSelected;

  const _WorkoutTemplatesSheet({
    required this.onTemplateSelected,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Sample workout templates - in a real app, these would come from a database or API
    final templates = [
      WorkoutTemplate(
        name: 'Push Day',
        description: 'Chest, shoulders, and triceps focused workout',
        category: 'Upper Body',
        estimatedDuration: 45,
        icon: Icons.fitness_center,
        color: const Color(0xFFEF4444),
        exercises: [
          // These would be real exercise IDs from your database
          TemplateExercise(
            exerciseId: 'sample-id-1',
            name: 'Push-ups',
            reps: [10, 10, 10],
            weights: [0, 0, 0],
          ),
          TemplateExercise(
            exerciseId: 'sample-id-2',
            name: 'Shoulder Press',
            reps: [12, 10, 8],
            weights: [20, 25, 30],
          ),
        ],
      ),
      WorkoutTemplate(
        name: 'Pull Day',
        description: 'Back and biceps focused workout',
        category: 'Upper Body',
        estimatedDuration: 40,
        icon: Icons.fitness_center,
        color: const Color(0xFF3B82F6),
        exercises: [
          TemplateExercise(
            exerciseId: 'sample-id-3',
            name: 'Pull-ups',
            reps: [8, 6, 5],
            weights: [0, 0, 0],
          ),
          TemplateExercise(
            exerciseId: 'sample-id-4',
            name: 'Bicep Curls',
            reps: [12, 10, 8],
            weights: [15, 20, 25],
          ),
        ],
      ),
      WorkoutTemplate(
        name: 'Leg Day',
        description: 'Complete lower body strength workout',
        category: 'Lower Body',
        estimatedDuration: 50,
        icon: Icons.directions_run,
        color: const Color(0xFF10B981),
        exercises: [
          TemplateExercise(
            exerciseId: 'sample-id-5',
            name: 'Squats',
            reps: [15, 12, 10],
            weights: [40, 50, 60],
          ),
          TemplateExercise(
            exerciseId: 'sample-id-6',
            name: 'Lunges',
            reps: [12, 12, 12],
            weights: [20, 20, 20],
          ),
        ],
      ),
      WorkoutTemplate(
        name: 'HIIT Cardio',
        description: 'High-intensity interval training',
        category: 'Cardio',
        estimatedDuration: 25,
        icon: Icons.local_fire_department,
        color: const Color(0xFFF59E0B),
        exercises: [
          TemplateExercise(
            exerciseId: 'sample-id-7',
            name: 'Burpees',
            reps: [10, 10, 10],
            weights: [0, 0, 0],
            restTime: 30,
          ),
          TemplateExercise(
            exerciseId: 'sample-id-8',
            name: 'Mountain Climbers',
            reps: [20, 20, 20],
            weights: [0, 0, 0],
            restTime: 30,
          ),
        ],
      ),
    ];
    
    return FractionallySizedBox(
      heightFactor: 0.8,
      child: Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        ),
        child: Column(
          children: [
            // Header
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.outline.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Workout Templates',
                              style: theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Choose a proven workout to get started quickly',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: AppColors.grayLight,
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Templates List
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                itemCount: templates.length,
                itemBuilder: (context, index) {
                  final template = templates[index];
                  return _TemplateCard(
                    template: template,
                    onTap: () => onTemplateSelected(template),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _TemplateCard extends StatelessWidget {
  final WorkoutTemplate template;
  final VoidCallback onTap;

  const _TemplateCard({
    required this.template,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      color: AppColors.surfaceDark,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: template.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      template.icon,
                      color: template.color,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          template.name,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          template.category,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: template.color,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: AppColors.grayLight,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                template.description,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: AppColors.grayLight,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  _InfoChip(
                    icon: Icons.access_time,
                    label: '${template.estimatedDuration} min',
                  ),
                  const SizedBox(width: 12),
                  _InfoChip(
                    icon: Icons.fitness_center,
                    label: '${template.exercises.length} exercises',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _InfoChip extends StatelessWidget {
  final IconData icon;
  final String label;

  const _InfoChip({
    required this.icon,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: AppColors.backgroundDark.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: AppColors.grayLight,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: AppColors.grayLight,
            ),
          ),
        ],
      ),
    );
  }
}