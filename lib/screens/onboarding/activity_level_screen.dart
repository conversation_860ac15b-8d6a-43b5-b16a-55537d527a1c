import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';

class ActivityLevelScreen extends StatefulWidget {
  final Function(String) onNext;
  final String? selectedLevel;
  
  const ActivityLevelScreen({
    super.key,
    required this.onNext,
    this.selectedLevel,
  });

  @override
  State<ActivityLevelScreen> createState() => _ActivityLevelScreenState();
}

class _ActivityLevelScreenState extends State<ActivityLevelScreen> {
  String? _selectedLevel;

  final List<Map<String, dynamic>> _activityLevels = [
    {
      'id': 'sedentary',
      'title': 'Sedentary',
      'description': 'Little to no exercise',
      'icon': Icons.weekend,
      'multiplier': '1.2x',
    },
    {
      'id': 'lightly_active',
      'title': 'Lightly Active',
      'description': 'Exercise 1-3 days/week',
      'icon': Icons.directions_walk,
      'multiplier': '1.375x',
    },
    {
      'id': 'moderately_active',
      'title': 'Moderately Active',
      'description': 'Exercise 3-5 days/week',
      'icon': Icons.directions_run,
      'multiplier': '1.55x',
    },
    {
      'id': 'very_active',
      'title': 'Very Active',
      'description': 'Exercise 6-7 days/week',
      'icon': Icons.fitness_center,
      'multiplier': '1.725x',
    },
    {
      'id': 'extremely_active',
      'title': 'Extremely Active',
      'description': 'Very hard exercise & physical job',
      'icon': Icons.sports_martial_arts,
      'multiplier': '1.9x',
    },
  ];

  @override
  void initState() {
    super.initState();
    _selectedLevel = widget.selectedLevel;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
      child: Column(
        children: [
          const SizedBox(height: AppSpacing.lg),
          
          // Question
          Text(
            'What is your activity level?',
            style: textTheme.headlineMedium?.copyWith(
              fontSize: 30,
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          // Activity level options
          Column(
            children: _activityLevels.map((level) {
              final isSelected = _selectedLevel == level['id'];
              return Padding(
                padding: const EdgeInsets.only(bottom: AppSpacing.xs),
                child: _buildActivityOption(
                  context,
                  id: level['id'],
                  title: level['title'],
                  description: level['description'],
                  icon: level['icon'],
                  multiplier: level['multiplier'],
                  isSelected: isSelected,
                  onTap: () {
                    setState(() {
                      _selectedLevel = level['id'];
                    });
                  },
                ),
              );
            }).toList(),
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          // Next button
          SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: _selectedLevel != null
                ? () {
                    widget.onNext(_selectedLevel!);
                  }
                : null,
              style: theme.elevatedButtonTheme.style?.copyWith(
                shape: WidgetStateProperty.all(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(19),
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Next',
                    style: textTheme.titleMedium,
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  const Icon(Icons.arrow_forward, size: 20),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: AppSpacing.lg),
        ],
      ),
    );
  }
  
  Widget _buildActivityOption(
    BuildContext context, {
    required String id,
    required String title,
    required String description,
    required IconData icon,
    required String multiplier,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppSpacing.sm),
        decoration: BoxDecoration(
          color: AppColors.surfaceDark,
          borderRadius: BorderRadius.circular(19),
          border: isSelected
            ? Border.all(
                color: theme.colorScheme.primary,
                width: 2,
              )
            : null,
          boxShadow: isSelected
            ? [
                BoxShadow(
                  color: theme.colorScheme.primary.withValues(alpha: 0.25),
                  blurRadius: 0,
                  spreadRadius: 4,
                ),
              ]
            : null,
        ),
        child: Row(
          children: [
            // Icon
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: isSelected
                  ? theme.colorScheme.primary.withValues(alpha: 0.2)
                  : AppColors.grayDark.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: isSelected
                  ? theme.colorScheme.primary
                  : AppColors.grayLight,
                size: 24,
              ),
            ),
            
            const SizedBox(width: AppSpacing.sm),
            
            // Text content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    description,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: AppColors.grayLight,
                    ),
                  ),
                ],
              ),
            ),
            
            // Multiplier badge
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.xs,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: isSelected
                  ? theme.colorScheme.primary
                  : AppColors.grayDark,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                multiplier,
                style: theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: isSelected
                    ? theme.colorScheme.onPrimary
                    : AppColors.grayLight,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}