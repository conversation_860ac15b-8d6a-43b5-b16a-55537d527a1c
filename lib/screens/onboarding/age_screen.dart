import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:openfitv4/theme/app_theme.dart';

class AgeScreen extends StatefulWidget {
  final Function(int) onNext;
  final int? selectedAge;
  
  const AgeScreen({
    super.key,
    required this.onNext,
    this.selectedAge,
  });

  @override
  State<AgeScreen> createState() => _AgeScreenState();
}

class _AgeScreenState extends State<AgeScreen> {
  late int _selectedAge;
  late FixedExtentScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _selectedAge = widget.selectedAge ?? 25;
    _scrollController = FixedExtentScrollController(
      initialItem: _selectedAge - 1,
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Question
          Text(
            'What is your age?',
            style: textTheme.headlineMedium?.copyWith(
              fontSize: 30,
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppSpacing.xxl),
          
          // Age picker
          Container(
            height: 200,
            decoration: BoxDecoration(
              color: AppColors.surfaceDark,
              borderRadius: BorderRadius.circular(32),
            ),
            child: Stack(
              children: [
                // Selection indicator
                Center(
                  child: Container(
                    height: 60,
                    margin: const EdgeInsets.symmetric(horizontal: AppSpacing.lg),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: theme.colorScheme.primary.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                  ),
                ),
                // Picker
                CupertinoPicker(
                  scrollController: _scrollController,
                  itemExtent: 60,
                  onSelectedItemChanged: (index) {
                    setState(() {
                      _selectedAge = index + 1;
                    });
                  },
                  children: List.generate(100, (index) {
                    final age = index + 1;
                    final isSelected = age == _selectedAge;
                    return Center(
                      child: Text(
                        '$age',
                        style: textTheme.displaySmall?.copyWith(
                          fontSize: isSelected ? 48 : 32,
                          fontWeight: FontWeight.w700,
                          color: isSelected
                            ? theme.colorScheme.primary
                            : AppColors.grayMedium,
                        ),
                      ),
                    );
                  }),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppSpacing.lg),
          
          // Years label
          Text(
            'years',
            style: textTheme.titleLarge?.copyWith(
              color: AppColors.grayLight,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xxl),
          
          // Next button
          SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: () {
                widget.onNext(_selectedAge);
              },
              style: theme.elevatedButtonTheme.style?.copyWith(
                shape: WidgetStateProperty.all(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(19),
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Next',
                    style: textTheme.titleMedium,
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  const Icon(Icons.arrow_forward, size: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}