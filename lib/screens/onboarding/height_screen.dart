import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:openfitv4/theme/app_theme.dart';

class HeightScreen extends StatefulWidget {
  final Function(double) onNext;
  final double? selectedHeight;
  
  const HeightScreen({
    super.key,
    required this.onNext,
    this.selectedHeight,
  });

  @override
  State<HeightScreen> createState() => _HeightScreenState();
}

class _HeightScreenState extends State<HeightScreen> {
  late double _selectedHeight; // in cm
  late FixedExtentScrollController _cmController;
  late FixedExtentScrollController _feetController;
  late FixedExtentScrollController _inchesController;
  bool _isCm = true;

  @override
  void initState() {
    super.initState();
    _selectedHeight = widget.selectedHeight ?? 170.0;
    _cmController = FixedExtentScrollController(
      initialItem: _selectedHeight.round() - 100,
    );
    
    // Convert to feet and inches for imperial controllers
    final totalInches = (_selectedHeight / 2.54).round();
    final feet = totalInches ~/ 12;
    final inches = totalInches % 12;
    
    _feetController = FixedExtentScrollController(
      initialItem: feet - 3,
    );
    _inchesController = FixedExtentScrollController(
      initialItem: inches,
    );
  }

  @override
  void dispose() {
    _cmController.dispose();
    _feetController.dispose();
    _inchesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Question
          Text(
            'What is your height?',
            style: textTheme.headlineMedium?.copyWith(
              fontSize: 30,
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppSpacing.xxl),
          
          // Height picker
          Container(
            height: 200,
            decoration: BoxDecoration(
              color: AppColors.surfaceDark,
              borderRadius: BorderRadius.circular(32),
            ),
            child: Stack(
              children: [
                // Selection indicator
                Center(
                  child: Container(
                    height: 60,
                    margin: const EdgeInsets.symmetric(horizontal: AppSpacing.lg),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: theme.colorScheme.primary.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                  ),
                ),
                // Picker
                _isCm
                  ? _buildCmPicker(theme, textTheme)
                  : _buildFeetInchesPicker(theme, textTheme),
              ],
            ),
          ),
          
          const SizedBox(height: AppSpacing.lg),
          
          // Unit toggle
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildUnitToggle(
                context,
                label: 'cm',
                isSelected: _isCm,
                onTap: () {
                  setState(() {
                    _isCm = true;
                  });
                },
              ),
              const SizedBox(width: AppSpacing.xs),
              _buildUnitToggle(
                context,
                label: 'ft/in',
                isSelected: !_isCm,
                onTap: () {
                  setState(() {
                    _isCm = false;
                  });
                },
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.xxl),
          
          // Next button
          SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: () {
                widget.onNext(_selectedHeight);
              },
              style: theme.elevatedButtonTheme.style?.copyWith(
                shape: WidgetStateProperty.all(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(19),
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Next',
                    style: textTheme.titleMedium,
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  const Icon(Icons.arrow_forward, size: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildCmPicker(ThemeData theme, TextTheme textTheme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: 120,
          child: CupertinoPicker(
            scrollController: _cmController,
            itemExtent: 60,
            onSelectedItemChanged: (index) {
              setState(() {
                _selectedHeight = (index + 100).toDouble();
              });
            },
            children: List.generate(150, (index) {
              final height = index + 100;
              final isSelected = height == _selectedHeight.round();
              return Center(
                child: Text(
                  '$height',
                  style: textTheme.displaySmall?.copyWith(
                    fontSize: isSelected ? 48 : 32,
                    fontWeight: FontWeight.w700,
                    color: isSelected
                      ? theme.colorScheme.primary
                      : AppColors.grayMedium,
                  ),
                ),
              );
            }),
          ),
        ),
        Text(
          'cm',
          style: textTheme.titleLarge?.copyWith(
            color: AppColors.grayLight,
          ),
        ),
      ],
    );
  }
  
  Widget _buildFeetInchesPicker(ThemeData theme, TextTheme textTheme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Feet
        SizedBox(
          width: 80,
          child: CupertinoPicker(
            scrollController: _feetController,
            itemExtent: 60,
            onSelectedItemChanged: (index) {
              setState(() {
                final feet = index + 3;
                final totalInches = (_selectedHeight / 2.54).round();
                final inches = totalInches % 12;
                _selectedHeight = ((feet * 12) + inches) * 2.54;
              });
            },
            children: List.generate(6, (index) {
              final feet = index + 3;
              final totalInches = (_selectedHeight / 2.54).round();
              final currentFeet = totalInches ~/ 12;
              final isSelected = feet == currentFeet;
              return Center(
                child: Text(
                  '$feet',
                  style: textTheme.displaySmall?.copyWith(
                    fontSize: isSelected ? 48 : 32,
                    fontWeight: FontWeight.w700,
                    color: isSelected
                      ? theme.colorScheme.primary
                      : AppColors.grayMedium,
                  ),
                ),
              );
            }),
          ),
        ),
        Text(
          'ft',
          style: textTheme.titleLarge?.copyWith(
            color: AppColors.grayLight,
          ),
        ),
        const SizedBox(width: AppSpacing.sm),
        // Inches
        SizedBox(
          width: 80,
          child: CupertinoPicker(
            scrollController: _inchesController,
            itemExtent: 60,
            onSelectedItemChanged: (index) {
              setState(() {
                final totalInches = (_selectedHeight / 2.54).round();
                final feet = totalInches ~/ 12;
                _selectedHeight = ((feet * 12) + index) * 2.54;
              });
            },
            children: List.generate(12, (index) {
              final totalInches = (_selectedHeight / 2.54).round();
              final currentInches = totalInches % 12;
              final isSelected = index == currentInches;
              return Center(
                child: Text(
                  '$index',
                  style: textTheme.displaySmall?.copyWith(
                    fontSize: isSelected ? 48 : 32,
                    fontWeight: FontWeight.w700,
                    color: isSelected
                      ? theme.colorScheme.primary
                      : AppColors.grayMedium,
                  ),
                ),
              );
            }),
          ),
        ),
        Text(
          'in',
          style: textTheme.titleLarge?.copyWith(
            color: AppColors.grayLight,
          ),
        ),
      ],
    );
  }
  
  Widget _buildUnitToggle(
    BuildContext context, {
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.xs,
        ),
        decoration: BoxDecoration(
          color: isSelected
            ? theme.colorScheme.primary
            : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: isSelected
            ? null
            : Border.all(
                color: AppColors.grayMedium,
                width: 1,
              ),
        ),
        child: Text(
          label,
          style: theme.textTheme.titleMedium?.copyWith(
            color: isSelected
              ? theme.colorScheme.onPrimary
              : AppColors.grayLight,
          ),
        ),
      ),
    );
  }
}