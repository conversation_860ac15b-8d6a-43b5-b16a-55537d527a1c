import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';

class GenderScreen extends StatefulWidget {
  final Function(String) onNext;
  final String? selectedGender;
  
  const GenderScreen({
    super.key,
    required this.onNext,
    this.selectedGender,
  });

  @override
  State<GenderScreen> createState() => _GenderScreenState();
}

class _GenderScreenState extends State<GenderScreen> {
  String? _selectedGender;

  @override
  void initState() {
    super.initState();
    _selectedGender = widget.selectedGender;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Question
          Text(
            'What is your gender?',
            style: textTheme.headlineMedium?.copyWith(
              fontSize: 30,
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppSpacing.xxl),
          
          // Gender options
          Column(
            children: [
              _buildGenderOption(
                context,
                label: 'Male',
                icon: Icons.male,
                isSelected: _selectedGender == 'male',
                onTap: () {
                  setState(() {
                    _selectedGender = 'male';
                  });
                },
              ),
              const SizedBox(height: AppSpacing.xs),
              _buildGenderOption(
                context,
                label: 'Female',
                icon: Icons.female,
                isSelected: _selectedGender == 'female',
                onTap: () {
                  setState(() {
                    _selectedGender = 'female';
                  });
                },
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.xxl),
          
          // Next buttons
          Row(
            children: [
              // Skip button
              Expanded(
                child: SizedBox(
                  height: 56,
                  child: ElevatedButton(
                    onPressed: () {
                      widget.onNext('');
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF431407),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(19),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Skip',
                          style: textTheme.titleMedium,
                        ),
                        const SizedBox(width: AppSpacing.sm),
                        const Icon(Icons.arrow_forward, size: 20),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: AppSpacing.xs),
              // Next button
              Expanded(
                child: SizedBox(
                  height: 56,
                  child: ElevatedButton(
                    onPressed: _selectedGender != null
                      ? () {
                          widget.onNext(_selectedGender!);
                        }
                      : null,
                    style: theme.elevatedButtonTheme.style?.copyWith(
                      shape: WidgetStateProperty.all(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(19),
                        ),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Next',
                          style: textTheme.titleMedium,
                        ),
                        const SizedBox(width: AppSpacing.sm),
                        const Icon(Icons.arrow_forward, size: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildGenderOption(
    BuildContext context, {
    required String label,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppSpacing.sm),
        decoration: BoxDecoration(
          color: AppColors.surfaceDark,
          borderRadius: BorderRadius.circular(32),
          border: isSelected
            ? Border.all(
                color: theme.colorScheme.primary,
                width: 1,
              )
            : null,
          boxShadow: isSelected
            ? [
                BoxShadow(
                  color: theme.colorScheme.primary.withValues(alpha: 0.25),
                  blurRadius: 0,
                  spreadRadius: 4,
                ),
              ]
            : null,
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 80,
              color: isSelected
                ? theme.colorScheme.primary
                : AppColors.grayMedium,
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              label,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.onSurface,
              ),
            ),
          ],
        ),
      ),
    );
  }
}