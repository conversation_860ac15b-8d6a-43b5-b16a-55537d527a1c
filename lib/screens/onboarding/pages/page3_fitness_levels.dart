import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/models/onboarding_data.dart';

class Page3FitnessLevels extends StatefulWidget {
  final OnboardingData data;
  final VoidCallback onDataChanged;
  
  const Page3FitnessLevels({
    super.key,
    required this.data,
    required this.onDataChanged,
  });

  @override
  State<Page3FitnessLevels> createState() => _Page3FitnessLevelsState();
}

class _Page3FitnessLevelsState extends State<Page3FitnessLevels> {
  late TextEditingController _cardioNotesController;
  late TextEditingController _additionalController;
  
  @override
  void initState() {
    super.initState();
    _cardioNotesController = TextEditingController(text: widget.data.cardioNotes);
    _additionalController = TextEditingController(text: widget.data.additionalComments);
  }
  
  @override
  void dispose() {
    _cardioNotesController.dispose();
    _additionalController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    
    // Keep controllers in sync with latest model data
    if (_cardioNotesController.text != (widget.data.cardioNotes ?? '')) {
      final newText = widget.data.cardioNotes ?? '';
      _cardioNotesController
        ..text = newText
        ..selection = TextSelection.fromPosition(TextPosition(offset: newText.length));
    }
    if (_additionalController.text != (widget.data.additionalComments ?? '')) {
      final newText = widget.data.additionalComments ?? '';
      _additionalController
        ..text = newText
        ..selection = TextSelection.fromPosition(TextPosition(offset: newText.length));
    }
    
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppSpacing.lg),
          
          // Title
          Text(
            'How would you rate your fitness level?',
            style: textTheme.headlineMedium?.copyWith(
              fontSize: 28,
              fontWeight: FontWeight.w700,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xs),
          
          // Helper text
          Text(
            'Help us understand where you are in your fitness journey',
            style: textTheme.bodyLarge?.copyWith(
              color: AppColors.grayLight,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          // Cardio Level
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Cardio Level',
                style: textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: AppSpacing.sm),
              _buildLevelSlider(
                widget.data.cardioLevel,
                (value) {
                  setState(() {
                    widget.data.cardioLevel = value;
                    widget.onDataChanged();
                  });
                },
              ),
              const SizedBox(height: AppSpacing.sm),
              TextFormField(
                controller: _cardioNotesController,
                style: textTheme.bodyMedium,
                maxLines: 2,
                onChanged: (value) {
                  widget.data.cardioNotes = value;
                  widget.onDataChanged();
                },
                decoration: InputDecoration(
                  hintText: 'Example: I run long distance (6 miles/10km or more)',
                  hintStyle: textTheme.bodySmall?.copyWith(
                    color: AppColors.grayMedium,
                  ),
                  filled: true,
                  fillColor: AppColors.surfaceDark,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.lg),
          
          // Weightlifting Level
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Weightlifting Level',
                style: textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: AppSpacing.sm),
              _buildLevelSlider(
                widget.data.weightliftingLevel,
                (value) {
                  setState(() {
                    widget.data.weightliftingLevel = value;
                    widget.onDataChanged();
                  });
                },
              ),
              const SizedBox(height: AppSpacing.xs),
              Text(
                widget.data.weightliftingLevel <= 1
                  ? 'I am a beginner and have not regularly been to the gym'
                  : FitnessLevel.labels[widget.data.weightliftingLevel],
                style: textTheme.bodySmall?.copyWith(
                  color: AppColors.grayLight,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.lg),
          
          // Additional comments
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Additional comments about your fitness',
                style: textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                'Injuries, limitations, etc.',
                style: textTheme.bodySmall?.copyWith(
                  color: AppColors.grayLight,
                ),
              ),
              const SizedBox(height: AppSpacing.sm),
              TextFormField(
                controller: _additionalController,
                style: textTheme.bodyMedium,
                maxLines: 4,
                maxLength: 500,
                onChanged: (value) {
                  widget.data.additionalComments = value;
                  widget.onDataChanged();
                },
                decoration: InputDecoration(
                  hintText: 'Share any injuries or limitations...',
                  filled: true,
                  fillColor: AppColors.surfaceDark,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.xl),
        ],
      ),
    );
  }
  
  Widget _buildLevelSlider(int currentLevel, Function(int) onChanged) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: AppColors.surfaceDark,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          // Level display
          Text(
            FitnessLevel.labels[currentLevel],
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          // Slider
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: theme.colorScheme.primary,
              inactiveTrackColor: AppColors.grayDark,
              thumbColor: theme.colorScheme.primary,
              overlayColor: theme.colorScheme.primary.withValues(alpha: 0.2),
              trackHeight: 6,
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 12),
            ),
            child: Slider(
              value: currentLevel.toDouble(),
              min: 0,
              max: 4,
              divisions: 4,
              onChanged: (value) => onChanged(value.round()),
            ),
          ),
          // Labels
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSpacing.xs),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: FitnessLevel.labels.map((label) {
                final index = FitnessLevel.labels.indexOf(label);
                final isSelected = index == currentLevel;
                return Text(
                  label,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isSelected
                      ? theme.colorScheme.primary
                      : AppColors.grayMedium,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    fontSize: 10,
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}