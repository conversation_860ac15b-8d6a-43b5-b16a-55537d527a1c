import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/models/onboarding_data.dart';
import 'package:openfitv4/widgets/wheel_picker.dart';

class Page1AboutYou extends StatefulWidget {
  final OnboardingData data;
  final VoidCallback onDataChanged;
  
  const Page1AboutYou({
    super.key,
    required this.data,
    required this.onDataChanged,
  });

  @override
  State<Page1AboutYou> createState() => _Page1AboutYouState();
}

class _Page1AboutYouState extends State<Page1AboutYou> {
  late TextEditingController _nameController;
  late TextEditingController _otherGenderController;
  
  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.data.fullName ?? '');
    _otherGenderController = TextEditingController(text: widget.data.otherGenderText ?? '');
  }
  
  @override
  void dispose() {
    _nameController.dispose();
    _otherGenderController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    // Keep controller in sync with latest model (best approach for async prefill)
    if (_nameController.text != (widget.data.fullName ?? '')) {
      final newText = widget.data.fullName ?? '';
      _nameController
        ..text = newText
        ..selection = TextSelection.fromPosition(TextPosition(offset: newText.length));
    }
    
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppSpacing.lg),
          
          // Title
          Text(
            'Tell us about you',
            style: textTheme.headlineMedium?.copyWith(
              fontSize: 32,
              fontWeight: FontWeight.w700,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xs),
          
          // Helper text
          Text(
            'Help us personalize your fitness journey',
            style: textTheme.bodyLarge?.copyWith(
              color: AppColors.grayLight,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          // Full Name
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    'Full Name',
                    style: textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    ' *',
                    style: textTheme.titleSmall?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppSpacing.xs),
              TextFormField(
                controller: _nameController,
                style: textTheme.bodyLarge,
                onChanged: (value) {
                  widget.data.fullName = value;
                  widget.onDataChanged();
                },
                // onTap sync no longer needed; controller is kept in sync each build
                decoration: InputDecoration(
                  hintText: 'Enter your full name',
                  filled: true,
                  fillColor: AppColors.surfaceDark,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: BorderSide.none,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: BorderSide.none,
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: BorderSide(
                      color: theme.colorScheme.primary,
                      width: 2,
                    ),
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          // Gender
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    'Gender',
                    style: textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    ' *',
                    style: textTheme.titleSmall?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppSpacing.xs),
              Wrap(
                spacing: AppSpacing.xs,
                runSpacing: AppSpacing.xs,
                children: [
                  _buildGenderChip('Male', 'male'),
                  _buildGenderChip('Female', 'female'),
                  _buildGenderChip('Non-binary', 'non-binary'),
                  _buildGenderChip('Prefer not to say', 'prefer-not'),
                  _buildGenderChip('Other', 'other'),
                ],
              ),
              if (widget.data.gender == 'other') ...[
                const SizedBox(height: AppSpacing.xs),
                TextFormField(
                  controller: _otherGenderController,
                  style: textTheme.bodyMedium,
                  onChanged: (value) {
                    widget.data.otherGenderText = value;
                    widget.onDataChanged();
                  },
                  decoration: InputDecoration(
                    hintText: 'Please specify',
                    filled: true,
                    fillColor: AppColors.surfaceDark,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                  ),
                ),
              ],
            ],
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          // Age
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    'Age',
                    style: textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    ' *',
                    style: textTheme.titleSmall?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppSpacing.xs),
              Center(
                child: Column(
                  children: [
                    Text(
                      '${widget.data.age ?? 25}',
                      style: textTheme.displaySmall?.copyWith(
                        fontSize: 48,
                        fontWeight: FontWeight.w700,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.sm),
                    WheelPicker(
                      minValue: 13,
                      maxValue: 100,
                      selectedValue: widget.data.age ?? 25,
                      onChanged: (value) {
                        setState(() {
                          widget.data.age = value;
                          widget.onDataChanged();
                        });
                      },
                      suffix: 'years',
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          // Units toggle
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Units',
                style: textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  color: AppColors.surfaceDark,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    _buildUnitToggle('US', !widget.data.isMetric),
                    _buildUnitToggle('Metric', widget.data.isMetric),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          // Height
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    'Height',
                    style: textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    ' *',
                    style: textTheme.titleSmall?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppSpacing.xs),
              Center(
                child: Column(
                  children: [
                    Text(
                      widget.data.isMetric
                        ? '${widget.data.heightCm.round()} cm'
                        : '${widget.data.heightFeet}\'${widget.data.heightInchesRemainder}"',
                      style: textTheme.displaySmall?.copyWith(
                        fontSize: 36,
                        fontWeight: FontWeight.w700,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.sm),
                    if (widget.data.isMetric)
                      WheelPicker(
                        minValue: 100,
                        maxValue: 250,
                        selectedValue: widget.data.heightCm.round(),
                        onChanged: (value) {
                          setState(() {
                            widget.data.setHeightFromCm(value.toDouble());
                            widget.onDataChanged();
                          });
                        },
                        suffix: 'cm',
                      )
                    else
                      DoubleWheelPicker(
                        minValue1: 3,
                        maxValue1: 8,
                        selectedValue1: widget.data.heightFeet,
                        onChanged1: (feet) {
                          setState(() {
                            widget.data.setHeightFromFeetInches(
                              feet,
                              widget.data.heightInchesRemainder,
                            );
                            widget.onDataChanged();
                          });
                        },
                        suffix1: 'ft',
                        minValue2: 0,
                        maxValue2: 11,
                        selectedValue2: widget.data.heightInchesRemainder,
                        onChanged2: (inches) {
                          setState(() {
                            widget.data.setHeightFromFeetInches(
                              widget.data.heightFeet,
                              inches,
                            );
                            widget.onDataChanged();
                          });
                        },
                        suffix2: 'in',
                      ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          // Weight
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    'Weight',
                    style: textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    ' *',
                    style: textTheme.titleSmall?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppSpacing.xs),
              Center(
                child: Column(
                  children: [
                    Text(
                      widget.data.isMetric
                        ? '${widget.data.weightKg.round()} kg'
                        : '${widget.data.weightPounds?.round() ?? 0} lbs',
                      style: textTheme.displaySmall?.copyWith(
                        fontSize: 36,
                        fontWeight: FontWeight.w700,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.sm),
                    WheelPicker(
                      minValue: widget.data.isMetric ? 30 : 60,
                      maxValue: widget.data.isMetric ? 200 : 400,
                      selectedValue: widget.data.isMetric
                        ? widget.data.weightKg.round()
                        : widget.data.weightPounds?.round() ?? 0,
                      onChanged: (value) {
                        setState(() {
                          if (widget.data.isMetric) {
                            widget.data.setWeightFromKg(value.toDouble());
                          } else {
                            widget.data.setWeightFromPounds(value.toDouble());
                          }
                          widget.onDataChanged();
                        });
                      },
                      suffix: widget.data.isMetric ? 'kg' : 'lbs',
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.xl),
        ],
      ),
    );
  }
  
  Widget _buildGenderChip(String label, String value) {
    final theme = Theme.of(context);
    final isSelected = widget.data.gender == value;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          widget.data.gender = value;
          if (value != 'other') {
            widget.data.otherGenderText = null;
          }
          widget.onDataChanged();
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.sm,
          vertical: AppSpacing.xs,
        ),
        decoration: BoxDecoration(
          color: isSelected
            ? theme.colorScheme.primary
            : AppColors.surfaceDark,
          borderRadius: BorderRadius.circular(12),
          border: isSelected
            ? null
            : Border.all(color: AppColors.grayDark),
        ),
        child: Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isSelected
              ? theme.colorScheme.onPrimary
              : theme.colorScheme.onSurface,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
      ),
    );
  }
  
  Widget _buildUnitToggle(String label, bool isSelected) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: () {
        setState(() {
          widget.data.isMetric = label == 'Metric';
          widget.onDataChanged();
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.xs,
        ),
        decoration: BoxDecoration(
          color: isSelected
            ? theme.colorScheme.primary
            : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isSelected
              ? theme.colorScheme.onPrimary
              : AppColors.grayLight,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
      ),
    );
  }
}
