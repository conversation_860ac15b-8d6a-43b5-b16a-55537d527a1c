import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/models/onboarding_data.dart';
import 'package:openfitv4/widgets/custom_buttons.dart';

class Page7PlanReady extends StatelessWidget {
  final OnboardingData data;
  final VoidCallback onGeneratePlan;
  final VoidCallback onEditAnswers;
  final VoidCallback onSaveAndExit;
  
  const Page7PlanReady({
    super.key,
    required this.data,
    required this.onGeneratePlan,
    required this.onEditAnswers,
    required this.onSaveAndExit,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppSpacing.lg),
          
          Center(
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.check_circle,
                color: theme.colorScheme.primary,
                size: 48,
              ),
            ),
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          Center(
            child: Text(
              'Your Fitness Plan is Ready',
              style: textTheme.headlineMedium?.copyWith(
                fontSize: 28,
                fontWeight: FontWeight.w700,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xs),
          
          Center(
            child: Text(
              'Based on your inputs:',
              style: textTheme.bodyLarge?.copyWith(
                color: AppColors.grayLight,
              ),
            ),
          ),
          
          const SizedBox(height: AppSpacing.lg),
          
          // Summary
          Container(
            padding: const EdgeInsets.all(AppSpacing.sm),
            decoration: BoxDecoration(
              color: AppColors.surfaceDark,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              children: [
                _buildSummaryItem(
                  Icons.calendar_today,
                  '${data.workoutsPerWeek} days a week goal',
                  theme,
                ),
                const SizedBox(height: AppSpacing.sm),
                _buildSummaryItem(
                  Icons.fitness_center,
                  'Focus on ${_getPrimaryGoals()}',
                  theme,
                ),
                const SizedBox(height: AppSpacing.sm),
                _buildSummaryItem(
                  Icons.sports_martial_arts,
                  'Equipment: ${_getEquipmentSummary()}',
                  theme,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppSpacing.lg),
          
          // Video preview card
          Container(
            height: 200,
            decoration: BoxDecoration(
              color: AppColors.surfaceDark,
              borderRadius: BorderRadius.circular(16),
              image: const DecorationImage(
                image: AssetImage('assets/images/yoga_woman.png'),
                fit: BoxFit.cover,
                opacity: 0.3,
              ),
            ),
            child: Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    gradient: LinearGradient(
                      colors: [
                        Colors.black.withValues(alpha: 0.7),
                        Colors.black.withValues(alpha: 0.3),
                      ],
                    ),
                  ),
                ),
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 64,
                        height: 64,
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withValues(alpha: 0.9),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.play_arrow,
                          color: Colors.white,
                          size: 32,
                        ),
                      ),
                      const SizedBox(height: AppSpacing.sm),
                      Text(
                        'See how OpenFit works',
                        style: textTheme.titleMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        '1:45 min',
                        style: textTheme.bodySmall?.copyWith(
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          // Action buttons
          CustomElevatedButton(
            onPressed: onGeneratePlan,
            text: 'Generate My Plan',
          ),
          
          const SizedBox(height: AppSpacing.sm),
          
          Row(
            children: [
              Expanded(
                child: CustomOutlinedButton(
                  onPressed: onEditAnswers,
                  text: 'Edit answers',
                  isPrimary: true,
                ),
              ),
              const SizedBox(width: AppSpacing.sm),
              Expanded(
                child: CustomOutlinedButton(
                  onPressed: onSaveAndExit,
                  text: 'Save & finish later',
                  isPrimary: false,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.xl),
        ],
      ),
    );
  }
  
  Widget _buildSummaryItem(IconData icon, String text, ThemeData theme) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            icon,
            color: theme.colorScheme.primary,
            size: 20,
          ),
        ),
        const SizedBox(width: AppSpacing.sm),
        Expanded(
          child: Text(
            text,
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }
  
  String _getPrimaryGoals() {
    if (data.goalPriorities.isEmpty) return 'general fitness';
    
    final goals = data.goalPriorities.take(2).map((id) {
      switch (id) {
        case 'sport': return 'sport training';
        case 'strength': return 'strength';
        case 'stamina': return 'endurance';
        case 'health': return 'health';
        case 'muscle': return 'muscle building';
        case 'weight_loss': return 'weight loss';
        default: return 'fitness';
      }
    }).join(' and ');
    
    return goals;
  }
  
  String _getEquipmentSummary() {
    if (data.selectedEquipment.isEmpty) return 'Bodyweight only';
    if (data.selectedEquipment.length <= 3) {
      return data.selectedEquipment.map((id) {
        final equipment = Equipment.catalog.firstWhere((e) => e['id'] == id);
        return equipment['name'];
      }).join(', ');
    }
    return '${data.selectedEquipment.length} items selected';
  }
}