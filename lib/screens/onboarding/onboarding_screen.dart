import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/screens/onboarding/gender_screen.dart';
import 'package:openfitv4/screens/onboarding/age_screen.dart';
import 'package:openfitv4/screens/onboarding/weight_screen.dart';
import 'package:openfitv4/screens/onboarding/height_screen.dart';
import 'package:openfitv4/screens/onboarding/activity_level_screen.dart';
import 'package:openfitv4/screens/onboarding/goal_screen.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  
  final Map<String, dynamic> _userData = {
    'gender': null,
    'age': null,
    'weight': null,
    'height': null,
    'activityLevel': null,
    'goal': null,
  };

  void _goToNextPage() {
    if (_currentPage < 5) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      // Complete onboarding
      Navigator.pushReplacementNamed(context, '/home');
    }
  }

  void _goToPreviousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Top Navigation Bar
            Padding(
              padding: const EdgeInsets.all(AppSpacing.sm),
              child: Row(
                children: [
                  // Back button
                  if (_currentPage > 0)
                    GestureDetector(
                      onTap: _goToPreviousPage,
                      child: Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: AppColors.surfaceDark,
                          borderRadius: BorderRadius.circular(18),
                        ),
                        child: Icon(
                          Icons.arrow_back,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    )
                  else
                    const SizedBox(width: 48),
                  
                  const SizedBox(width: AppSpacing.sm),
                  
                  // Title
                  Expanded(
                    child: Text(
                      'Assessment',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                  
                  // Progress indicator
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSpacing.sm,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF172554),
                      borderRadius: BorderRadius.circular(11),
                    ),
                    child: Text(
                      '${_currentPage + 1}/6',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // PageView
            Expanded(
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                children: [
                  GenderScreen(
                    onNext: (gender) {
                      setState(() {
                        _userData['gender'] = gender;
                      });
                      _goToNextPage();
                    },
                    selectedGender: _userData['gender'],
                  ),
                  AgeScreen(
                    onNext: (age) {
                      setState(() {
                        _userData['age'] = age;
                      });
                      _goToNextPage();
                    },
                    selectedAge: _userData['age'],
                  ),
                  WeightScreen(
                    onNext: (weight) {
                      setState(() {
                        _userData['weight'] = weight;
                      });
                      _goToNextPage();
                    },
                    selectedWeight: _userData['weight'],
                  ),
                  HeightScreen(
                    onNext: (height) {
                      setState(() {
                        _userData['height'] = height;
                      });
                      _goToNextPage();
                    },
                    selectedHeight: _userData['height'],
                  ),
                  ActivityLevelScreen(
                    onNext: (level) {
                      setState(() {
                        _userData['activityLevel'] = level;
                      });
                      _goToNextPage();
                    },
                    selectedLevel: _userData['activityLevel'],
                  ),
                  GoalScreen(
                    onNext: (goal) {
                      setState(() {
                        _userData['goal'] = goal;
                      });
                      _goToNextPage();
                    },
                    selectedGoal: _userData['goal'],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}