import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/services/workout_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:developer' as developer;

class CreateWorkoutScreen extends StatefulWidget {
  const CreateWorkoutScreen({super.key});

  @override
  State<CreateWorkoutScreen> createState() => _CreateWorkoutScreenState();
}

class _CreateWorkoutScreenState extends State<CreateWorkoutScreen> {
  final _formKey = GlobalKey<FormState>();
  final _workoutNameController = TextEditingController();
  final _workoutDescriptionController = TextEditingController();
  
  final List<WorkoutExerciseBuilder> _exercises = [];
  List<Map<String, dynamic>> _availableExercises = [];
  List<String> _muscleGroups = [];
  bool _isLoading = false;
  bool _isLoadingExercises = false;
  String _selectedMuscleFilter = 'All';

  @override
  void initState() {
    super.initState();
    _loadMuscleGroups();
    _loadExercises();
  }

  @override
  void dispose() {
    _workoutNameController.dispose();
    _workoutDescriptionController.dispose();
    super.dispose();
  }

  Future<void> _loadMuscleGroups() async {
    try {
      final client = Supabase.instance.client;
      final response = await client
          .from('exercises')
          .select('primary_muscle')
          .not('primary_muscle', 'is', null);
      
      final muscles = <String>{'All'};
      for (final row in response) {
        final muscle = row['primary_muscle'] as String?;
        if (muscle != null && muscle.isNotEmpty) {
          muscles.add(muscle);
        }
      }
      
      setState(() {
        _muscleGroups = muscles.toList()..sort();
      });
    } catch (e) {
      developer.log('Error loading muscle groups: $e');
    }
  }

  Future<void> _loadExercises([String? muscleFilter]) async {
    setState(() => _isLoadingExercises = true);
    
    try {
      final client = Supabase.instance.client;
      var query = client
          .from('exercises')
          .select('id, name, primary_muscle, equipment, instructions');
      
      if (muscleFilter != null && muscleFilter != 'All') {
        query = query.eq('primary_muscle', muscleFilter);
      }
      
      final response = await query.order('name').limit(100);
      
      setState(() {
        _availableExercises = List<Map<String, dynamic>>.from(response);
        _isLoadingExercises = false;
      });
    } catch (e) {
      setState(() => _isLoadingExercises = false);
      developer.log('Error loading exercises: $e');
    }
  }

  void _addExercise(Map<String, dynamic> exercise) {
    // Check if exercise is already added
    final exerciseId = exercise['id'];
    final alreadyExists = _exercises.any((e) => e.exerciseId == exerciseId);
    
    if (alreadyExists) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${exercise['name']} is already in your workout'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }
    
    setState(() {
      _exercises.add(WorkoutExerciseBuilder(
        exerciseId: exerciseId,
        name: exercise['name'] ?? 'Unknown Exercise',
        primaryMuscle: exercise['primary_muscle'] ?? '',
        equipment: exercise['equipment'] ?? '',
        instructions: exercise['instructions'] ?? '',
      ));
    });
    
    // Show success feedback
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('✅ Added ${exercise['name']} to workout'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _removeExercise(int index) {
    setState(() {
      _exercises.removeAt(index);
    });
  }

  void _reorderExercises(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final item = _exercises.removeAt(oldIndex);
      _exercises.insert(newIndex, item);
    });
  }

  Future<void> _saveWorkout() async {
    if (!_formKey.currentState!.validate() || _exercises.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add at least one exercise')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final userId = Supabase.instance.client.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final service = WorkoutService(Supabase.instance.client);
      
      // Create the workout
      final workoutId = await service.createWorkout(
        userId: userId,
        name: _workoutNameController.text.trim(),
        aiDescription: _workoutDescriptionController.text.trim().isNotEmpty 
          ? _workoutDescriptionController.text.trim() 
          : null,
      );

      if (workoutId == null) {
        throw Exception('Failed to create workout');
      }

      // Add exercises to the workout
      for (int i = 0; i < _exercises.length; i++) {
        final exercise = _exercises[i];
        final success = await service.addExerciseToWorkout(
          workoutId: workoutId,
          exerciseId: exercise.exerciseId,
          orderIndex: i,
          restTimeSeconds: exercise.restInterval,
          repsBySet: exercise.reps,
          weightsBySet: exercise.weights,
        );
        
        if (!success) {
          throw Exception('Failed to add exercise: ${exercise.name}');
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Workout created successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error creating workout: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Create Workout'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else
            TextButton(
              onPressed: _exercises.isNotEmpty ? _saveWorkout : null,
              child: const Text('Save'),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            // Workout Details Section
            Container(
              padding: const EdgeInsets.all(AppSpacing.md),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextFormField(
                    controller: _workoutNameController,
                    decoration: const InputDecoration(
                      labelText: 'Workout Name',
                      hintText: 'e.g., Upper Body Strength',
                      filled: true,
                      fillColor: AppColors.surfaceDark,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(12)),
                        borderSide: BorderSide.none,
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter a workout name';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: AppSpacing.sm),
                  TextFormField(
                    controller: _workoutDescriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description (Optional)',
                      hintText: 'Brief description of the workout',
                      filled: true,
                      fillColor: AppColors.surfaceDark,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(12)),
                        borderSide: BorderSide.none,
                      ),
                    ),
                    maxLines: 2,
                  ),
                ],
              ),
            ),

            // Exercise List Section
            Expanded(
              child: Column(
                children: [
                  // Section Header
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Exercises (${_exercises.length})',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        TextButton.icon(
                          onPressed: _showExerciseSelector,
                          icon: const Icon(Icons.add),
                          label: const Text('Add Exercise'),
                        ),
                      ],
                    ),
                  ),

                  // Exercise List
                  Expanded(
                    child: _exercises.isEmpty
                        ? _buildEmptyState()
                        : ReorderableListView.builder(
                            padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
                            itemCount: _exercises.length,
                            onReorder: _reorderExercises,
                            itemBuilder: (context, index) {
                              final exercise = _exercises[index];
                              return _buildExerciseCard(exercise, index);
                            },
                          ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.fitness_center,
            size: 64,
            color: AppColors.grayLight,
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'No exercises added yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.grayLight,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Tap "Add Exercise" to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.grayLight,
            ),
          ),
          const SizedBox(height: AppSpacing.md),
          ElevatedButton.icon(
            onPressed: _showExerciseSelector,
            icon: const Icon(Icons.add),
            label: const Text('Add Exercise'),
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseCard(WorkoutExerciseBuilder exercise, int index) {
    final theme = Theme.of(context);
    
    return Card(
      key: ValueKey(exercise.exerciseId),
      margin: const EdgeInsets.only(bottom: AppSpacing.sm),
      color: AppColors.surfaceDark,
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Exercise Header
            Row(
              children: [
                Icon(
                  Icons.drag_handle,
                  color: AppColors.grayLight,
                ),
                const SizedBox(width: AppSpacing.xs),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        exercise.name,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        '${exercise.primaryMuscle} • ${exercise.equipment}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: AppColors.grayLight,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => _removeExercise(index),
                  icon: const Icon(Icons.delete, color: Colors.red),
                ),
              ],
            ),

            const SizedBox(height: AppSpacing.sm),

            // Sets Configuration
            _buildSetsConfiguration(exercise),
          ],
        ),
      ),
    );
  }

  Widget _buildSetsConfiguration(WorkoutExerciseBuilder exercise) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Sets: ${exercise.sets}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: AppSpacing.md),
            Text(
              'Rest: ${exercise.restInterval}s',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.grayLight,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.xs),
        
        // Sets Details
        for (int i = 0; i < exercise.sets; i++)
          Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              children: [
                SizedBox(
                  width: 60,
                  child: Text(
                    'Set ${i + 1}:',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
                Text(
                  '${exercise.reps[i]} reps',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                if (exercise.weights[i] > 0) ...[
                  const SizedBox(width: AppSpacing.sm),
                  Text(
                    '@ ${exercise.weights[i]}kg',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.grayLight,
                    ),
                  ),
                ],
              ],
            ),
          ),
        
        const SizedBox(height: AppSpacing.xs),
        TextButton(
          onPressed: () => _editExerciseDetails(exercise),
          child: const Text('Edit Sets & Reps'),
        ),
      ],
    );
  }

  void _showExerciseSelector() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
      isDismissible: true,
      builder: (context) => _ExerciseSelectorSheet(
        availableExercises: _availableExercises,
        muscleGroups: _muscleGroups,
        selectedMuscleFilter: _selectedMuscleFilter,
        isLoadingExercises: _isLoadingExercises,
        onMuscleFilterChanged: (muscle) {
          setState(() => _selectedMuscleFilter = muscle);
          _loadExercises(muscle);
        },
        onExerciseSelected: (exercise) {
          _addExercise(exercise);
          // Provide haptic feedback
          // HapticFeedback.lightImpact(); // Uncomment if you want haptic feedback
        },
      ),
    );
  }

  void _editExerciseDetails(WorkoutExerciseBuilder exercise) {
    showDialog(
      context: context,
      builder: (context) => _ExerciseDetailsDialog(
        exercise: exercise,
        onSave: (updatedExercise) {
          setState(() {
            final index = _exercises.indexWhere((e) => e.exerciseId == exercise.exerciseId);
            if (index != -1) {
              _exercises[index] = updatedExercise;
            }
          });
        },
      ),
    );
  }
}

class WorkoutExerciseBuilder {
  final String exerciseId;
  final String name;
  final String primaryMuscle;
  final String equipment;
  final String instructions;
  int sets;
  List<int> reps;
  List<double> weights;
  int restInterval;

  WorkoutExerciseBuilder({
    required this.exerciseId,
    required this.name,
    required this.primaryMuscle,
    required this.equipment,
    required this.instructions,
    this.sets = 3,
    List<int>? reps,
    List<double>? weights,
    this.restInterval = 60,
  }) : reps = reps ?? List.filled(3, 10),
       weights = weights ?? List.filled(3, 0.0);
}

class _ExerciseSelectorSheet extends StatefulWidget {
  final List<Map<String, dynamic>> availableExercises;
  final List<String> muscleGroups;
  final String selectedMuscleFilter;
  final bool isLoadingExercises;
  final Function(String) onMuscleFilterChanged;
  final Function(Map<String, dynamic>) onExerciseSelected;

  const _ExerciseSelectorSheet({
    required this.availableExercises,
    required this.muscleGroups,
    required this.selectedMuscleFilter,
    required this.isLoadingExercises,
    required this.onMuscleFilterChanged,
    required this.onExerciseSelected,
  });

  @override
  State<_ExerciseSelectorSheet> createState() => _ExerciseSelectorSheetState();
}

class _ExerciseSelectorSheetState extends State<_ExerciseSelectorSheet> {
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, dynamic>> _filteredExercises = [];
  String _currentSearchQuery = '';

  @override
  void initState() {
    super.initState();
    _updateFilteredExercises();
  }

  @override
  void didUpdateWidget(_ExerciseSelectorSheet oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.availableExercises != widget.availableExercises ||
        oldWidget.selectedMuscleFilter != widget.selectedMuscleFilter) {
      _updateFilteredExercises();
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _updateFilteredExercises() {
    setState(() {
      _filteredExercises = widget.availableExercises.where((exercise) {
        final name = (exercise['name'] as String? ?? '').toLowerCase();
        final muscle = (exercise['primary_muscle'] as String? ?? '').toLowerCase();
        
        // Apply search filter
        final matchesSearch = _currentSearchQuery.isEmpty || 
            name.contains(_currentSearchQuery.toLowerCase()) ||
            muscle.contains(_currentSearchQuery.toLowerCase());
        
        return matchesSearch;
      }).toList();
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _currentSearchQuery = query;
    });
    _updateFilteredExercises();
  }

  void _onExerciseSelected(Map<String, dynamic> exercise) {
    // Add some feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Added ${exercise['name']}'),
        duration: const Duration(seconds: 1),
        backgroundColor: Colors.green,
      ),
    );
    
    // Call the callback
    widget.onExerciseSelected(exercise);
    
    // Close the sheet
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        children: [
          // Drag handle
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: theme.colorScheme.outline.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(AppSpacing.md),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Add Exercise',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),

          // Search Bar
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search exercises...',
                prefixIcon: const Icon(Icons.search),
                filled: true,
                fillColor: AppColors.surfaceDark,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                suffixIcon: _currentSearchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _onSearchChanged('');
                        },
                      )
                    : null,
              ),
              onChanged: _onSearchChanged,
            ),
          ),

          const SizedBox(height: AppSpacing.sm),

          // Muscle Filter Chips
          if (widget.muscleGroups.isNotEmpty) ...[
            SizedBox(
              height: 50,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
                itemCount: widget.muscleGroups.length,
                itemBuilder: (context, index) {
                  final muscle = widget.muscleGroups[index];
                  final isSelected = muscle == widget.selectedMuscleFilter;
                  
                  return Padding(
                    padding: const EdgeInsets.only(right: AppSpacing.xs),
                    child: FilterChip(
                      label: Text(muscle),
                      selected: isSelected,
                      onSelected: (_) {
                        widget.onMuscleFilterChanged(muscle);
                      },
                      backgroundColor: AppColors.surfaceDark,
                      selectedColor: theme.colorScheme.primary.withValues(alpha: 0.2),
                      checkmarkColor: theme.colorScheme.primary,
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
          ],

          // Results count
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
            child: Row(
              children: [
                Text(
                  '${_filteredExercises.length} exercise${_filteredExercises.length != 1 ? 's' : ''} found',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: AppColors.grayLight,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: AppSpacing.sm),

          // Exercise List
          Expanded(
            child: widget.isLoadingExercises
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Loading exercises...'),
                      ],
                    ),
                  )
                : _filteredExercises.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.search_off,
                              size: 64,
                              color: AppColors.grayLight,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No exercises found',
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: AppColors.grayLight,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Try adjusting your search or filters',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: AppColors.grayLight,
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
                        itemCount: _filteredExercises.length,
                        itemBuilder: (context, index) {
                          final exercise = _filteredExercises[index];
                          return _ExerciseListItem(
                            exercise: exercise,
                            onTap: () => _onExerciseSelected(exercise),
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }
}

class _ExerciseListItem extends StatelessWidget {
  final Map<String, dynamic> exercise;
  final VoidCallback onTap;

  const _ExerciseListItem({
    required this.exercise,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.only(bottom: AppSpacing.xs),
      color: AppColors.surfaceDark,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Exercise icon
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.fitness_center,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Exercise details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      exercise['name'] ?? 'Unknown Exercise',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.accessibility_new,
                          size: 16,
                          color: AppColors.grayLight,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          exercise['primary_muscle'] ?? 'Unknown',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.primary,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Icon(
                          Icons.build,
                          size: 16,
                          color: AppColors.grayLight,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            exercise['equipment'] ?? 'Unknown',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: AppColors.grayLight,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Add button
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.add,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _ExerciseDetailsDialog extends StatefulWidget {
  final WorkoutExerciseBuilder exercise;
  final Function(WorkoutExerciseBuilder) onSave;

  const _ExerciseDetailsDialog({
    required this.exercise,
    required this.onSave,
  });

  @override
  State<_ExerciseDetailsDialog> createState() => _ExerciseDetailsDialogState();
}

class _ExerciseDetailsDialogState extends State<_ExerciseDetailsDialog> {
  late int _sets;
  late List<int> _reps;
  late List<double> _weights;
  late int _restInterval;

  @override
  void initState() {
    super.initState();
    _sets = widget.exercise.sets;
    _reps = List.from(widget.exercise.reps);
    _weights = List.from(widget.exercise.weights);
    _restInterval = widget.exercise.restInterval;
  }

  void _updateSets(int newSets) {
    setState(() {
      _sets = newSets;
      
      // Adjust reps and weights arrays
      while (_reps.length < newSets) {
        _reps.add(10);
        _weights.add(0.0);
      }
      while (_reps.length > newSets) {
        _reps.removeLast();
        _weights.removeLast();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.exercise.name),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Sets Selector
            Row(
              children: [
                const Text('Sets: '),
                DropdownButton<int>(
                  value: _sets,
                  items: List.generate(6, (i) => i + 1)
                      .map((sets) => DropdownMenuItem(
                            value: sets,
                            child: Text('$sets'),
                          ))
                      .toList(),
                  onChanged: (value) => _updateSets(value!),
                ),
                const Spacer(),
                const Text('Rest: '),
                DropdownButton<int>(
                  value: _restInterval,
                  items: [30, 45, 60, 90, 120, 180]
                      .map((seconds) => DropdownMenuItem(
                            value: seconds,
                            child: Text('${seconds}s'),
                          ))
                      .toList(),
                  onChanged: (value) => setState(() => _restInterval = value!),
                ),
              ],
            ),

            const SizedBox(height: AppSpacing.md),

            // Sets Configuration
            for (int i = 0; i < _sets; i++)
              Padding(
                padding: const EdgeInsets.only(bottom: AppSpacing.sm),
                child: Row(
                  children: [
                    SizedBox(
                      width: 60,
                      child: Text('Set ${i + 1}:'),
                    ),
                    Expanded(
                      child: TextFormField(
                        initialValue: _reps[i].toString(),
                        decoration: const InputDecoration(
                          labelText: 'Reps',
                          isDense: true,
                        ),
                        keyboardType: TextInputType.number,
                        onChanged: (value) {
                          _reps[i] = int.tryParse(value) ?? _reps[i];
                        },
                      ),
                    ),
                    const SizedBox(width: AppSpacing.sm),
                    Expanded(
                      child: TextFormField(
                        initialValue: _weights[i].toString(),
                        decoration: const InputDecoration(
                          labelText: 'Weight (kg)',
                          isDense: true,
                        ),
                        keyboardType: TextInputType.number,
                        onChanged: (value) {
                          _weights[i] = double.tryParse(value) ?? _weights[i];
                        },
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final updatedExercise = WorkoutExerciseBuilder(
              exerciseId: widget.exercise.exerciseId,
              name: widget.exercise.name,
              primaryMuscle: widget.exercise.primaryMuscle,
              equipment: widget.exercise.equipment,
              instructions: widget.exercise.instructions,
              sets: _sets,
              reps: _reps,
              weights: _weights,
              restInterval: _restInterval,
            );
            widget.onSave(updatedExercise);
            Navigator.pop(context);
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
}