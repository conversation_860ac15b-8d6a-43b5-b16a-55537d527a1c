import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:openfitv4/config/app_config.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ChatScreenImproved extends StatefulWidget {
  const ChatScreenImproved({super.key});

  @override
  State<ChatScreenImproved> createState() => _ChatScreenImprovedState();
}

class _ChatScreenImprovedState extends State<ChatScreenImproved> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ChatMessage> _messages = [
    ChatMessage(
      text: "Hi! I'm your OpenFit AI coach. How can I help you today?",
      isUser: false,
      timestamp: DateTime.now(),
    ),
  ];
  String? _sessionId;

  @override
  void initState() {
    super.initState();
    _ensureSessionId();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _sendMessage() async {
    if (_messageController.text.trim().isEmpty) return;
    
    setState(() {
      _messages.add(ChatMessage(
        text: _messageController.text,
        isUser: true,
        timestamp: DateTime.now(),
      ));
    });
    final userText = _messageController.text;
    _messageController.clear();
    _scrollToBottom();

    if (AppConfig.chatWebhookUrl.isEmpty) {
      await Future.delayed(const Duration(seconds: 1));
      if (!mounted) return;
      setState(() {
        _messages.add(ChatMessage(
          text: "I'm here to help you with your fitness journey! This feature is coming soon.",
          isUser: false,
          timestamp: DateTime.now(),
        ));
      });
      _scrollToBottom();
      return;
    }

    setState(() {
      _messages.add(ChatMessage(
        text: '...',
        isUser: false,
        timestamp: DateTime.now(),
      ));
    });
    _scrollToBottom();

    final supabaseUser = Supabase.instance.client.auth.currentUser;
    if (_sessionId == null) {
      await _ensureSessionId();
    }
    final uri = Uri.parse(AppConfig.chatWebhookUrl);
    final payload = <String, dynamic>{
      'chatInput': userText,
      'sessionId': _sessionId,
      if (supabaseUser != null) 'user_id': supabaseUser.id,
    };

    try {
      final resp = await http.post(
        uri,
        headers: const {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode(payload),
      );

      http.Response finalResponse = resp;

      String replyText = '';
      if (finalResponse.statusCode >= 200 && finalResponse.statusCode < 300) {
        final contentType = finalResponse.headers['content-type'] ?? '';
        final body = finalResponse.body;
        if (contentType.contains('application/json')) {
          dynamic decoded;
          try {
            decoded = body.isNotEmpty ? json.decode(body) : null;
          } catch (_) {
            decoded = null;
          }
          if (decoded != null) {
            replyText = _extractReplyFromJson(decoded) ?? body.toString();
          } else {
            replyText = body.trim().isEmpty ? 'Okay.' : body.trim();
          }
        } else {
          replyText = body.trim().isEmpty ? 'Okay.' : body.trim();
        }
      } else {
        replyText = 'Error ${finalResponse.statusCode}: unable to get a response.';
      }

      if (!mounted) return;
      setState(() {
        final typingIndex = _messages.lastIndexWhere((m) => !m.isUser && m.text == '...');
        if (typingIndex != -1) {
          _messages[typingIndex] = ChatMessage(
            text: replyText,
            isUser: false,
            timestamp: DateTime.now(),
          );
        } else {
          _messages.add(ChatMessage(
            text: replyText,
            isUser: false,
            timestamp: DateTime.now(),
          ));
        }
      });
      _scrollToBottom();
    } catch (e) {
      if (!mounted) return;
      setState(() {
        final typingIndex = _messages.lastIndexWhere((m) => !m.isUser && m.text == '...');
        final errorText = 'Network error: $e';
        if (typingIndex != -1) {
          _messages[typingIndex] = ChatMessage(
            text: errorText,
            isUser: false,
            timestamp: DateTime.now(),
          );
        } else {
          _messages.add(ChatMessage(
            text: errorText,
            isUser: false,
            timestamp: DateTime.now(),
          ));
        }
      });
      _scrollToBottom();
    }
  }

  void _scrollToBottom() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        // Messages list
        Expanded(
          child: ListView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.all(AppSpacing.sm),
            itemCount: _messages.length,
            itemBuilder: (context, index) {
              final message = _messages[index];
              return _buildMessage(context, message);
            },
          ),
        ),
        
        // Input field
        Container(
          padding: const EdgeInsets.all(AppSpacing.sm),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            border: Border(
              top: BorderSide(
                color: theme.colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
          ),
          child: SafeArea(
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: InputDecoration(
                      hintText: 'Ask me anything...',
                      filled: true,
                      fillColor: AppColors.surfaceDark,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide.none,
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: AppSpacing.md,
                        vertical: AppSpacing.sm,
                      ),
                    ),
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
                const SizedBox(width: AppSpacing.xs),
                IconButton(
                  onPressed: () => _sendMessage(),
                  icon: Icon(
                    Icons.send_rounded,
                    color: theme.colorScheme.primary,
                  ),
                  style: IconButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary.withValues(alpha: 0.1),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMessage(BuildContext context, ChatMessage message) {
    final theme = Theme.of(context);
    final isUser = message.isUser;
    
    return Align(
      alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.only(bottom: AppSpacing.sm),
        padding: const EdgeInsets.all(AppSpacing.sm),
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        decoration: BoxDecoration(
          color: isUser 
            ? theme.colorScheme.primary 
            : AppColors.surfaceDark,
          borderRadius: BorderRadius.only(
            topLeft: const Radius.circular(16),
            topRight: const Radius.circular(16),
            bottomLeft: Radius.circular(isUser ? 16 : 4),
            bottomRight: Radius.circular(isUser ? 4 : 16),
          ),
        ),
        child: Text(
          message.text,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isUser 
              ? theme.colorScheme.onPrimary 
              : theme.colorScheme.onSurface,
          ),
        ),
      ),
    );
  }

  String? _extractReplyFromJson(dynamic decoded) {
    if (decoded is String) return decoded;
    if (decoded is Map<String, dynamic>) {
      // Chat Trigger response format - check for 'text' field first
      if (decoded['text'] is String) return decoded['text'] as String;
      
      // Capture sessionId if returned alongside output
      if (decoded['sessionId'] is String && _sessionId == null) {
        _persistSessionId(decoded['sessionId'] as String);
      }
      if (decoded['output'] is String) return decoded['output'] as String;
      if (decoded['reply'] is String) return decoded['reply'] as String;
      if (decoded['message'] is String) return decoded['message'] as String;
      if (decoded['content'] is String) return decoded['content'] as String;
      if (decoded['data'] is Map<String, dynamic>) {
        final data = decoded['data'] as Map<String, dynamic>;
        if (data['sessionId'] is String && _sessionId == null) {
          _persistSessionId(data['sessionId'] as String);
        }
        if (data['text'] is String) return data['text'] as String;
        if (data['output'] is String) return data['output'] as String;
        if (data['reply'] is String) return data['reply'] as String;
        if (data['message'] is String) return data['message'] as String;
        if (data['content'] is String) return data['content'] as String;
      }
      if (decoded['choices'] is List && (decoded['choices'] as List).isNotEmpty) {
        final first = (decoded['choices'] as List).first;
        if (first is Map<String, dynamic>) {
          final msg = first['message'];
          if (msg is Map<String, dynamic> && msg['content'] is String) {
            return msg['content'] as String;
          }
          if (first['text'] is String) return first['text'] as String;
        }
      }
    }
    if (decoded is List) {
      if (decoded.isEmpty) return null;
      final first = decoded.first;
      if (first is String) return first;
      if (first is Map<String, dynamic>) {
        if (first['sessionId'] is String && _sessionId == null) {
          _persistSessionId(first['sessionId'] as String);
        }
        return _extractReplyFromJson(first);
      }
    }
    return null;
  }

  Future<void> _ensureSessionId() async {
    final prefs = await SharedPreferences.getInstance();
    final existing = prefs.getString('chat_session_id');
    if (existing != null && existing.isNotEmpty) {
      setState(() => _sessionId = existing);
      return;
    }
    final newId = _generateSessionId();
    await prefs.setString('chat_session_id', newId);
    setState(() => _sessionId = newId);
  }

  void _persistSessionId(String sessionId) async {
    _sessionId = sessionId;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('chat_session_id', sessionId);
  }

  String _generateSessionId() {
    final userId = Supabase.instance.client.auth.currentUser?.id ?? 'anon';
    final millis = DateTime.now().millisecondsSinceEpoch;
    return '${userId}_$millis';
  }
}

class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
  });
}