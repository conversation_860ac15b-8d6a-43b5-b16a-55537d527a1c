// import moved below to avoid duplicate
import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/screens/workout/workout_screen.dart';
import 'package:openfitv4/widgets/custom_buttons.dart';
import 'package:openfitv4/models/workout_data.dart';
import 'package:openfitv4/screens/workout/workout_detail_screen.dart';
import 'package:openfitv4/screens/workout/pre_workout_overview_screen.dart';
// Replaced legacy BottomNavBar with CurvedBottomNav
import 'package:openfitv4/widgets/curved_bottom_nav.dart';
import 'package:openfitv4/services/workout_service.dart';
import 'package:openfitv4/widgets/theme_selector.dart';
import 'package:openfitv4/screens/onboarding/onboarding_screen_v2.dart';
// Provide a local fallback ChatScreen if the improved one isn't present
import 'package:provider/provider.dart';
// Use WorkoutProvider / WorkoutService instead of missing repository
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:async';
import 'dart:developer' as developer;
import 'package:openfitv4/providers/auth_provider.dart';
import 'package:openfitv4/screens/chat_screen.dart';
import 'package:openfitv4/screens/workout_list_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with SingleTickerProviderStateMixin {
  int _selectedIndex = 0;
  Workout? _nextWorkout; // Loaded from Supabase (latest)
  late AnimationController _fabAnimationController;
  late Animation<double> _fabScaleAnimation;
  StreamSubscription<AuthState>? _authSub;
  bool _isGeneratingNext = false;
  
  @override
  void initState() {
    super.initState();
    developer.log('HomeScreen: initState called', name: 'HomeScreen');
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fabScaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _fabAnimationController,
        curve: Curves.elasticOut,
      ),
    );
    _fabAnimationController.forward();

    // Load latest workout
    _loadLatestWorkout();

    // Reload when auth state changes (session restored or new sign in)
    _authSub = Supabase.instance.client.auth.onAuthStateChange.listen((_) {
      _loadLatestWorkout();
    });
  }
  
  @override
  void dispose() {
    _authSub?.cancel();
    _fabAnimationController.dispose();
    super.dispose();
  }

  Future<void> _loadLatestWorkout() async {
    try {
      final userId = Supabase.instance.client.auth.currentUser?.id;
      developer.log('Loading workout for user: $userId', name: 'HomeScreen');
      
      Workout? latestWorkout;
      if (userId != null) {
        final service = WorkoutService(Supabase.instance.client);
        
        // First try to get active workout
        developer.log('Trying to get active workout...', name: 'HomeScreen');
        latestWorkout = await service.getActiveWorkout(userId);
        
        // If no active workout found, get the most recent workout
        if (latestWorkout == null) {
          developer.log('No active workout found, getting all user workouts...', name: 'HomeScreen');
          final allWorkouts = await service.getUserWorkouts(userId);
          developer.log('Found ${allWorkouts.length} total workouts', name: 'HomeScreen');
          
          if (allWorkouts.isNotEmpty) {
            // Print details of all workouts for debugging
            for (int i = 0; i < allWorkouts.length && i < 5; i++) {
              final w = allWorkouts[i];
              developer.log('Workout $i: ${w.name} (${w.id}) - scheduled: ${w.scheduledDate}', name: 'HomeScreen');
            }
            
            latestWorkout = allWorkouts.first; // getUserWorkouts returns ordered by created_at DESC
            developer.log('Selected latest workout: ${latestWorkout.name} (${latestWorkout.id})', name: 'HomeScreen');
          } else {
            developer.log('No workouts found in workouts table', name: 'HomeScreen');
            
            // FALLBACK: Check workout_logs table for recent activity
            developer.log('Checking workout_logs table as fallback...', name: 'HomeScreen');
          }
        } else {
          developer.log('Found active workout: ${latestWorkout.name} (${latestWorkout.id})', name: 'HomeScreen');
        }
      } else {
        developer.log('No user ID found', name: 'HomeScreen');
      }
      
      if (!mounted) return;
      setState(() {
        _nextWorkout = latestWorkout;
      });
      developer.log('Updated _nextWorkout: ${_nextWorkout?.name ?? "null"}', name: 'HomeScreen');
    } catch (e) {
      developer.log('Error loading latest workout: $e', name: 'HomeScreen', error: e);
      // Keep null to show empty-state fallback
    }
  }

  Future<List<Workout>> _loadAllWorkoutsForUser() async {
    try {
      final userId = Supabase.instance.client.auth.currentUser?.id;
      if (userId == null) return [];
      final service = WorkoutService(Supabase.instance.client);
      return await service.getUserWorkouts(userId);
    } catch (_) {
      return [];
    }
  }
  
  // Temporary workout creation while n8n workflow is being fixed
  Future<void> _createTemporaryWorkout(String userId, String action) async {
    try {
      final client = Supabase.instance.client;
      
      // Create a new workout with basic structure
      final workoutNames = [
        'Full Body Power',
        'Upper Body Strength',
        'Lower Body Focus',
        'Core & Cardio',
        'Push Day Workout',
        'Pull Day Workout',
        'HIIT Circuit',
        'Strength & Endurance'
      ];
      
      final randomIndex = DateTime.now().millisecond % workoutNames.length;
      final workoutName = workoutNames[randomIndex];
      
      final workoutData = {
        'user_id': userId,
        'name': workoutName,
        'is_active': true,
        'ai_description': 'AI-generated workout focusing on strength and endurance',
        'duration': 45,
      };
      
      developer.log('Creating temporary workout: $workoutName', name: 'HomeScreen');
      
      final workoutResponse = await client
          .from('workouts')
          .insert(workoutData)
          .select()
          .single();
      
      final workoutId = workoutResponse['id'];
      
      // Add some basic exercises with more varied workouts based on the workout name
      List<Map<String, dynamic>> exercises;
      
      if (workoutName.contains('Upper Body')) {
        exercises = [
          {'name': 'Push-ups', 'sets': 3, 'reps': [12, 10, 8], 'weight': [0, 0, 0], 'rest_interval': 60, 'order_index': 0},
          {'name': 'Dumbbell Rows', 'sets': 3, 'reps': [12, 10, 8], 'weight': [20, 20, 20], 'rest_interval': 60, 'order_index': 1},
          {'name': 'Shoulder Press', 'sets': 3, 'reps': [10, 8, 6], 'weight': [15, 15, 15], 'rest_interval': 90, 'order_index': 2},
          {'name': 'Bicep Curls', 'sets': 3, 'reps': [15, 12, 10], 'weight': [10, 10, 10], 'rest_interval': 45, 'order_index': 3},
        ];
      } else if (workoutName.contains('Lower Body')) {
        exercises = [
          {'name': 'Squats', 'sets': 4, 'reps': [15, 12, 10, 8], 'weight': [0, 0, 0, 0], 'rest_interval': 90, 'order_index': 0},
          {'name': 'Lunges', 'sets': 3, 'reps': [12, 10, 8], 'weight': [0, 0, 0], 'rest_interval': 60, 'order_index': 1},
          {'name': 'Deadlifts', 'sets': 3, 'reps': [10, 8, 6], 'weight': [40, 45, 50], 'rest_interval': 120, 'order_index': 2},
          {'name': 'Calf Raises', 'sets': 3, 'reps': [20, 18, 15], 'weight': [0, 0, 0], 'rest_interval': 45, 'order_index': 3},
        ];
      } else if (workoutName.contains('Core')) {
        exercises = [
          {'name': 'Plank', 'sets': 3, 'reps': [60, 45, 30], 'weight': [0, 0, 0], 'rest_interval': 45, 'order_index': 0},
          {'name': 'Russian Twists', 'sets': 3, 'reps': [20, 18, 15], 'weight': [10, 10, 10], 'rest_interval': 45, 'order_index': 1},
          {'name': 'Bicycle Crunches', 'sets': 3, 'reps': [25, 20, 15], 'weight': [0, 0, 0], 'rest_interval': 30, 'order_index': 2},
          {'name': 'Mountain Climbers', 'sets': 3, 'reps': [30, 25, 20], 'weight': [0, 0, 0], 'rest_interval': 60, 'order_index': 3},
        ];
      } else if (workoutName.contains('HIIT')) {
        exercises = [
          {'name': 'Burpees', 'sets': 4, 'reps': [10, 8, 6, 5], 'weight': [0, 0, 0, 0], 'rest_interval': 30, 'order_index': 0},
          {'name': 'Jump Squats', 'sets': 3, 'reps': [15, 12, 10], 'weight': [0, 0, 0], 'rest_interval': 30, 'order_index': 1},
          {'name': 'High Knees', 'sets': 3, 'reps': [30, 25, 20], 'weight': [0, 0, 0], 'rest_interval': 30, 'order_index': 2},
          {'name': 'Box Jumps', 'sets': 3, 'reps': [10, 8, 6], 'weight': [0, 0, 0], 'rest_interval': 45, 'order_index': 3},
        ];
      } else {
        // Default full body workout
        exercises = [
          {'name': 'Push-ups', 'sets': 3, 'reps': [12, 10, 8], 'weight': [0, 0, 0], 'rest_interval': 60, 'order_index': 0},
          {'name': 'Squats', 'sets': 3, 'reps': [15, 12, 10], 'weight': [0, 0, 0], 'rest_interval': 60, 'order_index': 1},
          {'name': 'Plank', 'sets': 3, 'reps': [45, 40, 35], 'weight': [0, 0, 0], 'rest_interval': 45, 'order_index': 2},
          {'name': 'Burpees', 'sets': 3, 'reps': [8, 6, 5], 'weight': [0, 0, 0], 'rest_interval': 90, 'order_index': 3},
        ];
      }
      
      for (final exercise in exercises) {
        try {
          // Convert arrays to proper format for PostgreSQL (bigint arrays)
          final repsArray = (exercise['reps'] as List<dynamic>).map((r) => r as int).toList();
          final weightArray = (exercise['weight'] as List<dynamic>).map((w) => (w as double).toInt()).toList();
          
          await client
              .from('workout_exercises')
              .insert({
                'workout_id': workoutId,
                'name': exercise['name'],
                'sets': exercise['sets'],
                'reps': repsArray,
                'weight': weightArray,
                'rest_interval': exercise['rest_interval'],
                'order_index': exercise['order_index'],
                // Generate a random exercise_id from the exercises table
                'exercise_id': await _getRandomExerciseId(client, exercise['name'] as String),
              });
        } catch (e) {
          developer.log('Error adding exercise ${exercise['name']}: $e', name: 'HomeScreen');
          // Continue with other exercises even if one fails
        }
      }
      
      developer.log('Temporary workout created successfully: $workoutId', name: 'HomeScreen');
      
    } catch (e) {
      developer.log('Error creating temporary workout: $e', name: 'HomeScreen', error: e);
      rethrow;
    }
  }

  // Helper method to get a random exercise ID from the database
  Future<String> _getRandomExerciseId(SupabaseClient client, String exerciseName) async {
    try {
      // First try to find an exercise with the exact name
      final exactMatch = await client
          .from('exercises')
          .select('id')
          .ilike('name', exerciseName)
          .limit(1);
      
      if (exactMatch.isNotEmpty) {
        return exactMatch.first['id'] as String;
      }
      
      // If no exact match, get a random exercise
      final randomExercise = await client
          .from('exercises')
          .select('id')
          .limit(1);
      
      if (randomExercise.isNotEmpty) {
        return randomExercise.first['id'] as String;
      }
      
      // Fallback: create a UUID (this shouldn't happen if exercises table has data)
      return 'fallback-${DateTime.now().millisecondsSinceEpoch}';
    } catch (e) {
      developer.log('Error getting exercise ID for $exerciseName: $e', name: 'HomeScreen');
      return 'fallback-${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  // Actions below Next workout
  Future<void> _onSkipNextWorkout() async {
    developer.log('Skip: Function called', name: 'HomeScreen');
    developer.log('Skip: _isGeneratingNext = $_isGeneratingNext', name: 'HomeScreen');
    
    if (_isGeneratingNext) {
      developer.log('Skip: Already generating, returning', name: 'HomeScreen');
      return;
    }
    
    final w = _nextWorkout; // may be null; generation can still proceed
    // Try different webhook URL formats - n8n webhooks can have various paths
    // First try the original URL that was working before
    final url = Uri.parse('https://sciwell.app.n8n.cloud/webhook/f468ffaf-259b-439d-acee-23906b9716eb');
    final userId = Supabase.instance.client.auth.currentUser?.id;

    developer.log('Skip: User ID: $userId', name: 'HomeScreen');
    developer.log('Skip: Current workout: ${w?.name ?? "null"}', name: 'HomeScreen');

    if (userId == null) {
      developer.log('Skip: No user ID, showing sign in message', name: 'HomeScreen');
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please sign in to generate your next workout.')),
      );
      return;
    }

    setState(() => _isGeneratingNext = true);
    final previousId = _nextWorkout?.id;
    
    developer.log('Skip: Starting temporary workout creation', name: 'HomeScreen');
    // Create a workout directly in database (n8n workflow simplified)
    try {
      await _createTemporaryWorkout(userId, 'skip');
      developer.log('Skip: Temporary workout created successfully', name: 'HomeScreen');
    } catch (e) {
      developer.log('Error creating temporary workout: $e', name: 'HomeScreen', error: e);
    }

    // Build the JSON payload in the exact format specified by the user
    final nowIso = DateTime.now().toUtc().toIso8601String();
    
    // Build planned workout exercises - using example data if no workout exists
    final plannedExercises = <Map<String, dynamic>>[];
    if (w != null && w.exercises.isNotEmpty) {
      for (int i = 0; i < w.exercises.length; i++) {
        final workoutExercise = w.exercises[i];
        final exercise = workoutExercise.exercise;
        final sets = workoutExercise.sets;
        
        if (sets.isNotEmpty) {
          plannedExercises.add({
            'order': i + 1,
            'exercise': exercise.name,
            'planned_sets': sets.length,
            'planned_reps': sets.first.reps,
            'planned_weight': sets.first.weight?.toInt() ?? 0,
            'rest_interval': workoutExercise.restTime,
          });
        }
      }
    } else {
      // Use example data when no workout exists
      plannedExercises.addAll([
        {
          'order': 1,
          'exercise': 'Bench Press',
          'planned_sets': 3,
          'planned_reps': 10,
          'planned_weight': 135,
          'rest_interval': 90,
        },
        {
          'order': 2,
          'exercise': 'Squat',
          'planned_sets': 3,
          'planned_reps': 8,
          'planned_weight': 185,
          'rest_interval': 120,
        },
      ]);
    }
    
    // Build actual workout exercises with sample skip data
    final actualExercises = <Map<String, dynamic>>[];
    if (plannedExercises.isNotEmpty) {
      for (final plannedExercise in plannedExercises) {
        actualExercises.add({
          'exercise': plannedExercise['exercise'],
          'actual_sets': [
            {
              'set_order': 1,
              'performed_reps': plannedExercise['planned_reps'],
              'performed_weight': plannedExercise['planned_weight'],
              'rep_difference': 0,
              'set_feedback_difficulty': 'skipped',
            },
          ],
        });
      }
    }
    
    final payloadItem = <String, dynamic>{
      'user_id': userId,
      'workout_id': w?.id ?? '77b64214-a89c-4c46-a053-ce6d392ba9c5',
      'workout_name': w?.name ?? 'Full Body Strength Training',
      'workout_date': nowIso,
      'planned_workout': {
        'workout_name': w?.name ?? 'Full Body Strength Training',
        'exercises': plannedExercises,
      },
      'actual_workout': {
        'workout_name': w?.name ?? 'Full Body Strength Training',
        'exercises': actualExercises,
      },
      'feedback': 'Workout was skipped - generating next optimized workout',
      'additional_metrics': {
        'duration': 0,
        'calories_burned': 0,
      },
    };

    try {
      developer.log('Skip: Sending request to: $url', name: 'HomeScreen');
      developer.log('Skip: Payload: ${jsonEncode([payloadItem])}', name: 'HomeScreen');
      
      final resp = await http.post(
        url,
        headers: const {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode([payloadItem]),
      );
      
      developer.log('Skip: Response status: ${resp.statusCode}', name: 'HomeScreen');
      developer.log('Skip: Response body: ${resp.body}', name: 'HomeScreen');
      
      if (!mounted) return;
      if (resp.statusCode >= 200 && resp.statusCode < 300) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Generating your next workout...'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
        
        // Enhanced polling with better feedback
        bool foundNewWorkout = false;
        for (int i = 0; i < 10; i++) {
          await Future.delayed(const Duration(seconds: 2));
          developer.log('Skip: Polling attempt ${i + 1}/10 for new workout...', name: 'HomeScreen');
          await _loadLatestWorkout();
          
          // Check if we got a new workout (different ID or if we had no workout before)
          final hasNewWorkout = _nextWorkout != null && 
              (_nextWorkout!.id != previousId || previousId == null);
          
          if (hasNewWorkout) {
            foundNewWorkout = true;
            developer.log('Skip: Found new workout: ${_nextWorkout!.name} (${_nextWorkout!.id})', name: 'HomeScreen');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('New workout "${_nextWorkout!.name}" is ready!'),
                  backgroundColor: Colors.green,
                  duration: const Duration(seconds: 3),
                ),
              );
            }
            break;
          }
          developer.log('Skip: No new workout found yet, continuing to poll...', name: 'HomeScreen');
        }
        
        if (!foundNewWorkout && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Workout generation is taking longer than expected. Please check back in a moment.'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 4),
            ),
          );
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Skip failed (${resp.statusCode})'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Network error: $e'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    } finally {
      if (mounted) setState(() => _isGeneratingNext = false);
    }
  }

  // Removed older Replace action per new UX

  Future<void> _onRegenerateNextWorkout() async {
    if (_isGeneratingNext) return;
    final w = _nextWorkout; // may be null
    final url = Uri.parse('https://sciwell.app.n8n.cloud/webhook/f468ffaf-259b-439d-acee-23906b9716eb');
    final userId = Supabase.instance.client.auth.currentUser?.id;
    if (userId == null) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please sign in to generate your next workout.')),
      );
      return;
    }

    setState(() => _isGeneratingNext = true);
    final previousId = _nextWorkout?.id;
    
    // Create a workout directly in database (n8n workflow simplified)
    try {
      await _createTemporaryWorkout(userId, 'regenerate');
    } catch (e) {
      developer.log('Error creating temporary workout: $e', name: 'HomeScreen', error: e);
    }
    final nowIso = DateTime.now().toUtc().toIso8601String();
    final payloadItem = <String, dynamic>{
      'user_id': userId,
      'action': 'regenerate_next_workout',
      if (w != null) 'skip_current_workout_id': w.id,
      'workout_id': w?.id ?? 'regen-no-workout',
      'workout_name': w?.name ?? 'Regenerate Trigger',
      'workout_date': nowIso,
      'planned_workout': {
        'workout_name': w?.name ?? 'Regenerate Trigger',
        'exercises': const <dynamic>[],
      },
      'actual_workout': {
        'workout_name': w?.name ?? 'Regenerate Trigger',
        'exercises': const <dynamic>[],
      },
      'feedback': null,
      'additional_metrics': const {
        'duration': 0,
        'calories_burned': 0,
      },
    };

    try {
      final resp = await http.post(
        url,
        headers: const {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode([payloadItem]),
      );
      if (!mounted) return;
      if (resp.statusCode >= 200 && resp.statusCode < 300) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Regenerating your workout...'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
        
        // Enhanced polling with better feedback
        bool foundNewWorkout = false;
        for (int i = 0; i < 10; i++) {
          await Future.delayed(const Duration(seconds: 2));
          developer.log('Regenerate: Polling attempt ${i + 1}/10 for new workout...', name: 'HomeScreen');
          await _loadLatestWorkout();
          
          // Check if we got a new workout (different ID or if we had no workout before)
          final hasNewWorkout = _nextWorkout != null && 
              (_nextWorkout!.id != previousId || previousId == null);
          
          if (hasNewWorkout) {
            foundNewWorkout = true;
            developer.log('Regenerate: Found new workout: ${_nextWorkout!.name} (${_nextWorkout!.id})', name: 'HomeScreen');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('New workout "${_nextWorkout!.name}" is ready!'),
                  backgroundColor: Colors.green,
                  duration: const Duration(seconds: 3),
                ),
              );
            }
            break;
          }
          developer.log('Regenerate: No new workout found yet, continuing to poll...', name: 'HomeScreen');
        }
        
        if (!foundNewWorkout && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Workout generation is taking longer than expected. Please check back in a moment.'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 4),
            ),
          );
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Regenerate failed (${resp.statusCode})'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Network error: $e'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    } finally {
      if (mounted) setState(() => _isGeneratingNext = false);
    }
  }

  // Removed older Delete action per new UX

  @override
  Widget build(BuildContext context) {
    developer.log('HomeScreen: build called, _isGeneratingNext = $_isGeneratingNext', name: 'HomeScreen');
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: IndexedStack(
          index: _selectedIndex,
          children: [
            // Dashboard
            _buildDashboard(context),
            // Workout
            _buildWorkoutTab(context),
            // Profile
            _buildProfile(context),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      floatingActionButton: ScaleTransition(
        scale: _fabScaleAnimation,
        child: FloatingActionButton(
          onPressed: _openChatPopup,
          backgroundColor: theme.colorScheme.primary,
          elevation: 6,
          child: Icon(
            Icons.chat_bubble_outline,
            color: theme.colorScheme.onPrimary,
          ),
        ),
      ),
      bottomNavigationBar: CurvedBottomNav(
        currentIndex: _selectedIndex,
        onTap: (i) {
          setState(() {
            if (i == 2) {
              // Center orb tapped - quick start workout
              _quickStartWorkout();
            } else {
              // Map: left=Dashboard(0), right=Profile(2)
              _selectedIndex = (i == 0) ? 0 : 2;
              _fabAnimationController
                ..reset()
                ..forward();
            }
          });
        },
      ),
    );
  }
  
  void _quickStartWorkout() {
    // Navigate to workout list screen for testing
    Navigator.push(
      context,
      MaterialPageRoute(builder: (_) => const WorkoutListScreen()),
    );
  }

  void _openChatPopup() {
    final theme = Theme.of(context);
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return FractionallySizedBox(
          heightFactor: 0.92,
          child: Container(
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.25),
                  blurRadius: 24,
                  offset: const Offset(0, -8),
                ),
              ],
            ),
            child: Column(
              children: [
                // Header with drag handle and close button
                Padding(
                  padding: const EdgeInsets.only(top: 8, left: 16, right: 8, bottom: 4),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            Container(
                              width: 40,
                              height: 4,
                              decoration: BoxDecoration(
                                color: theme.colorScheme.outline.withValues(alpha: 0.3),
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                'OpenFit AI Coach',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                      fontWeight: FontWeight.w700,
                                    ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        tooltip: 'Close',
                        onPressed: () => Navigator.of(context).pop(),
                        icon: Icon(Icons.close, color: theme.colorScheme.onSurface.withValues(alpha: 0.7)),
                      ),
                    ],
                  ),
                ),
                const Divider(height: 1),
                 const Expanded(child: ChatScreenImproved()),
              ],
            ),
          ),
        );
      },
    );
  }
  
  // legacy bottom bar icon builder removed (icons provided by CurvedBottomNav)
  
  // replaced by non-interactive center orb used in CurvedBottomNav
  
  Widget _buildDashboard(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.sm),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome back!',
                      style: textTheme.bodyLarge?.copyWith(
                        color: AppColors.grayLight,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Let\'s crush your goals',
                      style: textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                  borderRadius: BorderRadius.circular(AppBorderRadius.md),
                ),
                child: Icon(
                  Icons.notifications_none,
                  color: theme.colorScheme.onPrimary,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          // Today's Stats Card
          Container(
            padding: const EdgeInsets.all(AppSpacing.sm),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  theme.colorScheme.primary,
                  theme.colorScheme.primary.withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(AppBorderRadius.sm),
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.primary.withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'Today\'s Progress',
                  style: textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: AppSpacing.sm),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildStatItem(
                      context,
                      icon: Icons.local_fire_department,
                      value: '1,850',
                      label: 'Calories',
                      onPrimary: true,
                    ),
                    Container(
                      width: 1,
                      height: 50,
                      color: theme.colorScheme.onPrimary.withValues(alpha: 0.2),
                    ),
                    _buildStatItem(
                      context,
                      icon: Icons.directions_walk,
                      value: '8,432',
                      label: 'Steps',
                      onPrimary: true,
                    ),
                    Container(
                      width: 1,
                      height: 50,
                      color: theme.colorScheme.onPrimary.withValues(alpha: 0.2),
                    ),
                    _buildStatItem(
                      context,
                      icon: Icons.timer,
                      value: '45',
                      label: 'Minutes',
                      onPrimary: true,
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppSpacing.md),

          // Next Workout (Latest created)
          Text(
            'Next workout',
            style: textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            _nextWorkout == null
                ? 'Create a workout to get started'
                : 'Your next workout is optimized and ready',
            style: textTheme.bodyMedium?.copyWith(
              color: AppColors.grayLight,
            ),
          ),
          const SizedBox(height: AppSpacing.md),

          if (_nextWorkout != null)
          Container(
            decoration: BoxDecoration(
              color: AppColors.surfaceDark,
              borderRadius: BorderRadius.circular(AppBorderRadius.md),
              border: Border.all(
                color: AppColors.grayDark.withValues(alpha: 0.3),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(AppSpacing.md),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header row with icon + title
                  Row(
                    children: [
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Theme.of(context).colorScheme.primary.withValues(alpha: 0.9),
                              Theme.of(context).colorScheme.primary,
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                        ),
                        child: const Icon(
                          Icons.fitness_center,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: AppSpacing.md),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _nextWorkout!.name,
                              style: textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              _nextWorkout!.category,
                              style: textTheme.bodyMedium?.copyWith(
                                color: AppColors.grayLight,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppSpacing.md),

                  // Stats row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _nextStat(context, Icons.access_time, 'Duration', '${_nextWorkout!.duration} min'),
                      Container(
                        width: 1,
                        height: 45,
                        color: AppColors.grayDark.withValues(alpha: 0.3),
                      ),
                      _nextStat(context, Icons.local_fire_department, 'Calories', '${_nextWorkout!.caloriesBurn} kcal'),
                      Container(
                        width: 1,
                        height: 45,
                        color: AppColors.grayDark.withValues(alpha: 0.3),
                      ),
                      _nextStat(
                        context,
                        Icons.repeat,
                        'Sets',
                        _nextWorkout!.exercises.isNotEmpty
                            ? '${_nextWorkout!.exercises.first.sets.length} × ${_nextWorkout!.exercises.first.sets.first.reps}'
                            : '0 × 0',
                      ),
                    ],
                  ),

                  const SizedBox(height: AppSpacing.md),

                  // Focus chips
                  Container(
                    padding: const EdgeInsets.all(AppSpacing.sm),
                    decoration: BoxDecoration(
                      color: AppColors.backgroundDark.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                      border: Border.all(
                        color: AppColors.grayDark.withValues(alpha: 0.2),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Today's Focus",
                          style: textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: AppSpacing.xs),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: _buildFocusChips(context).take(3).toList(),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: AppSpacing.md),

                  // CTAs
                  CustomElevatedButton(
                    onPressed: () {
                      // Go to the workout overview screen first
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (_) => PreWorkoutOverviewScreen(workout: _nextWorkout!)),
                      );
                    },
                    text: 'Start Workout',
                    icon: const Icon(Icons.play_arrow, size: 20),
                    borderRadius: 20,
                    height: 48,
                  ),
                  const SizedBox(height: AppSpacing.xs),
                  CustomSecondaryButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (_) => WorkoutDetailScreen(workout: _nextWorkout!)),
                      );
                    },
                    text: 'View Details',
                    borderRadius: 20,
                    height: 48,
                  ),
                  
                  const SizedBox(height: AppSpacing.md),
                  
                  // Skip and Regenerate actions moved below workout
                  if (_isGeneratingNext)
                    GestureDetector(
                      onTap: () {
                        developer.log('Skip: Manual reset of _isGeneratingNext', name: 'HomeScreen');
                        setState(() => _isGeneratingNext = false);
                      },
                      child: Container(
                        padding: const EdgeInsets.all(AppSpacing.md),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                          border: Border.all(
                            color: theme.colorScheme.primary.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                              ),
                            ),
                            const SizedBox(width: AppSpacing.sm),
                            Expanded(
                              child: Text(
                                'Generating your next workout... (Tap to cancel)',
                                style: textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.primary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  else
                    Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: () {
                                  developer.log('Skip button pressed!', name: 'HomeScreen');
                                  _onSkipNextWorkout();
                                },
                                icon: const Icon(Icons.skip_next, size: 18),
                                label: const Text('Skip'),
                                style: OutlinedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                  foregroundColor: AppColors.grayLight,
                                  side: BorderSide(
                                    color: AppColors.grayLight.withValues(alpha: 0.3),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: AppSpacing.xs),
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: _onRegenerateNextWorkout,
                                icon: const Icon(Icons.refresh, size: 18),
                                label: const Text('Regenerate'),
                                style: OutlinedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                  foregroundColor: theme.colorScheme.primary,
                                  side: BorderSide(
                                    color: theme.colorScheme.primary.withValues(alpha: 0.3),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                ],
              ),
            ),
          ),

          const SizedBox(height: AppSpacing.md),

          // Quick Actions
          Text(
            'Quick Actions',
            style: textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            mainAxisSpacing: AppSpacing.xs,
            crossAxisSpacing: AppSpacing.xs,
            childAspectRatio: 1.5,
            children: [
              _buildQuickAction(
                context,
                icon: Icons.play_arrow,
                title: 'Start Workout',
                subtitle: 'Chest & Triceps',
                color: const Color(0xFF3B82F6),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (_) => const WorkoutScreen()),
                  );
                },
              ),
              _buildQuickAction(
                context,
                icon: Icons.restaurant,
                title: 'Log Meal',
                subtitle: 'Track nutrition',
                color: const Color(0xFF10B981),
              ),
              _buildQuickAction(
                context,
                icon: Icons.water_drop,
                title: 'Hydration',
                subtitle: '5/8 glasses',
                color: const Color(0xFF06B6D4),
              ),
              _buildQuickAction(
                context,
                icon: Icons.bedtime,
                title: 'Sleep',
                subtitle: '7.5 hours',
                color: const Color(0xFF8B5CF6),
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          // Recent Workouts
          Text(
            'Recent Workouts',
            style: textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          
          _buildWorkoutItem(context, 'Upper Body Strength', 'Yesterday', '45 min'),
          _buildWorkoutItem(context, 'HIIT Cardio', '2 days ago', '30 min'),
          _buildWorkoutItem(context, 'Lower Body Power', '3 days ago', '50 min'),
        ],
      ),
    );
  }
  
  Widget _buildStatItem(
    BuildContext context, {
    required IconData icon,
    required String value,
    required String label,
    bool onPrimary = false,
  }) {
    final theme = Theme.of(context);
    final color = onPrimary ? theme.colorScheme.onPrimary : theme.colorScheme.onSurface;
    
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          icon,
          color: color.withValues(alpha: 0.9),
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: theme.textTheme.titleLarge?.copyWith(
            color: color,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: color.withValues(alpha: 0.8),
            fontSize: 14,
          ),
        ),
      ],
    );
  }
  
  Widget _buildQuickAction(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    VoidCallback? onTap,
  }) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.sm),
        decoration: BoxDecoration(
          color: AppColors.surfaceDark,
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 22),
            const SizedBox(height: AppSpacing.xs),
            Text(
              title,
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              subtitle,
              style: theme.textTheme.bodySmall?.copyWith(
                color: AppColors.grayLight,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // Helpers for Next Workout card
  Widget _nextStat(BuildContext context, IconData icon, String label, String value) {
    final theme = Theme.of(context);
    return Column(
      children: [
        Icon(icon, size: 20, color: AppColors.grayLight),
        const SizedBox(height: 8),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: AppColors.grayLight,
          ),
        ),
      ],
    );
  }

  List<Widget> _buildFocusChips(BuildContext context) {
    // Derive focus topics from workout muscle groups or equipment, falling back to 3 items.
    final chips = <String>[];
    final w = _nextWorkout;
    if (w != null && w.muscleGroups.isNotEmpty) {
      chips.addAll(w.muscleGroups.take(3));
    } else if (w != null && w.equipment.isNotEmpty) {
      chips.addAll(w.equipment.take(3));
    } else if (w != null && w.exercises.isNotEmpty) {
      chips.addAll(w.exercises.take(3).map((e) => e.exercise.muscle));
    } else {
      chips.addAll(const ['Strength', 'Form', 'Tempo']);
    }
    return chips.map((t) => _focusChip(context, t)).toList();
  }

  Widget _focusChip(BuildContext context, String text) {
    final theme = Theme.of(context);
    return Expanded(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: AppColors.surfaceDark,
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: AppColors.grayDark.withValues(alpha: 0.3),
          ),
        ),
        child: Text(
          text,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  Widget _buildWorkoutItem(BuildContext context, String title, String date, String duration) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.xs),
      padding: const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: AppColors.surfaceDark,
        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppBorderRadius.xs),
            ),
            child: Icon(
              Icons.fitness_center,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  date,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: AppColors.grayLight,
                  ),
                ),
              ],
            ),
          ),
          Text(
            duration,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
  
  
  Widget _buildWorkoutTab(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    
    return FutureBuilder<List<Workout>>(
      future: _loadAllWorkoutsForUser(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final workouts = snapshot.data ?? [];
        final recentWorkouts = workouts.take(3).toList();
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpacing.sm),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: AppSpacing.xs),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Workouts',
                      style: textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.search, color: theme.colorScheme.primary),
                      onPressed: () => Navigator.of(context).pushNamed('/workouts'),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: AppSpacing.md),
              
              // Quick Actions
              Row(
                children: [
                  Expanded(
                    child: _buildActionCard(
                      context,
                      icon: Icons.explore,
                      title: 'Browse',
                      subtitle: 'Explore workouts',
                      color: theme.colorScheme.primary,
                      onTap: () => Navigator.of(context).pushNamed('/workouts'),
                    ),
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Expanded(
                    child: _buildActionCard(
                      context,
                      icon: Icons.add_circle_outline,
                      title: 'Create',
                      subtitle: 'Custom workout',
                      color: Colors.green,
                      onTap: () => Navigator.of(context).pushNamed('/create-workout'),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppSpacing.lg),
              
              // Recent Workouts Section
              if (recentWorkouts.isNotEmpty) ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: AppSpacing.xs),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Recent Workouts',
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      TextButton(
                        onPressed: () => Navigator.of(context).pushNamed('/workouts'),
                        child: Text(
                          'See All',
                          style: TextStyle(color: theme.colorScheme.primary),
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: AppSpacing.sm),
                
                // Workout Cards
                ...recentWorkouts.map((workout) => _buildWorkoutCard(context, workout)),
              ] else ...[
                // Empty State
                Container(
                  margin: const EdgeInsets.only(top: AppSpacing.xl),
                  padding: const EdgeInsets.all(AppSpacing.xl),
                  decoration: BoxDecoration(
                    color: AppColors.surfaceDark,
                    borderRadius: BorderRadius.circular(AppBorderRadius.lg),
                    border: Border.all(
                      color: theme.colorScheme.outline.withValues(alpha: 0.1),
                    ),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.fitness_center_rounded,
                        size: 48,
                        color: theme.colorScheme.primary.withValues(alpha: 0.5),
                      ),
                      const SizedBox(height: AppSpacing.md),
                      Text(
                        'No Workouts Yet',
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: AppSpacing.xs),
                      Text(
                        'Start by browsing our library or creating your own custom workout',
                        style: textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
              
              const SizedBox(height: AppSpacing.md),
              
              // Categories Section
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: AppSpacing.xs),
                child: Text(
                  'Categories',
                  style: textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              
              const SizedBox(height: AppSpacing.xs),
              
              // Category Grid
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                mainAxisSpacing: AppSpacing.xs,
                crossAxisSpacing: AppSpacing.xs,
                childAspectRatio: 1.5,
                children: [
                  _buildCategoryCard(context, 'Full Body', Icons.accessibility_new, Colors.orange),
                  _buildCategoryCard(context, 'Upper Body', Icons.fitness_center, Colors.blue),
                  _buildCategoryCard(context, 'Lower Body', Icons.directions_run, Colors.green),
                  _buildCategoryCard(context, 'Core', Icons.self_improvement, Colors.purple),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
  
  Widget _buildActionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppBorderRadius.lg),
        child: Container(
          padding: const EdgeInsets.all(AppSpacing.sm),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                color.withValues(alpha: 0.15),
                color.withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
            border: Border.all(
              color: color.withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(height: AppSpacing.xs),
              Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildWorkoutCard(BuildContext context, Workout workout) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.sm),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) => PreWorkoutOverviewScreen(workout: workout),
              ),
            );
          },
          borderRadius: BorderRadius.circular(AppBorderRadius.lg),
          child: Container(
            padding: const EdgeInsets.all(AppSpacing.sm),
            decoration: BoxDecoration(
              color: AppColors.surfaceDark,
              borderRadius: BorderRadius.circular(AppBorderRadius.lg),
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.1),
              ),
            ),
            child: Row(
              children: [
                // Workout Image
                ClipRRect(
                  borderRadius: BorderRadius.circular(AppBorderRadius.md),
                  child: Image.asset(
                    workout.imageUrl.isNotEmpty 
                      ? workout.imageUrl 
                      : 'assets/images/chest_press.png',
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                
                // Workout Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        workout.name,
                        style: textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        workout.category,
                        style: textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.primary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(Icons.timer_outlined, size: 14, color: AppColors.grayLight),
                          const SizedBox(width: 4),
                          Text(
                            '${workout.duration} min',
                            style: textTheme.bodySmall?.copyWith(color: AppColors.grayLight),
                          ),
                          const SizedBox(width: AppSpacing.sm),
                          Icon(Icons.local_fire_department, size: 14, color: AppColors.grayLight),
                          const SizedBox(width: 4),
                          Text(
                            '${workout.caloriesBurn} kcal',
                            style: textTheme.bodySmall?.copyWith(color: AppColors.grayLight),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                // Play Button
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.play_arrow_rounded,
                    color: theme.colorScheme.onPrimary,
                    size: 24,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildCategoryCard(BuildContext context, String title, IconData icon, Color color) {
    final theme = Theme.of(context);
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          Navigator.of(context).pushNamed('/workouts');
        },
        borderRadius: BorderRadius.circular(AppBorderRadius.lg),
        child: Container(
          padding: const EdgeInsets.all(AppSpacing.md),
          decoration: BoxDecoration(
            color: AppColors.surfaceDark,
            borderRadius: BorderRadius.circular(AppBorderRadius.lg),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.1),
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(height: AppSpacing.xs),
              Text(
                title,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildProfile(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final authProvider = Provider.of<AuthProvider>(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.sm),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Enhanced Profile Header Card with gradient
          Container(
            padding: const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  theme.colorScheme.primary.withValues(alpha: 0.15),
                  theme.colorScheme.primary.withValues(alpha: 0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(AppBorderRadius.md),
              border: Border.all(
                color: theme.colorScheme.primary.withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    // Enhanced Avatar with shadow
                    Container(
                      width: 72,
                      height: 72,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          colors: [
                            theme.colorScheme.primary,
                            theme.colorScheme.primary.withValues(alpha: 0.8),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: theme.colorScheme.primary.withValues(alpha: 0.3),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          authProvider.userName.isNotEmpty
                            ? authProvider.userName[0].toUpperCase()
                            : 'U',
                          style: textTheme.headlineMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: AppSpacing.md),
                    // Name & Email with badge
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            authProvider.userName,
                            style: textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            authProvider.userEmail,
                            style: textTheme.bodyMedium?.copyWith(
                              color: AppColors.grayLight,
                            ),
                          ),
                          const SizedBox(height: 6),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppSpacing.xs,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primary.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(AppBorderRadius.xs),
                            ),
                            child: Text(
                              'Premium Member',
                              style: textTheme.labelSmall?.copyWith(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppSpacing.md),
                // Action buttons row
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          // TODO: Navigate to edit profile
                        },
                        icon: const Icon(Icons.edit_outlined, size: 16),
                        label: const Text('Edit Profile'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: theme.colorScheme.primary,
                          side: BorderSide(
                            color: theme.colorScheme.primary.withValues(alpha: 0.3),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 8),
                        ),
                      ),
                    ),
                    const SizedBox(width: AppSpacing.xs),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          // TODO: Share profile
                        },
                        icon: const Icon(Icons.share_outlined, size: 16),
                        label: const Text('Share'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: theme.colorScheme.primary,
                          side: BorderSide(
                            color: theme.colorScheme.primary.withValues(alpha: 0.3),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 8),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: AppSpacing.md),

          // Achievement Stats Card
          Container(
            padding: const EdgeInsets.all(AppSpacing.sm),
            decoration: BoxDecoration(
              color: AppColors.surfaceDark,
              borderRadius: BorderRadius.circular(AppBorderRadius.md),
              border: Border.all(
                color: AppColors.grayDark.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Your Progress',
                  style: textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: AppSpacing.sm),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildAchievementStat(
                      context,
                      icon: Icons.local_fire_department,
                      value: '7',
                      label: 'Day Streak',
                      color: Colors.orange,
                    ),
                    Container(width: 1, height: 40, color: AppColors.grayDark.withValues(alpha: 0.3)),
                    _buildAchievementStat(
                      context,
                      icon: Icons.fitness_center,
                      value: '42',
                      label: 'Workouts',
                      color: theme.colorScheme.primary,
                    ),
                    Container(width: 1, height: 40, color: AppColors.grayDark.withValues(alpha: 0.3)),
                    _buildAchievementStat(
                      context,
                      icon: Icons.timer,
                      value: '28h',
                      label: 'Total Time',
                      color: Colors.blue,
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: AppSpacing.md),

          // Body Metrics Grid
          Text(
            'Body Metrics',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            mainAxisSpacing: AppSpacing.xs,
            crossAxisSpacing: AppSpacing.xs,
            childAspectRatio: 2.5,
            children: [
              _buildMetricCard(context, 'Weight', '72 kg', Icons.monitor_weight_outlined),
              _buildMetricCard(context, 'Height', '178 cm', Icons.height),
              _buildMetricCard(context, 'BMI', '22.7', Icons.speed),
              _buildMetricCard(context, 'Body Fat', '15%', Icons.percent),
            ],
          ),

          const SizedBox(height: AppSpacing.sm),

          // Theme Selector
          _buildSectionHeader(context, 'Appearance'),
          const ThemeSelector(),

          const SizedBox(height: AppSpacing.xs),

          // Sections
          _buildSectionHeader(context, 'Account'),
          _buildProfileOption(context, Icons.person_outline, 'Edit Profile'),
          _buildProfileOption(context, Icons.settings, 'Settings'),
          _buildProfileOption(context, Icons.notifications_none, 'Notifications'),

          const SizedBox(height: AppSpacing.xs),

          _buildSectionHeader(context, 'Privacy & Help'),
          _buildProfileOption(context, Icons.lock_outline, 'Privacy'),
          _buildProfileOption(context, Icons.help_outline, 'Help & Support'),
          _buildProfileOption(context, Icons.info_outline, 'About'),

          const SizedBox(height: AppSpacing.xs),

          _buildSectionHeader(context, 'Developer'),
          Container(
            margin: const EdgeInsets.only(bottom: AppSpacing.xs),
            decoration: BoxDecoration(
              color: AppColors.surfaceDark,
              borderRadius: BorderRadius.circular(AppBorderRadius.sm),
              border: Border.all(
                color: Colors.blue.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: ListTile(
              leading: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.12),
                  borderRadius: BorderRadius.circular(AppBorderRadius.xs),
                ),
                child: Icon(
                  Icons.science,
                  color: Colors.blue,
                  size: 20,
                ),
              ),
              title: Text(
                'Test Onboarding UI',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              subtitle: Text(
                'Preview onboarding screens without signing out',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: AppColors.grayLight,
                ),
              ),
              trailing: Icon(
                Icons.chevron_right,
                color: AppColors.grayMedium,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.sm,
                vertical: AppSpacing.xs,
              ),
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const OnboardingScreenV2(),
                  ),
                );
              },
            ),
          ),
          Container(
            margin: const EdgeInsets.only(bottom: AppSpacing.xs),
            decoration: BoxDecoration(
              color: AppColors.surfaceDark,
              borderRadius: BorderRadius.circular(AppBorderRadius.sm),
              border: Border.all(
                color: theme.colorScheme.primary.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: ListTile(
              leading: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.12),
                  borderRadius: BorderRadius.circular(AppBorderRadius.xs),
                ),
                child: Icon(
                  Icons.restart_alt,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
              ),
              title: Text(
                'Reset Onboarding',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              subtitle: Text(
                'Sign out and restart from login',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: AppColors.grayLight,
                ),
              ),
              trailing: Icon(
                Icons.chevron_right,
                color: AppColors.grayMedium,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.sm,
                vertical: AppSpacing.xs,
              ),
              onTap: () {
                Navigator.of(context).pushNamedAndRemoveUntil(
                  '/welcome',
                  (route) => false,
                );
              },
            ),
          ),

          const SizedBox(height: AppSpacing.md),

          // Sign Out Button
          CustomElevatedButton(
            onPressed: () async {
              await authProvider.signOut();
              if (!context.mounted) return;
              Navigator.of(context).pushNamedAndRemoveUntil(
                '/welcome',
                (route) => false,
              );
            },
            text: 'Sign Out',
            backgroundColor: theme.colorScheme.error.withValues(alpha: 0.15),
            foregroundColor: theme.colorScheme.error,
          ),
        ],
      ),
    );
  }
  
  Widget _buildProfileOption(BuildContext context, IconData icon, String title) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.xs),
      decoration: BoxDecoration(
        color: AppColors.surfaceDark,
        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        border: Border.all(
          color: AppColors.grayDark,
          width: 1,
        ),
      ),
      child: ListTile(
        leading: Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.12),
            borderRadius: BorderRadius.circular(AppBorderRadius.xs),
          ),
          child: Icon(icon, color: theme.colorScheme.primary, size: 20),
        ),
        title: Text(
          title,
          style: theme.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        trailing: Icon(
          Icons.chevron_right,
          color: AppColors.grayMedium,
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.sm,
          vertical: AppSpacing.xs,
        ),
        onTap: () {
          // Handle option tap
        },
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
      child: Text(
        title,
        style: theme.textTheme.titleSmall?.copyWith(
          color: AppColors.grayLight,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildAchievementStat(
    BuildContext context, {
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 24,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w700,
            fontSize: 20,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: AppColors.grayLight,
          ),
        ),
      ],
    );
  }

  Widget _buildMetricCard(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: AppColors.surfaceDark,
        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        border: Border.all(
          color: AppColors.grayDark.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: theme.colorScheme.primary,
            size: 20,
          ),
          const SizedBox(width: AppSpacing.xs),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: AppColors.grayLight,
                  fontSize: 10,
                ),
              ),
              Text(
                value,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
