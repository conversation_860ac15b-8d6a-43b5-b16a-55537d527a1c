import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';

class WelcomeScreen extends StatelessWidget {
  const WelcomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: Stack(
        children: [
          // Background image with yoga woman
          Positioned.fill(
            child: Container(
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage('assets/images/yoga_woman.png'),
                  fit: BoxFit.cover,
                  alignment: Alignment.topCenter,
                ),
              ),
            ),
          ),
          
          // Gradient overlay at bottom
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            height: 480,
            child: Container(
              decoration: const BoxDecoration(
                gradient: AppColors.overlayGradient,
              ),
            ),
          ),
          
          // Status bar (for iOS)
          const Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: <PERSON><PERSON><PERSON>(
              bottom: false,
              child: Sized<PERSON>ox(height: 44),
            ),
          ),
          
          // Content
          Positioned(
            left: AppSpacing.sm,
            right: AppSpacing.sm,
            bottom: 120,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Health plus icon
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                  ),
                  child: Icon(
                    Icons.add,
                    color: theme.colorScheme.onSurface,
                    size: 24,
                  ),
                ),
                
                const SizedBox(height: AppSpacing.lg),
                
                // Title
                Text(
                  'Welcome To\nOpenFit!',
                  textAlign: TextAlign.center,
                  style: textTheme.displaySmall?.copyWith(
                    fontSize: 36,
                    height: 1.22,
                  ),
                ),
                
                const SizedBox(height: AppSpacing.sm),
                
                // Subtitle
                Text(
                  'Your personal fitness AI Assistant 🤖',
                  textAlign: TextAlign.center,
                  style: textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.9),
                  ),
                ),
                
                const SizedBox(height: AppSpacing.lg),
                
                // Get Started Button
                SizedBox(
                  width: double.infinity,
                  height: 64,
                  child: ElevatedButton(
                    onPressed: () {
                      // Navigate to sign up
                      Navigator.pushReplacementNamed(context, '/signup');
                    },
                    style: theme.elevatedButtonTheme.style?.copyWith(
                      minimumSize: WidgetStateProperty.all(const Size(double.infinity, 64)),
                      shape: WidgetStateProperty.all(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(21),
                        ),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Get Started',
                          style: textTheme.labelLarge?.copyWith(
                            fontSize: 18,
                          ),
                        ),
                        const SizedBox(width: AppSpacing.sm),
                        const Icon(
                          Icons.arrow_forward,
                          size: 24,
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: AppSpacing.xl),
                
                // Sign In link
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Already have account? ',
                      style: textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        // Navigate to sign in
                        Navigator.pushNamed(context, '/signin');
                      },
                      child: Text(
                        'Sign In',
                        style: textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.primary,
                          decoration: TextDecoration.underline,
                          decorationColor: theme.colorScheme.primary,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Home indicator (iOS)
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: SafeArea(
              top: false,
              child: Center(
                child: Container(
                  margin: const EdgeInsets.only(bottom: AppSpacing.xs),
                  width: 134,
                  height: 5,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(2.5),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}