import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/models/workout_data.dart';
import 'package:openfitv4/screens/workout/workout_complete_screen.dart';
import 'package:openfitv4/screens/workout/widgets/reps_weight_picker.dart';
import 'package:openfitv4/widgets/custom_buttons.dart';

/// Active Workout screen implementing full spec:
/// - System status bar with SafeArea
/// - Top HUD: elapsed timer, completion %, segmented progress with partial fill for current exercise
/// - Exercise header: title + set counter
/// - Player surface (image-based) with gestures:
///   * Tap center -> play/pause (stub)
///   * Tap right half / swipe left -> next set
///   * Tap left half / swipe right -> prev set
///   * Swipe down -> next exercise (skip confirmation if sets remain)
/// - AI Coach avatar (bottom-right) with pulsing animation; tap => tooltip; long-press => mute toggle
/// - Inline metrics: reps (bottom-left) & weight (bottom-right), tap to open wheel picker
/// - Next-exercise strip above bottom safe area; swipe up to jump to that exercise (with skip confirmation)
/// - State persistence across app restarts via SharedPreferences
/// - Portrait-only while this screen is visible
class ActiveWorkoutScreen extends StatefulWidget {
  final Workout workout;

  const ActiveWorkoutScreen({
    super.key,
    required this.workout,
  });

  @override
  State<ActiveWorkoutScreen> createState() => _ActiveWorkoutScreenState();
}

class _ActiveWorkoutScreenState extends State<ActiveWorkoutScreen> with WidgetsBindingObserver, TickerProviderStateMixin {
  // Progress state
  int _currentExerciseIndex = 0;
  int _currentSetIndex = 0;

  // Timer
  Timer? _timer;
  int _timerSeconds = 0;

  // Playback (stub for image player)
  bool _isPlaying = true;

  // Coach voice
  bool _coachMuted = false;

  // Avatar pulse
  late final AnimationController _pulseCtrl;
  late final Animation<double> _pulseTween;

  // Persistence keys
  static const _kKeyWorkoutId = 'active_session_workout_id';
  static const _kKeyExercise = 'active_session_current_exercise';
  static const _kKeySet = 'active_session_current_set';
  static const _kKeyTime = 'active_session_elapsed_secs';
  static const _kKeyMute = 'active_session_mute';

  late List<WorkoutExercise> _exercises;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Portrait lock
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

    _exercises = List.of(widget.workout.exercises);

    // Avatar pulse
    _pulseCtrl = AnimationController(vsync: this, duration: const Duration(seconds: 2))..repeat(reverse: true);
    _pulseTween = Tween<double>(begin: 0.95, end: 1.05).animate(CurvedAnimation(parent: _pulseCtrl, curve: Curves.easeInOut));

    _restoreSession().then((_) {
      // start timer after restore
      _startTimer();
      setState(() {});
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _timer?.cancel();
    _pulseCtrl.dispose();
    // Restore default orientations
    SystemChrome.setPreferredOrientations(DeviceOrientation.values);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused || state == AppLifecycleState.inactive) {
      _saveSession();
    }
  }

  Future<void> _restoreSession() async {
    final prefs = await SharedPreferences.getInstance();
    final savedWorkout = prefs.getString(_kKeyWorkoutId);
    if (savedWorkout == widget.workout.id) {
      _currentExerciseIndex = prefs.getInt(_kKeyExercise) ?? 0;
      _currentSetIndex = prefs.getInt(_kKeySet) ?? 0;
      _timerSeconds = prefs.getInt(_kKeyTime) ?? 0;
      _coachMuted = prefs.getBool(_kKeyMute) ?? false;

      // Bounds safety
      if (_exercises.isEmpty) {
        _currentExerciseIndex = 0;
        _currentSetIndex = 0;
      } else {
        if (_currentExerciseIndex >= _exercises.length) _currentExerciseIndex = 0;
        if (_currentExerciseIndex < _exercises.length && 
            _currentSetIndex >= _exercises[_currentExerciseIndex].sets.length) {
          _currentSetIndex = 0;
        }
      }
    } else {
      // new session for this workout
      await prefs.setString(_kKeyWorkoutId, widget.workout.id);
      await prefs.setInt(_kKeyExercise, 0);
      await prefs.setInt(_kKeySet, 0);
      await prefs.setInt(_kKeyTime, 0);
      await prefs.setBool(_kKeyMute, false);
    }
  }

  Future<void> _saveSession() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_kKeyWorkoutId, widget.workout.id);
    await prefs.setInt(_kKeyExercise, _currentExerciseIndex);
    await prefs.setInt(_kKeySet, _currentSetIndex);
    await prefs.setInt(_kKeyTime, _timerSeconds);
    await prefs.setBool(_kKeyMute, _coachMuted);
  }

  void _startTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      setState(() => _timerSeconds++);
    });
  }

  String _formatTime(int seconds) {
    final m = (seconds ~/ 60).toString().padLeft(2, '0');
    final s = (seconds % 60).toString().padLeft(2, '0');
    return '$m:$s';
  }

  double get _exerciseCompletion {
    // completed exercises / total
    int completedExercises = 0;
    for (int i = 0; i < _exercises.length; i++) {
      final ex = _exercises[i];
      final done = ex.sets.every((s) => s.isCompleted);
      if (done) completedExercises++;
    }
    return _exercises.isEmpty ? 0 : completedExercises / _exercises.length;
  }

  double get _overallPercent => (_exerciseCompletion * 100);

  // Navigation helpers
  bool get _atFirstSet => _currentExerciseIndex == 0 && _currentSetIndex == 0;

  bool get _atLastSetOfExercise {
    if (_exercises.isEmpty || _currentExerciseIndex >= _exercises.length) return true;
    final sets = _exercises[_currentExerciseIndex].sets;
    return sets.isEmpty || _currentSetIndex >= sets.length - 1;
  }

  bool get _atLastExercise => _exercises.isEmpty || _currentExerciseIndex >= _exercises.length - 1;
  
  bool get _isPaused => _timer == null || !_timer!.isActive;

  Future<void> _nextSet({bool skipRest = false}) async {
    if (_exercises.isEmpty || _currentExerciseIndex >= _exercises.length) return;
    
    final ex = _exercises[_currentExerciseIndex];
    if (ex.sets.isEmpty || _currentSetIndex >= ex.sets.length) return;
    
    setState(() {
      ex.sets[_currentSetIndex].isCompleted = true;
    });

    // Show Rest overlay if restTime > 0 and not skipping
    final restSeconds = ex.restTime;
    if (restSeconds > 0 && !skipRest) {
      await _showRestSheet(
        totalSeconds: restSeconds,
        backgroundImage: ex.exercise.imageUrl,
        nextTitle: !_atLastSetOfExercise
            ? '${ex.exercise.name} — Set ${_currentSetIndex + 2}'
            : (!_atLastExercise ? _exercises[_currentExerciseIndex + 1].exercise.name : 'Finish'),
      );
    }

    if (!_atLastSetOfExercise) {
      setState(() {
        _currentSetIndex++;
      });
    } else if (!_atLastExercise) {
      setState(() {
        _currentExerciseIndex++;
        _currentSetIndex = 0;
      });
    } else {
      _completeWorkout();
      return;
    }
    _saveSession();
  }
  
  // Skip current set without marking as complete
  void _skipSet() {
    if (!_atLastSetOfExercise) {
      setState(() {
        _currentSetIndex++;
      });
    } else if (!_atLastExercise) {
      setState(() {
        _currentExerciseIndex++;
        _currentSetIndex = 0;
      });
    } else {
      _completeWorkout();
    }
    _saveSession();
  }

  void _prevSet() {
    if (_atFirstSet || _exercises.isEmpty) return;
    if (_currentSetIndex > 0) {
      setState(() {
        _currentSetIndex--;
      });
    } else {
      setState(() {
        _currentExerciseIndex--;
        if (_currentExerciseIndex < _exercises.length && _exercises[_currentExerciseIndex].sets.isNotEmpty) {
          _currentSetIndex = _exercises[_currentExerciseIndex].sets.length - 1;
        } else {
          _currentSetIndex = 0;
        }
      });
    }
    _saveSession();
  }

  Future<void> _confirmSkipRemainingAndGoNextExercise() async {
    if (_exercises.isEmpty || _currentExerciseIndex >= _exercises.length) return;
    final name = _exercises[_currentExerciseIndex].exercise.name;
    final theme = Theme.of(context);
    final ok = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        backgroundColor: AppColors.surfaceDark,
        title: Text('Skip remaining sets?', style: theme.textTheme.titleMedium),
        content: Text('Skip remaining sets for $name?', style: theme.textTheme.bodyMedium?.copyWith(color: AppColors.grayLight)),
        actions: [
          TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('No')),
          TextButton(onPressed: () => Navigator.pop(ctx, true), child: const Text('Yes')),
        ],
      ),
    );
    if (ok == true) {
      if (!_atLastExercise) {
        setState(() {
          _currentExerciseIndex++;
          _currentSetIndex = 0;
        });
        _saveSession();
      } else {
        _completeWorkout();
      }
    }
  }

  void _completeWorkout() {
    _timer?.cancel();
    _saveSession();
    // Clear session data
    SharedPreferences.getInstance().then((prefs) {
      prefs.remove(_kKeyWorkoutId);
      prefs.remove(_kKeyExercise);
      prefs.remove(_kKeySet);
      prefs.remove(_kKeyTime);
      prefs.remove(_kKeyMute);
    });
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (_) => WorkoutCompleteScreen(
          workout: widget.workout,
          duration: _timerSeconds,
          exercises: _exercises,
        ),
      ),
    );
  }

  Future<void> _confirmEndWorkoutEarly() async {
    final theme = Theme.of(context);
    final ok = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        backgroundColor: AppColors.surfaceDark,
        title: Text('End workout now?', style: theme.textTheme.titleMedium),
        content: Text('This will finalize your session and show the summary.', style: theme.textTheme.bodyMedium?.copyWith(color: AppColors.grayLight)),
        actions: [
          TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () => Navigator.pop(ctx, true),
            style: ElevatedButton.styleFrom(backgroundColor: theme.colorScheme.primary),
            child: const Text('End workout'),
          ),
        ],
      ),
    );
    if (ok == true) {
      _completeWorkout();
    }
  }

  Future<void> _confirmDeleteCurrentExercise() async {
    if (_exercises.isEmpty || _currentExerciseIndex >= _exercises.length) return;
    final theme = Theme.of(context);
    final name = _exercises[_currentExerciseIndex].exercise.name;
    final ok = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        backgroundColor: AppColors.surfaceDark,
        title: Text('Delete exercise?', style: theme.textTheme.titleMedium),
        content: Text('Remove "$name" from this session?', style: theme.textTheme.bodyMedium?.copyWith(color: AppColors.grayLight)),
        actions: [
          TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('Cancel')),
          TextButton(onPressed: () => Navigator.pop(ctx, true), child: const Text('Delete')),
        ],
      ),
    );
    if (ok == true) {
      setState(() {
        _exercises.removeAt(_currentExerciseIndex);
        if (_exercises.isEmpty) {
          // Nothing left; exit to home
          SharedPreferences.getInstance().then((prefs) {
            prefs.remove(_kKeyWorkoutId);
            prefs.remove(_kKeyExercise);
            prefs.remove(_kKeySet);
            prefs.remove(_kKeyTime);
            prefs.remove(_kKeyMute);
            if (mounted) {
              // Fallback to root route since 'home' named route isn't registered
      Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
            }
          });
          return;
        }
        if (_currentExerciseIndex >= _exercises.length) {
          _currentExerciseIndex = _exercises.length - 1;
          _currentSetIndex = 0;
        } else {
          _currentSetIndex = _currentSetIndex.clamp(0, _exercises[_currentExerciseIndex].sets.length - 1);
        }
      });
      await _saveSession();
    }
  }

  Future<void> _exitWorkout() async {
    // Pause timer
    _timer?.cancel();
    
    final theme = Theme.of(context);
    
    final result = await showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.surfaceDark,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Row(
          children: [
            Icon(Icons.warning_amber_rounded, color: theme.colorScheme.primary, size: 28),
            const SizedBox(width: AppSpacing.sm),
            Text('Exit Workout?', style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'What would you like to do?',
              style: theme.textTheme.bodyLarge,
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              '• Save & Exit: Your progress will be saved\n• Exit Without Saving: Progress will be lost\n• Cancel: Continue workout',
              style: theme.textTheme.bodyMedium?.copyWith(color: AppColors.grayLight, height: 1.5),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop('cancel');
            },
            child: Text('Cancel', style: TextStyle(color: AppColors.grayLight)),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop('exit_no_save');
            },
            child: Text('Exit Without Saving', style: TextStyle(color: theme.colorScheme.error)),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop('save_exit');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text('Save & Exit'),
          ),
        ],
      ),
    );
    
    if (result == 'cancel') {
      // Resume timer
      _startTimer();
    } else if (result == 'exit_no_save') {
      // Clear session and exit to home
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_kKeyWorkoutId);
      await prefs.remove(_kKeyExercise);
      await prefs.remove(_kKeySet);
      await prefs.remove(_kKeyTime);
      await prefs.remove(_kKeyMute);
      if (!mounted) return;
      // Fallback to root route since 'home' named route isn't registered
      Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
    } else if (result == 'save_exit') {
      // Save progress and exit to home
      await _saveSession();
      if (!mounted) return;
              // Fallback to root route since 'home' named route isn't registered
              Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
    } else {
      // Resume if dialog dismissed
      _startTimer();
    }
  }
  
  void _pauseResume() {
    setState(() {
      if (_timer != null && _timer!.isActive) {
        _timer!.cancel();
      } else {
        _startTimer();
      }
    });
  }

  // Inline pickers
  Future<void> _openPicker() async {
    if (_exercises.isEmpty || _currentExerciseIndex >= _exercises.length) return;
    final currentExercise = _exercises[_currentExerciseIndex];
    if (currentExercise.sets.isEmpty || _currentSetIndex >= currentExercise.sets.length) return;
    
    final set = currentExercise.sets[_currentSetIndex];
    final initialReps = set.reps;
    final initialWeight = (set.weight ?? 0).toDouble();

    await RepsWeightPicker.show(
      context,
      initialReps: initialReps,
      initialWeight: initialWeight,
      weightUnit: 'lb',
      onConfirm: (reps, weight) {
        // WorkoutSet fields are final in the current model; update is view-only for now.
        setState(() {/* noop: persist when model supports editing */});
      },
    );
  }

  // Gesture handling
  void _onTapDown(TapDownDetails details, Size size) {
    final dx = details.localPosition.dx;
    final centerLeft = size.width / 3;
    final centerRight = size.width * 2 / 3;

    if (dx >= centerLeft && dx <= centerRight) {
      // center -> play/pause stub
      setState(() => _isPlaying = !_isPlaying);
    } else if (dx > centerRight) {
      _nextSet();
    } else {
      _prevSet();
    }
  }

  void _onHorizontalDragEnd(DragEndDetails details) {
    if (details.primaryVelocity == null) return;
    if (details.primaryVelocity! < 0) {
      // swipe left -> next
      _nextSet();
    } else if (details.primaryVelocity! > 0) {
      // swipe right -> prev
      _prevSet();
    }
  }

  void _onVerticalDragEnd(DragEndDetails details) {
    if (details.primaryVelocity == null) return;
    if (details.primaryVelocity! > 300) {
      // swipe down (positive velocity) -> next exercise confirm
      _confirmSkipRemainingAndGoNextExercise();
    }
  }

  // Segmented progress with partial fill for current exercise
  Widget _buildSegmentedProgress() {
    final theme = Theme.of(context);
    final count = _exercises.length;
    final segments = <Widget>[];

    for (int i = 0; i < count; i++) {
      final ex = _exercises[i];
      final isCurrent = i == _currentExerciseIndex;
      final isDone = ex.sets.every((s) => s.isCompleted);

      Widget bar = Container(
        height: 6,
        decoration: BoxDecoration(
          color: isDone ? theme.colorScheme.primary : AppColors.grayDark,
          borderRadius: BorderRadius.circular(3),
        ),
      );

      if (isCurrent && !isDone) {
        final sets = ex.sets.length;
        final completed = ex.sets.where((s) => s.isCompleted).length + 0.0;
        final frac = (completed / sets).clamp(0.0, 1.0);
        bar = Stack(
          children: [
            Container(
              height: 6,
              decoration: BoxDecoration(
                color: AppColors.grayDark,
                borderRadius: BorderRadius.circular(3),
              ),
            ),
            FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: frac == 0 ? 0.05 : frac, // show minimal sliver if 0 to indicate current
              child: Container(
                height: 6,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
            ),
          ],
        );
      }

      segments.add(Expanded(child: bar));
      if (i < count - 1) {
        segments.add(SizedBox(width: AppSpacing.xs));
      }
    }

    return Row(children: segments);
  }

  // Coach tooltip
  void _showCoachTooltip() {
    final theme = Theme.of(context);
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: false,
      builder: (_) => Container(
        decoration: const BoxDecoration(
          color: AppColors.backgroundDark,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(width: 44, height: 5, decoration: BoxDecoration(color: AppColors.grayDark, borderRadius: BorderRadius.circular(3))),
            const SizedBox(height: AppSpacing.md),
            Text('Coach Tips', style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w800)),
            const SizedBox(height: AppSpacing.xs),
            Text(
              'Keep your core tight and control the eccentric. Drive through full range.\nBreathe out on the exertion.',
              style: theme.textTheme.bodyMedium?.copyWith(color: AppColors.grayLight, height: 1.4),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.md),
            CustomElevatedButton(onPressed: () => Navigator.pop(context), text: 'Got it'),
            const SizedBox(height: AppSpacing.sm),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final text = theme.textTheme;
    
    // Handle empty workout case
    if (_exercises.isEmpty) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: const Text('Workout'),
          backgroundColor: Colors.transparent,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text('No exercises in this workout'),
              SizedBox(height: 8),
              Text('Please add exercises to continue'),
            ],
          ),
        ),
      );
    }
    
    final current = _exercises[_currentExerciseIndex];
    
    // Handle empty sets case
    if (current.sets.isEmpty) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: Text(current.exercise.name),
          backgroundColor: Colors.transparent,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text('No sets configured for this exercise'),
              SizedBox(height: 8),
              Text('Please configure sets to continue'),
            ],
          ),
        ),
      );
    }
    
    final set = current.sets[_currentSetIndex];
    final image = current.exercise.imageUrl.isNotEmpty ? current.exercise.imageUrl : 'assets/images/chest_press.png';

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Top Bar with Exit and Pause
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm, vertical: AppSpacing.xs),
              child: Row(
                children: [
                  // Exit button
                  IconButton(
                    onPressed: _exitWorkout,
                    icon: const Icon(Icons.close, size: 28),
                    style: IconButton.styleFrom(
                      backgroundColor: AppColors.surfaceDark,
                      padding: const EdgeInsets.all(8),
                    ),
                  ),
                  const Spacer(),
                  // More actions (end workout, delete exercise)
                  PopupMenuButton<String>(
                    color: AppColors.surfaceDark,
                    onSelected: (value) {
                      if (value == 'end') {
                        _confirmEndWorkoutEarly();
                      } else if (value == 'delete_ex') {
                        _confirmDeleteCurrentExercise();
                      }
                    },
                    itemBuilder: (ctx) => [
                      PopupMenuItem(
                        value: 'end',
                        child: Row(
                          children: [
                            Icon(Icons.flag, color: Theme.of(ctx).colorScheme.primary, size: 18),
                            const SizedBox(width: 8),
                            const Text('End workout now'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'delete_ex',
                        child: Row(
                          children: [
                            Icon(Icons.delete_outline, color: Theme.of(ctx).colorScheme.error, size: 18),
                            const SizedBox(width: 8),
                            const Text('Delete current exercise'),
                          ],
                        ),
                      ),
                    ],
                    child: IconButton(
                      onPressed: null,
                      icon: const Icon(Icons.more_vert, size: 22),
                      style: IconButton.styleFrom(
                        backgroundColor: AppColors.surfaceDark,
                        padding: const EdgeInsets.all(8),
                      ),
                    ),
                  ),
                  // Pause/Resume button
                  IconButton(
                    onPressed: _pauseResume,
                    icon: Icon(_isPaused ? Icons.play_arrow : Icons.pause, size: 28),
                    style: IconButton.styleFrom(
                      backgroundColor: AppColors.surfaceDark,
                      padding: const EdgeInsets.all(8),
                    ),
                  ),
                ],
              ),
            ),
            // HUD
            Padding(
              padding: const EdgeInsets.fromLTRB(AppSpacing.sm, 0, AppSpacing.sm, AppSpacing.sm),
              child: Column(
                children: [
                  Row(
                    children: [
                      // Elapsed
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(_formatTime(_timerSeconds), style: text.headlineMedium?.copyWith(fontWeight: FontWeight.w800, color: theme.colorScheme.primary)),
                            Text('Elapsed', style: text.bodySmall?.copyWith(color: AppColors.grayLight)),
                          ],
                        ),
                      ),
                      // Percent
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text('${_overallPercent.round()}%', style: text.headlineMedium?.copyWith(fontWeight: FontWeight.w800)),
                            Text('Complete', style: text.bodySmall?.copyWith(color: AppColors.grayLight)),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppSpacing.sm),
                  _buildSegmentedProgress(),
                ],
              ),
            ),

            // Exercise header
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
              child: Column(
                children: [
                  Text(current.exercise.name, style: text.headlineSmall?.copyWith(fontWeight: FontWeight.w800), textAlign: TextAlign.center, maxLines: 2),
                  const SizedBox(height: 4),
                  Text('Set ${_currentSetIndex + 1} / ${current.sets.length}', style: text.bodyMedium?.copyWith(color: AppColors.grayLight)),
                ],
              ),
            ),
            const SizedBox(height: AppSpacing.sm),

            // Player surface (image-based)
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    final size = Size(constraints.maxWidth, constraints.maxHeight);
                    return GestureDetector(
                      onTapDown: (d) => _onTapDown(d, size),
                      onHorizontalDragEnd: _onHorizontalDragEnd,
                      onVerticalDragEnd: _onVerticalDragEnd,
                      child: Stack(
                        fit: StackFit.expand,
                        children: [
                          // Background image
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              image: DecorationImage(image: AssetImage(image), fit: BoxFit.cover),
                            ),
                          ),
                          // Dark overlay for legibility
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [Colors.black.withValues(alpha: 0.15), Colors.black.withValues(alpha: 0.5)],
                              ),
                            ),
                          ),
                          // Center play/pause icon (stub)
                          Center(
                            child: AnimatedOpacity(
                              opacity: _isPlaying ? 0.0 : 1.0,
                              duration: const Duration(milliseconds: 200),
                              child: Container(
                                padding: const EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                  color: Colors.black.withValues(alpha: 0.4),
                                  borderRadius: BorderRadius.circular(40),
                                ),
                                child: const Icon(Icons.play_arrow, color: Colors.white, size: 48),
                              ),
                            ),
                          ),
                          // Inline metrics (bottom-left reps, bottom-right weight)
                          Positioned(
                            left: 12,
                            bottom: 12 + 72, // above next strip
                            child: _metricPill(label: 'Reps', value: set.reps.toString(), onTap: _openPicker),
                          ),
                          Positioned(
                            right: 12,
                            bottom: 12 + 72,
                            child: _metricPill(label: 'Weight', value: '${set.weight?.toStringAsFixed(0) ?? '0'} lb', onTap: _openPicker),
                          ),
                          // AI Coach avatar
                          Positioned(
                            right: 16,
                            bottom: 16,
                            child: ScaleTransition(
                              scale: _pulseTween,
                              child: GestureDetector(
                                onTap: _showCoachTooltip,
                                onLongPress: () {
                                  setState(() => _coachMuted = !_coachMuted);
                                  _saveSession();
                                },
                                child: Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    Container(
                                      width: 56,
                                      height: 56,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: Colors.black.withValues(alpha: 0.3),
                                        border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
                                      ),
                                    ),
                                    CircleAvatar(
                                      radius: 22,
                                      backgroundColor: theme.colorScheme.primary,
                                      child: Icon(_coachMuted ? Icons.volume_off : Icons.record_voice_over, color: Colors.white),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          // Next exercise strip
                          if (!_atLastExercise)
                            Positioned(
                              left: 12,
                              right: 12,
                              bottom: 12,
                              child: GestureDetector(
                                onVerticalDragEnd: (d) {
                                  if (d.primaryVelocity != null && d.primaryVelocity! < -200) {
                                    // swipe up -> jump
                                    _confirmSkipRemainingAndGoNextExercise();
                                  }
                                },
                                child: Container(
                                  padding: const EdgeInsets.all(AppSpacing.sm),
                                  decoration: BoxDecoration(
                                    color: AppColors.surfaceDark.withValues(alpha: 0.9),
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(color: AppColors.grayDark.withValues(alpha: 0.5)),
                                  ),
                                  child: Row(
                                    children: [
                                      Container(
                                        width: 44,
                                        height: 44,
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(8),
                                          image: DecorationImage(
                                            image: AssetImage(_exercises[_currentExerciseIndex + 1].exercise.imageUrl),
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: AppSpacing.sm),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Text('Next → ${_exercises[_currentExerciseIndex + 1].exercise.name}',
                                                style: text.titleSmall?.copyWith(fontWeight: FontWeight.w700)),
                                            Text('Swipe up to jump', style: text.bodySmall?.copyWith(color: AppColors.grayLight)),
                                          ],
                                        ),
                                      ),
                                      Icon(Icons.keyboard_arrow_up, color: AppColors.grayLight),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),

            // Bottom bar with three actions
            Padding(
              padding: const EdgeInsets.fromLTRB(AppSpacing.sm, AppSpacing.sm, AppSpacing.sm, AppSpacing.sm),
              child: Column(
                children: [
                  // Main actions row
                  Row(
                    children: [
                      // Skip Set button
                      Expanded(
                        flex: 1,
                        child: CustomSecondaryButton(
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (ctx) => AlertDialog(
                                backgroundColor: AppColors.surfaceDark,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                title: const Text('Skip This Set?'),
                                content: Text(
                                  'This set will not be marked as complete. Continue to next set?',
                                  style: theme.textTheme.bodyMedium?.copyWith(color: AppColors.grayLight),
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.pop(ctx),
                                    child: const Text('Cancel'),
                                  ),
                                  ElevatedButton(
                                    onPressed: () {
                                      Navigator.pop(ctx);
                                      _skipSet();
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: theme.colorScheme.primary,
                                    ),
                                    child: const Text('Skip'),
                                  ),
                                ],
                              ),
                            );
                          },
                          text: 'Skip',
                          height: 56,
                          borderRadius: 16,
                        ),
                      ),
                      const SizedBox(width: AppSpacing.xs),
                      // Log RPE button
                      Expanded(
                        flex: 2,
                        child: CustomSecondaryButton(
                          onPressed: () => _openRpeSheet(),
                          text: 'Log RPE',
                          height: 56,
                          borderRadius: 16,
                        ),
                      ),
                      const SizedBox(width: AppSpacing.xs),
                      // Complete Set button
                      Expanded(
                        flex: 2,
                        child: CustomElevatedButton(
                          onPressed: () => _nextSet(skipRest: false),
                          text: 'Complete',
                          icon: const Icon(Icons.check, size: 20),
                          height: 56,
                          borderRadius: 16,
                        ),
                      ),
                    ],
                  ),
                  // Quick navigation row
                  const SizedBox(height: AppSpacing.xs),
                  Row(
                    children: [
                      // Previous set
                      IconButton(
                        onPressed: _atFirstSet ? null : _prevSet,
                        icon: const Icon(Icons.skip_previous),
                        style: IconButton.styleFrom(
                          foregroundColor: _atFirstSet ? AppColors.grayDark : AppColors.grayLight,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          'Quick Navigation',
                          style: theme.textTheme.bodySmall?.copyWith(color: AppColors.grayLight),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      // Next exercise
                      IconButton(
                        onPressed: _atLastExercise ? null : () => _confirmSkipRemainingAndGoNextExercise(),
                        icon: const Icon(Icons.skip_next),
                        style: IconButton.styleFrom(
                          foregroundColor: _atLastExercise ? AppColors.grayDark : AppColors.grayLight,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _metricPill({required String label, required String value, required VoidCallback onTap}) {
    final theme = Theme.of(context);
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md, vertical: AppSpacing.xs),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.35),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.white.withValues(alpha: 0.12)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(label, style: theme.textTheme.bodySmall?.copyWith(color: Colors.white70)),
            Text(value, style: theme.textTheme.titleMedium?.copyWith(color: Colors.white, fontWeight: FontWeight.w700)),
          ],
        ),
      ),
    );
  }

  // RPE sheet
  void _openRpeSheet() {
    _timer?.cancel(); // Pause timer during RPE
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: false,
      enableDrag: false,
      builder: (context) => _RpeSheet(
        onComplete: (rpe) async {
          Navigator.pop(context);
          _startTimer(); // Resume timer
          if (rpe != null && rpe > 0) {
            // Store RPE for future weight adjustments
            setState(() {
              // TODO: Store RPE with set data when model supports it
            });
          }
          await _nextSet();
        },
        onSkip: () {
          Navigator.pop(context);
          _startTimer(); // Resume timer
          _nextSet(skipRest: true);
        },
      ),
    ).whenComplete(() {
      // Ensure timer resumes if sheet dismissed
      if (!_isPaused) _startTimer();
    });
  }

  // Rest sheet matching requested design with theme styling and background image
  Future<bool?> _showRestSheet({
    required int totalSeconds,
    required String backgroundImage,
    required String nextTitle,
  }) async {
    int remaining = totalSeconds;
    bool running = true;
    Timer? ticker;

    await showModalBottomSheet<bool>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (ctx) {
        return StatefulBuilder(
          builder: (ctx, setLocal) {
            // start ticker lazily once the sheet builds
            ticker ??= Timer.periodic(const Duration(seconds: 1), (_) {
              if (!running) return;
              if (remaining > 0) {
                remaining--;
                setLocal(() {});
              }
              if (remaining == 0) {
                Navigator.of(ctx).maybePop(false);
              }
            });

            String mmss(int s) {
              final m = (s ~/ 60).toString().padLeft(1, '0');
              final ss = (s % 60).toString().padLeft(2, '0');
              return '$m:$ss';
            }

            double progress() => 1 - (remaining / totalSeconds);

            void addSecs(int delta) {
              remaining = (remaining + delta).clamp(0, 60 * 10); // clamp to 10 min
              setLocal(() {});
            }

            return WillPopScope(
              onWillPop: () async {
                ticker?.cancel();
                return true;
              },
              child: Container(
                height: MediaQuery.of(context).size.height,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(
                      backgroundImage.isNotEmpty ? backgroundImage : 'assets/images/chest_press.png',
                    ),
                    fit: BoxFit.cover,
                    colorFilter: ColorFilter.mode(Colors.black.withValues(alpha: 0.55), BlendMode.darken),
                  ),
                ),
                child: SafeArea(
                  child: Column(
                    children: [
                      // Top HUD re-using active layout idea
                      Padding(
                        padding: const EdgeInsets.fromLTRB(AppSpacing.sm, AppSpacing.xs, AppSpacing.sm, AppSpacing.sm),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(_formatTime(_timerSeconds), style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: Theme.of(context).colorScheme.primary, fontWeight: FontWeight.w800)),
                                      Text('Elapsed Time', style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.grayLight)),
                                    ],
                                  ),
                                ),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      Text('${_overallPercent.round()}%', style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.w800)),
                                      Text('Complete', style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.grayLight)),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: AppSpacing.sm),
                            _buildSegmentedProgress(),
                          ],
                        ),
                      ),

                      // Now & metrics
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
                        child: Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Now:', style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.grayLight)),
                                  Text(_exercises[_currentExerciseIndex].exercise.name, style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w700)),
                                ],
                              ),
                            ),
                            Text('Set ${_currentSetIndex + 1} / ${_exercises[_currentExerciseIndex].sets.length}', style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.grayLight)),
                          ],
                        ),
                      ),
                      const SizedBox(height: AppSpacing.sm),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              children: [
                                Text('${_exercises[_currentExerciseIndex].sets[_currentSetIndex].reps}', style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w800)),
                                Text('Reps', style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.grayLight)),
                              ],
                            ),
                            Column(
                              children: [
                                Text(_exercises[_currentExerciseIndex].sets[_currentSetIndex].weight?.toStringAsFixed(0) ?? '0', style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w800)),
                                Text('lbs', style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.grayLight)),
                              ],
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: AppSpacing.lg),

                      // Circular countdown
                      SizedBox(
                        width: 220,
                        height: 220,
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            SizedBox(
                              width: 220,
                              height: 220,
                              child: CircularProgressIndicator(
                                value: progress(),
                                strokeWidth: 16,
                                strokeCap: StrokeCap.round,
                                backgroundColor: Colors.white.withValues(alpha: 0.1),
                                valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
                              ),
                            ),
                            Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(mmss(remaining), style: Theme.of(context).textTheme.headlineLarge?.copyWith(fontWeight: FontWeight.w800)),
                                const SizedBox(height: 4),
                                Text('remaining', style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.grayLight)),
                              ],
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: AppSpacing.md),

                      // +/- controls with better layout
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          _roundAdjustBtn(label: '-15s', onTap: () => addSecs(-15)),
                          const SizedBox(width: AppSpacing.lg),
                          _roundAdjustBtn(label: '+15s', onTap: () => addSecs(15)),
                        ],
                      ),

                      const Spacer(),

                      // Actions with better layout
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: CustomSecondaryButton(
                                    onPressed: () {
                                      setLocal(() => running = !running);
                                    },
                                    text: running ? 'Pause' : 'Resume',
                                    borderRadius: 16,
                                    height: 56,
                                  ),
                                ),
                                const SizedBox(width: AppSpacing.sm),
                                Expanded(
                                  flex: 2,
                                  child: CustomElevatedButton(
                                    onPressed: () {
                                      ticker?.cancel();
                                      Navigator.of(ctx).pop(true); // skipped
                                    },
                                    text: 'Skip Rest',
                                    icon: const Icon(Icons.skip_next, size: 20),
                                    borderRadius: 16,
                                    height: 56,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: AppSpacing.sm),
                          ],
                        ),
                      ),

                      // Next preview
                      if (!_atLastSetOfExercise || !_atLastExercise)
                        Padding(
                          padding: const EdgeInsets.fromLTRB(AppSpacing.sm, 0, AppSpacing.sm, AppSpacing.md),
                          child: Row(
                            children: [
                              Container(
                                width: 44,
                                height: 44,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  image: DecorationImage(
                                    image: AssetImage(
                                      !_atLastSetOfExercise
                                          ? _exercises[_currentExerciseIndex].exercise.imageUrl
                                          : _exercises[_currentExerciseIndex + 1].exercise.imageUrl,
                                    ),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              const SizedBox(width: AppSpacing.sm),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text('Next:', style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.grayLight)),
                                    Text(nextTitle, style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w700)),
                                  ],
                                ),
                              ),
                              const Icon(Icons.fast_forward, color: Colors.white70),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    ).whenComplete(() {
      ticker?.cancel();
    });
    return null;
  }

  Widget _roundAdjustBtn({required String label, required VoidCallback onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 72,
        height: 72,
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.08),
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white.withValues(alpha: 0.15)),
        ),
        alignment: Alignment.center,
        child: Text(label, style: Theme.of(context).textTheme.titleMedium?.copyWith(color: Colors.white, fontWeight: FontWeight.w700)),
      ),
    );
  }

  // Enhanced RPE sheet with better UX
  Widget _RpeSheet({required ValueChanged<int?> onComplete, required VoidCallback onSkip}) {
    int rpe = 7;
    return StatefulBuilder(
      builder: (ctx, setLocal) {
        final theme = Theme.of(ctx);
        String titleFor(int v) {
          if (v <= 4) return 'Too Easy';
          if (v <= 6) return 'Ideal';
          if (v <= 8) return 'Hard';
          return 'Killer';
        }

        String hintFor(int v) {
          if (v <= 4) return 'Consider +5–10% weight or +2–5 reps next time';
          if (v <= 6) return 'Right on target';
          if (v <= 8) return 'Slightly heavy, maintain or -5%';
          return 'Reduce weight 5–10% for form';
        }

        return Container(
          decoration: const BoxDecoration(
            color: AppColors.backgroundDark,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: SafeArea(
            top: false,
            child: SingleChildScrollView(
              padding: const EdgeInsets.fromLTRB(AppSpacing.sm, AppSpacing.sm, AppSpacing.sm, AppSpacing.sm),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Handle
                  Container(width: 44, height: 5, decoration: BoxDecoration(color: AppColors.grayDark, borderRadius: BorderRadius.circular(3))),
                  const SizedBox(height: AppSpacing.md),

                  // Coach bubble
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(AppSpacing.md),
                    decoration: BoxDecoration(
                      color: AppColors.surfaceDark,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: AppColors.grayDark.withValues(alpha: 0.35)),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(Icons.insights, color: theme.colorScheme.primary),
                        const SizedBox(width: AppSpacing.sm),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('How difficult was that set?', style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w800)),
                              const SizedBox(height: 4),
                              Text(
                                'Rating your exertion helps personalize the weights I recommend for you in the future.',
                                style: theme.textTheme.bodyMedium?.copyWith(color: AppColors.grayLight, height: 1.45),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: AppSpacing.md),

                  // Exercise card header
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(AppSpacing.md),
                    decoration: BoxDecoration(
                      color: AppColors.surfaceDark,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: AppColors.grayDark.withValues(alpha: 0.35)),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            image: DecorationImage(
                              image: AssetImage(_exercises[_currentExerciseIndex].exercise.imageUrl),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        const SizedBox(width: AppSpacing.sm),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _exercises[_currentExerciseIndex].exercise.name,
                                style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w700),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                '${_exercises[_currentExerciseIndex].sets.length} sets',
                                style: theme.textTheme.bodySmall?.copyWith(color: AppColors.grayLight),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: AppSpacing.md),

                  // Recommendation headline
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(titleFor(rpe), style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w800)),
                        const SizedBox(height: 4),
                        Text(
                          hintFor(rpe),
                          style: theme.textTheme.bodyMedium?.copyWith(color: AppColors.grayLight),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: AppSpacing.md),

                  // 5-point slider (Effortless → Killer)
                  Column(
                    children: [
                      SliderTheme(
                        data: SliderTheme.of(ctx).copyWith(
                          activeTrackColor: theme.colorScheme.primary,
                          inactiveTrackColor: AppColors.grayDark,
                          thumbColor: theme.colorScheme.primary,
                          overlayColor: theme.colorScheme.primary.withValues(alpha: 0.2),
                        ),
                        child: Slider(
                          min: 1,
                          max: 5,
                          divisions: 4,
                          value: (rpe <= 2) ? 1 : (rpe <= 4) ? 2 : (rpe <= 6) ? 3 : (rpe <= 8) ? 4 : 5,
                          onChanged: (v) {
                            // map back to rpe buckets
                            final bucket = v.round();
                            setLocal(() {
                              rpe = bucket == 1 ? 2 : bucket == 2 ? 4 : bucket == 3 ? 6 : bucket == 4 ? 8 : 10;
                            });
                          },
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('Effortless', style: theme.textTheme.labelSmall?.copyWith(color: AppColors.grayLight)),
                          Text('Easy', style: theme.textTheme.labelSmall?.copyWith(color: AppColors.grayLight)),
                          Text('Ideal', style: theme.textTheme.labelSmall?.copyWith(color: AppColors.grayLight)),
                          Text('Hard', style: theme.textTheme.labelSmall?.copyWith(color: AppColors.grayLight)),
                          Text('Killer', style: theme.textTheme.labelSmall?.copyWith(color: AppColors.grayLight)),
                        ],
                      ),
                    ],
                  ),

                  const SizedBox(height: AppSpacing.md),

                  // Primary & Secondary CTAs
                  Row(
                    children: [
                      Expanded(
                        child: CustomSecondaryButton(
                          onPressed: onSkip,
                          text: 'Skip RPE',
                          borderRadius: 16,
                          height: 56,
                        ),
                      ),
                      const SizedBox(width: AppSpacing.sm),
                      Expanded(
                        flex: 2,
                        child: CustomElevatedButton(
                          onPressed: () {
                            onComplete(rpe);
                          },
                          text: 'Log & Continue',
                          borderRadius: 16,
                          height: 56,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
