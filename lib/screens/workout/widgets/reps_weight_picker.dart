import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/widgets/custom_buttons.dart';

class RepsWeightPicker extends StatefulWidget {
  final int initialReps;
  final int minReps;
  final int maxReps;
  final int repsStep;

  final double initialWeight;
  final double minWeight;
  final double maxWeight;
  final double weightStep;
  final String weightUnit; // "lb" or "kg"

  final void Function(int reps, double weight) onConfirm;

  const RepsWeightPicker({
    super.key,
    required this.onConfirm,
    this.initialReps = 10,
    this.minReps = 1,
    this.maxReps = 50,
    this.repsStep = 1,
    this.initialWeight = 20,
    this.minWeight = 0,
    this.maxWeight = 500,
    this.weightStep = 5,
    this.weightUnit = 'lb',
  });

  static Future<void> show(
    BuildContext context, {
    required int initialReps,
    required double initialWeight,
    String weightUnit = 'lb',
    void Function(int reps, double weight)? onConfirm,
  }) async {
    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (ctx) {
        return RepsWeightPicker(
          initialReps: initialReps,
          initialWeight: initialWeight,
          weightUnit: weightUnit,
          onConfirm: (r, w) => onConfirm?.call(r, w),
        );
      },
    );
  }

  @override
  State<RepsWeightPicker> createState() => _RepsWeightPickerState();
}

class _RepsWeightPickerState extends State<RepsWeightPicker> {
  late List<int> _repsValues;
  late List<double> _weightValues;

  late FixedExtentScrollController _repsController;
  late FixedExtentScrollController _weightController;

  @override
  void initState() {
    super.initState();
    _repsValues = List<int>.generate(
      ((widget.maxReps - widget.minReps) ~/ widget.repsStep) + 1,
      (i) => widget.minReps + (i * widget.repsStep),
    );

    final steps = ((widget.maxWeight - widget.minWeight) / widget.weightStep).floor();
    _weightValues = List<double>.generate(
      steps + 1,
      (i) => (widget.minWeight + (i * widget.weightStep)),
    );

    _repsController = FixedExtentScrollController(
      initialItem: _nearestIndexInt(_repsValues, widget.initialReps),
    );
    _weightController = FixedExtentScrollController(
      initialItem: _nearestIndexDouble(_weightValues, widget.initialWeight),
    );
  }

  int _nearestIndexInt(List<int> list, int value) {
    int idx = list.indexOf(value);
    if (idx != -1) return idx;
    int nearest = 0;
    int smallestDiff = 1 << 30;
    for (int i = 0; i < list.length; i++) {
      final d = (list[i] - value).abs();
      if (d < smallestDiff) {
        smallestDiff = d;
        nearest = i;
      }
    }
    return nearest;
  }

  int _nearestIndexDouble(List<double> list, double value) {
    int nearest = 0;
    double smallestDiff = 1e9;
    for (int i = 0; i < list.length; i++) {
      final d = (list[i] - value).abs();
      if (d < smallestDiff) {
        smallestDiff = d;
        nearest = i;
      }
    }
    return nearest;
  }

  int get _currentReps => _repsValues[_repsController.selectedItem];
  double get _currentWeight => _weightValues[_weightController.selectedItem];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: const BoxDecoration(
        color: AppColors.backgroundDark,
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: SafeArea(
        top: false,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(AppSpacing.lg, AppSpacing.md, AppSpacing.lg, AppSpacing.lg),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle
              Container(
                width: 44,
                height: 5,
                decoration: BoxDecoration(
                  color: AppColors.grayDark,
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
              const SizedBox(height: AppSpacing.md),

              Text(
                'Adjust Reps & Weight',
                style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w800),
              ),
              const SizedBox(height: AppSpacing.xs),
              Text(
                'Fine tune your next set.',
                style: theme.textTheme.bodyMedium?.copyWith(color: AppColors.grayLight),
              ),

              const SizedBox(height: AppSpacing.md),

              // Pickers
              Row(
                children: [
                  Expanded(child: _buildPickerCard(
                    title: 'Reps',
                    unit: 'reps',
                    child: _buildCupertinoPicker<int>(
                      controller: _repsController,
                      itemCount: _repsValues.length,
                      itemBuilder: (i) => _pickerText('${_repsValues[i]}'),
                    ),
                  )),
                  const SizedBox(width: AppSpacing.sm),
                  Expanded(child: _buildPickerCard(
                    title: 'Weight',
                    unit: widget.weightUnit,
                    child: _buildCupertinoPicker<double>(
                      controller: _weightController,
                      itemCount: _weightValues.length,
                      itemBuilder: (i) => _pickerText(_weightValues[i].toStringAsFixed(0)),
                    ),
                  )),
                ],
              ),

              const SizedBox(height: AppSpacing.lg),

              Row(
                children: [
                  Expanded(
                    child: CustomSecondaryButton(
                      onPressed: () => Navigator.pop(context),
                      text: 'Cancel',
                      borderRadius: 16,
                      height: 56,
                    ),
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Expanded(
                    child: CustomElevatedButton(
                      onPressed: () {
                        widget.onConfirm(_currentReps, _currentWeight);
                        Navigator.pop(context);
                      },
                      text: 'Confirm',
                      borderRadius: 16,
                      height: 56,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPickerCard({
    required String title,
    required String unit,
    required Widget child,
  }) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.surfaceDark,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.grayDark.withValues(alpha: 0.4)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(title, style: theme.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600)),
          const SizedBox(height: AppSpacing.xs),
          SizedBox(
            height: 160,
            child: child,
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(unit, style: theme.textTheme.bodySmall?.copyWith(color: AppColors.grayLight)),
        ],
      ),
    );
  }

  Widget _pickerText(String text) {
    final theme = Theme.of(context);
    return Center(
      child: Text(
        text,
        style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700),
      ),
    );
  }

  Widget _buildCupertinoPicker<T>({
    required FixedExtentScrollController controller,
    required int itemCount,
    required Widget Function(int index) itemBuilder,
  }) {
    return CupertinoPicker(
      backgroundColor: Colors.transparent,
      scrollController: controller,
      itemExtent: 44,
      squeeze: 1.1,
      diameterRatio: 2.4,
      onSelectedItemChanged: (_) {},
      selectionOverlay: const CupertinoPickerDefaultSelectionOverlay(background: Colors.transparent),
      children: List.generate(itemCount, (i) => itemBuilder(i)),
    );
  }
}
