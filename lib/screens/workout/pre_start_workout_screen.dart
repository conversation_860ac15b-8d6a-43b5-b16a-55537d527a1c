import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/models/workout_data.dart';
import 'package:openfitv4/screens/workout/active_workout_screen.dart';
import 'package:openfitv4/widgets/custom_buttons.dart';

class PreStartWorkoutScreen extends StatelessWidget {
  final Workout workout;
  
  const PreStartWorkoutScreen({
    super.key,
    required this.workout,
  });

  Widget _roundIcon(ThemeData theme, IconData icon, VoidCallback onPressed) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: AppColors.backgroundDark.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(12),
      ),
      child: IconButton(
        icon: Icon(icon, color: Colors.white, size: 20),
        onPressed: onPressed,
      ),
    );
  }

  Widget _buildStatOverlay(String value, String label, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white70, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w700,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.7),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return Scaffold(
      backgroundColor: AppColors.backgroundDark,
      body: Stack(
        children: [
          // Background image
          Positioned.fill(
            child: Image.asset(
              workout.imageUrl,
              fit: BoxFit.cover,
            ),
          ),
          
          // Gradient overlay
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.2),
                    Colors.black.withValues(alpha: 0.85),
                  ],
                ),
              ),
            ),
          ),

          // Top navigation
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.sm,
                vertical: AppSpacing.sm,
              ),
              child: Row(
                children: [
                  _roundIcon(
                    theme,
                    Icons.arrow_back,
                    () => Navigator.of(context).pop(),
                  ),
                  const Spacer(),
                  _roundIcon(
                    theme,
                    Icons.settings_outlined,
                    () {},
                  ),
                ],
              ),
            ),
          ),

          // Content
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              padding: const EdgeInsets.all(AppSpacing.lg),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Workout name
                  Text(
                    workout.name,
                    style: textTheme.displaySmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: AppSpacing.xs),
                  
                  // Difficulty badge
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSpacing.sm,
                      vertical: AppSpacing.xs,
                    ),
                    decoration: BoxDecoration(
                      color: _getDifficultyColor(workout.difficulty),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      workout.difficulty.toUpperCase(),
                      style: textTheme.bodySmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w700,
                        letterSpacing: 1,
                      ),
                    ),
                  ),

                  const SizedBox(height: AppSpacing.xl),

                  // Stats row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildStatOverlay(
                        '${workout.duration}',
                        'minutes',
                        Icons.access_time,
                      ),
                      _buildStatOverlay(
                        '${workout.caloriesBurn}',
                        'calories',
                        Icons.local_fire_department,
                      ),
                      _buildStatOverlay(
                        '${workout.exercises.length}',
                        'exercises',
                        Icons.fitness_center,
                      ),
                    ],
                  ),

                  const SizedBox(height: AppSpacing.xl),

                  // Motivational text
                  Text(
                    'Ready to push your limits?',
                    style: textTheme.titleLarge?.copyWith(
                      color: Colors.white70,
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: AppSpacing.xl),

                  // Start button
                  CustomElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pushReplacement(
                        MaterialPageRoute(
                          builder: (context) => ActiveWorkoutScreen(
                            workout: workout,
                          ),
                        ),
                      );
                    },
                    text: "Let's Go!",
                    height: 60,
                    borderRadius: 30,
                    icon: const Icon(
                      Icons.play_arrow,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),

                  const SizedBox(height: AppSpacing.lg),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return const Color(0xFF10B981);
      case 'intermediate':
        return const Color(0xFFF59E0B);
      case 'advanced':
        return const Color(0xFFEF4444);
      default:
        return AppColors.grayMedium;
    }
  }
}