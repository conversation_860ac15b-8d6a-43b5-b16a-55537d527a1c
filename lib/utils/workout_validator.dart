import 'package:openfitv4/models/workout_data.dart';

class WorkoutValidator {
  static bool isWorkoutValid(Workout workout) {
    // Check if workout has exercises
    if (workout.exercises.isEmpty) {
      return false;
    }
    
    // Check if at least one exercise has sets
    final hasValidSets = workout.exercises.any((exercise) => exercise.sets.isNotEmpty);
    if (!hasValidSets) {
      return false;
    }
    
    return true;
  }
  
  static String getValidationError(Workout workout) {
    if (workout.exercises.isEmpty) {
      return 'No exercises found in this workout';
    }
    
    final hasValidSets = workout.exercises.any((exercise) => exercise.sets.isNotEmpty);
    if (!hasValidSets) {
      return 'No sets configured for exercises';
    }
    
    return '';
  }
}