import 'dart:convert';
import 'dart:developer' as developer;
import 'package:http/http.dart' as http;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:openfitv4/models/workout_data.dart';

/// WorkoutService fetches and mutates workout data in Supabase and maps it to
/// the existing Workout / WorkoutExercise / WorkoutSet / Exercise models.
///
/// Confirmed schema:
/// - workouts(id, user_id, name, category, duration, calories_burn, created_at)
/// - workout_exercises(id, workout_id, exercise_id, order, rest_time)
/// - workout_sets(id, workout_exercise_id, order, reps, weight)
/// - exercises(id, name, description, video_url, primary_muscle, equipment, secondary_muscle, instructions, category, vertical_video, created_at)
class WorkoutService {
  WorkoutService(this._client);

  final SupabaseClient _client;

  // ========================= READ =========================

  /// Get the current active workout for a user (single workout system)
  Future<Workout?> getActiveWorkout(String userId) async {
    developer.log('📥 getActiveWorkout(userId=$userId)', name: 'WorkoutService');

    try {
      final workoutsRes = await _client
          .from('workouts')
          .select('id, user_id, name, duration, ai_description, created_at')
          .eq('user_id', userId)
          .eq('is_active', true)
          .limit(1);

      if (workoutsRes.isEmpty) {
        developer.log('No active workout found for user $userId', name: 'WorkoutService');
        return null;
      }

      final w = workoutsRes.first;
      final workoutId = w['id'] as String;
      final mappedExercises = await _fetchExercisesForWorkout(workoutId);

      return Workout(
        id: workoutId,
        name: (w['name'] as String?) ?? 'Workout',
        description: (w['ai_description'] as String?) ?? 'Personalized session',
        imageUrl: mappedExercises.isNotEmpty ? mappedExercises.first.exercise.imageUrl : 'assets/images/chest_press.png',
        category: _inferCategory(mappedExercises),
        difficulty: 'Intermediate',
        duration: (w['duration'] as num?)?.toInt() ?? _estimateDuration(mappedExercises),
        caloriesBurn: _estimateCalories(mappedExercises),
        equipment: _collectEquipment(mappedExercises),
        muscleGroups: _collectMuscles(mappedExercises),
        exercises: mappedExercises,
      );
    } catch (e, st) {
      developer.log('❌ getActiveWorkout error: $e', name: 'WorkoutService');
      developer.log('📍 $st', name: 'WorkoutService');
      return null;
    }
  }

  /// Get all workouts for a user (for admin/history purposes)
  Future<List<Workout>> getUserWorkouts(String userId) async {
    developer.log('📥 getUserWorkouts(userId=$userId)', name: 'WorkoutService');

    try {
      final workoutsRes = await _client
          .from('workouts')
          .select('id, user_id, name, duration, ai_description, created_at')
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      final workouts = <Workout>[];
      for (final w in workoutsRes.cast<Map<String, dynamic>>()) {
        final workoutId = w['id'] as String;

        final mappedExercises = await _fetchExercisesForWorkout(workoutId);

        workouts.add(
          Workout(
            id: workoutId,
            name: (w['name'] as String?) ?? 'Workout',
            description: (w['ai_description'] as String?) ?? 'Personalized session',
            imageUrl: mappedExercises.isNotEmpty ? mappedExercises.first.exercise.imageUrl : 'assets/images/chest_press.png',
            category: _inferCategory(mappedExercises),
            difficulty: 'Intermediate',
            duration: (w['duration'] as num?)?.toInt() ?? _estimateDuration(mappedExercises),
            caloriesBurn: _estimateCalories(mappedExercises),
            equipment: _collectEquipment(mappedExercises),
            muscleGroups: _collectMuscles(mappedExercises),
            exercises: mappedExercises,
          ),
        );
      }

      return workouts;
    } catch (e, st) {
      developer.log('❌ getUserWorkouts error: $e', name: 'WorkoutService');
      developer.log('📍 $st', name: 'WorkoutService');
      return [];
    }
  }

  Future<Workout?> getWorkoutById(String workoutId) async {
    developer.log('📥 getWorkoutById($workoutId)', name: 'WorkoutService');

    try {
      final wRows = await _client
          .from('workouts')
          .select('id, user_id, name, duration, ai_description, created_at')
          .eq('id', workoutId)
          .limit(1);

      if (wRows.isEmpty) return null;
      final w = wRows.first;

      final mappedExercises = await _fetchExercisesForWorkout(workoutId);

      return Workout(
        id: w['id'] as String,
        name: (w['name'] as String?) ?? 'Workout',
        description: (w['ai_description'] as String?) ?? 'Personalized session',
        imageUrl: mappedExercises.isNotEmpty ? mappedExercises.first.exercise.imageUrl : 'assets/images/chest_press.png',
        category: _inferCategory(mappedExercises),
        difficulty: 'Intermediate',
        duration: (w['duration'] as num?)?.toInt() ?? _estimateDuration(mappedExercises),
        caloriesBurn: _estimateCalories(mappedExercises),
        equipment: _collectEquipment(mappedExercises),
        muscleGroups: _collectMuscles(mappedExercises),
        exercises: mappedExercises,
      );
    } catch (e, st) {
      developer.log('❌ getWorkoutById error: $e', name: 'WorkoutService');
      developer.log('📍 $st', name: 'WorkoutService');
      return null;
    }
  }

  Future<List<WorkoutExercise>> _fetchExercisesForWorkout(String workoutId) async {
    final exRows = await _client
        .from('workout_exercises')
        .select('id, workout_id, exercise_id, name, sets, reps, weight, rest_interval, order_index')
        .eq('workout_id', workoutId)
        .order('order_index', ascending: true);

    final mappedExercises = <WorkoutExercise>[];

    for (final row in exRows.cast<Map<String, dynamic>>()) {
      // Create exercise from workout_exercises data
      final exercise = Exercise(
        id: (row['exercise_id'] ?? row['id']).toString(),
        name: (row['name'] as String?) ?? 'Exercise',
        muscle: 'full_body', // We'll infer this from exercise name or fetch from exercises table if needed
        equipment: 'bodyweight', // Default, could be enhanced by joining exercises table
        difficulty: 'beginner',
        instructions: 'Follow proper form. Breathe and control tempo.',
        imageUrl: 'assets/images/chest_press.png',
      );

      // Parse sets from arrays
      final sets = <WorkoutSet>[];
      final setsCount = (row['sets'] as num?)?.toInt() ?? 0;
      final repsList = row['reps'] as List<dynamic>? ?? [];
      final weightsList = row['weight'] as List<dynamic>? ?? [];

      for (int i = 0; i < setsCount; i++) {
        final reps = i < repsList.length ? int.tryParse(repsList[i].toString()) ?? 0 : 0;
        final weight = i < weightsList.length ? double.tryParse(weightsList[i].toString()) : null;
        
        sets.add(
          WorkoutSet(
            setNumber: i + 1,
            reps: reps,
            weight: weight,
          ),
        );
      }

      mappedExercises.add(
        WorkoutExercise(
          exercise: exercise,
          sets: sets,
          restTime: (row['rest_interval'] as num?)?.toInt() ?? 60,
          sourceId: (row['id'] as String?),
        ),
      );
    }
  
    return mappedExercises;
  }

  // ========================= WRITE (Reorder / Delete / Replace) =========================

  /// Reorder exercises within a workout by updating the "order" column.
  /// orderedWorkoutExerciseIds is a list of workout_exercises.id in the desired 0-based order.
  Future<bool> reorderWorkoutExercises({
    required String workoutId,
    required List<String> orderedWorkoutExerciseIds,
  }) async {
    developer.log('🔧 reorderWorkoutExercises($workoutId, ${orderedWorkoutExerciseIds.length} items)', name: 'WorkoutService');
    try {
      for (int i = 0; i < orderedWorkoutExerciseIds.length; i++) {
        final weId = orderedWorkoutExerciseIds[i];
        await _client
            .from('workout_exercises')
            .update({'order': i})
            .eq('id', weId)
            .eq('workout_id', workoutId);
      }
      return true;
    } catch (e, st) {
      developer.log('❌ reorderWorkoutExercises error: $e', name: 'WorkoutService');
      developer.log('📍 $st', name: 'WorkoutService');
      return false;
    }
  }

  /// Delete an exercise from a workout (assumes ON DELETE CASCADE for workout_sets).
  Future<bool> deleteWorkoutExercise({
    required String workoutExerciseId,
  }) async {
    developer.log('🗑️ deleteWorkoutExercise($workoutExerciseId)', name: 'WorkoutService');
    try {
      await _client.from('workout_exercises').delete().eq('id', workoutExerciseId);
      return true;
    } catch (e, st) {
      developer.log('❌ deleteWorkoutExercise error: $e', name: 'WorkoutService');
      developer.log('📍 $st', name: 'WorkoutService');
      return false;
    }
  }

  /// Replace the exercise_id for a workout_exercises row. Optionally regenerate sets.
  Future<bool> replaceWorkoutExercise({
    required String workoutExerciseId,
    required String newExerciseId,
    bool regenerateSets = false,
  }) async {
    developer.log('♻️ replaceWorkoutExercise(we=$workoutExerciseId -> ex=$newExerciseId, regen=$regenerateSets)', name: 'WorkoutService');
    try {
      await _client
          .from('workout_exercises')
          .update({'exercise_id': newExerciseId})
          .eq('id', workoutExerciseId);

      if (regenerateSets) {
        final currentSets = await _client
            .from('workout_sets')
            .select('id, order')
            .eq('workout_exercise_id', workoutExerciseId)
            .order('order');

        await _client.from('workout_sets').delete().eq('workout_exercise_id', workoutExerciseId);
        for (final s in currentSets.cast<Map<String, dynamic>>()) {
          final ord = (s['order'] as num?)?.toInt() ?? 0;
          await _client.from('workout_sets').insert({
            'workout_exercise_id': workoutExerciseId,
            'order': ord,
            'reps': 10,
            'weight': null,
          });
        }
            }

      return true;
    } catch (e, st) {
      developer.log('❌ replaceWorkoutExercise error: $e', name: 'WorkoutService');
      developer.log('📍 $st', name: 'WorkoutService');
      return false;
    }
  }

  /// Suggest exercises by primary muscle, with optional search query.
  Future<List<Map<String, dynamic>>> suggestExercisesByMuscle({
    required String primaryMuscle,
    String? query,
    int limit = 25,
  }) async {
    try {
      final base = _client
          .from('exercises')
          .select('id, name, primary_muscle, equipment, instructions, description')
          .eq('primary_muscle', primaryMuscle);

      final rows = await (query != null && query.trim().isNotEmpty
              ? (base as PostgrestFilterBuilder).filter('name', 'ilike', '%${query.trim()}%')
              : base)
          .limit(limit);
      if (rows is List) {
        return rows.cast<Map<String, dynamic>>();
      }
      return [];
    } catch (e, st) {
      developer.log('❌ suggestExercisesByMuscle error: $e', name: 'WorkoutService');
      developer.log('📍 $st', name: 'WorkoutService');
      return [];
    }
  }

  /// Free text search across all exercises.
  Future<List<Map<String, dynamic>>> searchExercises({
    String? query,
    int limit = 50,
  }) async {
    try {
      final base = _client
          .from('exercises')
          .select('id, name, primary_muscle, equipment, instructions, description');

      final rows = await (query != null && query.trim().isNotEmpty
              ? (base as PostgrestFilterBuilder).filter('name', 'ilike', '%${query.trim()}%')
              : base)
          .limit(limit);
      if (rows is List) {
        return rows.cast<Map<String, dynamic>>();
      }
      return [];
    } catch (e, st) {
      developer.log('❌ searchExercises error: $e', name: 'WorkoutService');
      developer.log('📍 $st', name: 'WorkoutService');
      return [];
    }
  }

  // ========================= HELPERS =========================

  List<String> _collectEquipment(List<WorkoutExercise> exs) {
    final set = <String>{};
    for (final we in exs) {
      if (we.exercise.equipment.isNotEmpty) set.add(we.exercise.equipment);
    }
    return set.toList();
  }

  List<String> _collectMuscles(List<WorkoutExercise> exs) {
    final set = <String>{};
    for (final we in exs) {
      if (we.exercise.muscle.isNotEmpty) set.add(we.exercise.muscle);
    }
    return set.toList();
  }

  int _estimateDuration(List<WorkoutExercise> exs) {
    int seconds = 0;
    for (final we in exs) {
      final sets = we.sets.length;
      seconds += sets * (45 + (we.restTime));
    }
    return (seconds / 60).round().clamp(10, 120);
  }

  int _estimateCalories(List<WorkoutExercise> exs) {
    int base = exs.fold<int>(0, (acc, we) => acc + we.sets.length * 6);
    return base.clamp(50, 1200);
  }

  String _inferCategory(List<WorkoutExercise> exs) {
    if (exs.isEmpty) return 'General';
    
    // Simple category inference based on exercise names
    final exerciseNames = exs.map((e) => e.exercise.name.toLowerCase()).join(' ');
    
    if (exerciseNames.contains('squat') || exerciseNames.contains('lunge') || exerciseNames.contains('deadlift')) {
      return 'Lower Body';
    } else if (exerciseNames.contains('press') || exerciseNames.contains('push') || exerciseNames.contains('pull')) {
      return 'Upper Body';
    } else if (exerciseNames.contains('burpee') || exerciseNames.contains('cardio') || exerciseNames.contains('jump')) {
      return 'Cardio';
    } else {
      return 'Full Body';
    }
  }

  // ========================= CREATE =========================

  /// Create a new workout for the current user and return the created id.
  /// This replaces any existing active workout for the user.
  Future<String?> createWorkout({
    required String userId,
    required String name,
    String? aiDescription,
    int? duration,
  }) async {
    developer.log('🆕 createWorkout(name=$name) - replacing active workout', name: 'WorkoutService');
    try {
      // First, delete any existing active workout for this user
      await _deleteActiveWorkout(userId);
      
      final insert = await _client
          .from('workouts')
          .insert({
            'user_id': userId,
            'name': name,
            'is_active': true, // Mark as the active workout
            if (aiDescription != null) 'ai_description': aiDescription,
            if (duration != null) 'duration': duration,
          })
          .select('id')
          .single();

      final id = insert['id']?.toString();
      developer.log('✅ Created new active workout: $id', name: 'WorkoutService');
      return id;
    } catch (e, st) {
      developer.log('❌ createWorkout error: $e', name: 'WorkoutService');
      developer.log('📍 $st', name: 'WorkoutService');
      return null;
    }
  }

  /// Delete the current active workout for a user (internal helper)
  Future<void> _deleteActiveWorkout(String userId) async {
    try {
      // For single workout system, just mark existing active workouts as inactive
      // This is simpler and avoids complex cascading deletes during workout creation
      await _client
          .from('workouts')
          .update({'is_active': false})
          .eq('user_id', userId)
          .eq('is_active', true);
      
      developer.log('🔄 Marked existing active workouts as inactive for user $userId', name: 'WorkoutService');
    } catch (e) {
      developer.log('⚠️ Error deactivating existing workouts: $e', name: 'WorkoutService');
      // Don't throw - we can still create the new workout
    }
  }

  /// Add an exercise to a workout with generated sets.
  /// repsBySet defines reps for each set; weightsBySet is optional and may be shorter than reps list.
  Future<bool> addExerciseToWorkout({
    required String workoutId,
    required String exerciseId,
    required int orderIndex,
    int restTimeSeconds = 60,
    required List<int> repsBySet,
    List<double>? weightsBySet,
  }) async {
    developer.log('➕ addExerciseToWorkout(workout=$workoutId, ex=$exerciseId, sets=${repsBySet.length})', name: 'WorkoutService');
    try {
      // Get exercise name for the workout_exercises table
      final exerciseData = await _client
          .from('exercises')
          .select('name')
          .eq('id', exerciseId)
          .single();
      
      final exerciseName = exerciseData['name'] as String;

      // Prepare arrays with proper types for PostgreSQL
      final weightsArray = weightsBySet ?? List.filled(repsBySet.length, 0.0);
      
      // PostgreSQL expects bigint arrays, so convert properly
      final repsArrayForDb = repsBySet; // Keep as integers
      final weightsArrayForDb = weightsArray.map((w) => w.toInt()).toList(); // Convert to integers
      
      developer.log('Creating workout exercise: $exerciseName with ${repsBySet.length} sets', name: 'WorkoutService');
      developer.log('Reps: $repsBySet, Weights: $weightsArray', name: 'WorkoutService');
      
      // Insert complete record with all data
      final insertData = {
        'workout_id': workoutId,
        'exercise_id': exerciseId,
        'name': exerciseName,
        'order_index': orderIndex,
        'rest_interval': restTimeSeconds,
        'sets': repsBySet.length,
        'reps': repsArrayForDb,
        'weight': weightsArrayForDb,
      };
      
      final weRow = await _client
          .from('workout_exercises')
          .insert(insertData)
          .select('id')
          .single();
      
      final workoutExerciseId = weRow['id']?.toString();
      if (workoutExerciseId == null) {
        developer.log('❌ Failed to get workout exercise ID', name: 'WorkoutService');
        return false;
      }
      
      developer.log('✅ Workout exercise created successfully: $workoutExerciseId', name: 'WorkoutService');
      return true;
      
    } catch (e, st) {
      developer.log('❌ addExerciseToWorkout error: $e', name: 'WorkoutService');
      developer.log('❌ Error type: ${e.runtimeType}', name: 'WorkoutService');
      if (e.toString().contains('PostgrestException')) {
        developer.log('❌ Supabase error details: $e', name: 'WorkoutService');
      }
      developer.log('📍 Stack trace: $st', name: 'WorkoutService');
      return false;
    }
  }

  /// Delete an entire workout and all associated data.
  /// Manually handles cascading deletes for all related tables.
  Future<bool> deleteWorkout(String workoutId) async {
    developer.log('🗑️ deleteWorkout($workoutId)', name: 'WorkoutService');
    try {
      // Verify the workout exists and belongs to the current user
      final workoutCheck = await _client
          .from('workouts')
          .select('id, user_id')
          .eq('id', workoutId)
          .maybeSingle();
      
      if (workoutCheck == null) {
        developer.log('❌ Workout $workoutId not found or not accessible', name: 'WorkoutService');
        return false;
      }
      
      developer.log('✅ Workout $workoutId verified, proceeding with deletion', name: 'WorkoutService');
      
      // Delete in order to respect foreign key constraints
      
      // 1. First get all workout_log IDs for this workout
      developer.log('Step 1: Getting workout logs for workout $workoutId', name: 'WorkoutService');
      final workoutLogs = await _client
          .from('workout_logs')
          .select('id')
          .eq('workout_id', workoutId);
      developer.log('Found ${workoutLogs.length} workout logs', name: 'WorkoutService');
      
      // 2. Delete workout_set_logs that reference the workout_logs
      for (final log in workoutLogs) {
        final logId = log['id'];
        developer.log('Step 2: Deleting workout_set_logs for log $logId', name: 'WorkoutService');
        try {
          await _client.from('workout_set_logs').delete().eq('workout_log_id', logId);
        } catch (e) {
          developer.log('⚠️ Failed to delete workout_set_logs for log $logId: $e', name: 'WorkoutService');
        }
      }
      
      // 3. Delete completed_sets that reference this workout
      developer.log('Step 3: Deleting completed_sets for workout $workoutId', name: 'WorkoutService');
      try {
        await _client.from('completed_sets').delete().eq('workout_id', workoutId);
      } catch (e) {
        developer.log('⚠️ Failed to delete completed_sets: $e', name: 'WorkoutService');
      }
      
      // 4. Delete workout_exercises that reference this workout
      developer.log('Step 4: Deleting workout_exercises for workout $workoutId', name: 'WorkoutService');
      try {
        await _client.from('workout_exercises').delete().eq('workout_id', workoutId);
      } catch (e) {
        developer.log('⚠️ Failed to delete workout_exercises: $e', name: 'WorkoutService');
      }
      
      // 5. Delete completed_workouts that reference this workout
      developer.log('Step 5: Deleting completed_workouts for workout $workoutId', name: 'WorkoutService');
      try {
        await _client.from('completed_workouts').delete().eq('workout_id', workoutId);
      } catch (e) {
        developer.log('⚠️ Failed to delete completed_workouts: $e', name: 'WorkoutService');
      }
      
      // 6. Delete workout_logs that reference this workout (now safe)
      developer.log('Step 6: Deleting workout_logs for workout $workoutId', name: 'WorkoutService');
      try {
        await _client.from('workout_logs').delete().eq('workout_id', workoutId);
      } catch (e) {
        developer.log('⚠️ Failed to delete workout_logs: $e', name: 'WorkoutService');
      }
      
      // 7. Finally delete the workout itself
      developer.log('Step 7: Deleting workout $workoutId', name: 'WorkoutService');
      try {
        await _client.from('workouts').delete().eq('id', workoutId);
        developer.log('✅ Successfully deleted workout $workoutId', name: 'WorkoutService');
        return true;
      } catch (e) {
        developer.log('❌ Failed to delete workout itself: $e', name: 'WorkoutService');
        return false;
      }
      
    } catch (e, st) {
      developer.log('❌ deleteWorkout error: $e', name: 'WorkoutService');
      developer.log('📍 $st', name: 'WorkoutService');
      return false;
    }
  }

  /// Complete the current active workout and trigger next workout generation
  Future<bool> completeWorkout({
    required String workoutId,
    required String userId,
    int? duration,
    int? caloriesBurned,
    int? rating,
    String? feedback,
  }) async {
    developer.log('✅ completeWorkout($workoutId)', name: 'WorkoutService');
    try {
      // 1. Mark workout as completed
      await _client
          .from('workouts')
          .update({
            'is_completed': true,
            'is_active': false,
            'end_time': DateTime.now().toUtc().toIso8601String(),
            if (duration != null) 'duration': duration,
          })
          .eq('id', workoutId);

      // 2. Log the completed workout
      await _client
          .from('completed_workouts')
          .insert({
            'workout_id': workoutId,
            'user_id': userId,
            'date_completed': DateTime.now().toUtc().toIso8601String(),
            if (duration != null) 'duration': duration,
            if (caloriesBurned != null) 'calories_burned': caloriesBurned,
            if (rating != null) 'rating': rating,
            if (feedback != null) 'user_feedback_completed_workout': feedback,
          });

      developer.log('✅ Workout completed and logged', name: 'WorkoutService');
      
      // 3. Trigger next workout generation (this would call your n8n webhook)
      await _triggerNextWorkoutGeneration(userId, workoutId);
      
      return true;
    } catch (e, st) {
      developer.log('❌ completeWorkout error: $e', name: 'WorkoutService');
      developer.log('📍 $st', name: 'WorkoutService');
      return false;
    }
  }

  /// Trigger the generation of the next workout via n8n webhook
  Future<void> _triggerNextWorkoutGeneration(String userId, String completedWorkoutId) async {
    developer.log('🤖 Triggering next workout generation for user $userId', name: 'WorkoutService');
    try {
      // Use the same n8n webhook as the home screen
      final url = Uri.parse('https://sciwell.app.n8n.cloud/webhook/f468ffaf-259b-439d-acee-23906b9716eb');
      final nowIso = DateTime.now().toUtc().toIso8601String();
      
      // Get the completed workout details
      final completedWorkout = await getWorkoutById(completedWorkoutId);
      
      final payloadItem = <String, dynamic>{
        'user_id': userId,
        'action': 'workout_completed',
        'completed_workout_id': completedWorkoutId,
        'workout_id': completedWorkoutId,
        'workout_name': completedWorkout?.name ?? 'Completed Workout',
        'workout_date': nowIso,
        'planned_workout': {
          'workout_name': completedWorkout?.name ?? 'Completed Workout',
          'exercises': completedWorkout?.exercises.map((e) => {
            'name': e.exercise.name,
            'sets': e.sets.length,
            'reps': e.sets.map((s) => s.reps).toList(),
          }).toList() ?? [],
        },
        'actual_workout': {
          'workout_name': completedWorkout?.name ?? 'Completed Workout',
          'exercises': completedWorkout?.exercises.map((e) => {
            'name': e.exercise.name,
            'sets': e.sets.length,
            'reps': e.sets.map((s) => s.reps).toList(),
          }).toList() ?? [],
        },
        'feedback': 'Workout completed successfully',
        'additional_metrics': {
          'duration': completedWorkout?.duration ?? 0,
          'calories_burned': completedWorkout?.caloriesBurn ?? 0,
        },
      };
      
      developer.log('🔄 Sending workout completion to n8n: $payloadItem', name: 'WorkoutService');
      
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode([payloadItem]),
      );
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        developer.log('✅ Successfully triggered next workout generation', name: 'WorkoutService');
      } else {
        developer.log('❌ n8n webhook failed: ${response.statusCode} - ${response.body}', name: 'WorkoutService');
      }
      
    } catch (e) {
      developer.log('⚠️ Failed to trigger next workout generation: $e', name: 'WorkoutService');
    }
  }

  /// Test the n8n webhook integration
  Future<bool> testN8nWebhook(String userId) async {
    developer.log('🧪 Testing n8n webhook integration', name: 'WorkoutService');
    try {
      final url = Uri.parse('https://sciwell.app.n8n.cloud/webhook/f468ffaf-259b-439d-acee-23906b9716eb');
      final nowIso = DateTime.now().toUtc().toIso8601String();
      
      final testPayload = <String, dynamic>{
        'user_id': userId,
        'action': 'test_webhook',
        'workout_id': 'test-workout-id',
        'workout_name': 'Test Workout',
        'workout_date': nowIso,
        'planned_workout': {
          'workout_name': 'Test Workout',
          'exercises': [
            {
              'name': 'Test Exercise',
              'sets': 3,
              'reps': [10, 10, 10],
            }
          ],
        },
        'actual_workout': {
          'workout_name': 'Test Workout',
          'exercises': [
            {
              'name': 'Test Exercise',
              'sets': 3,
              'reps': [10, 10, 10],
            }
          ],
        },
        'feedback': 'Test webhook call',
        'additional_metrics': {
          'duration': 30,
          'calories_burned': 200,
        },
      };
      
      developer.log('🔄 Sending test payload to n8n: $testPayload', name: 'WorkoutService');
      
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode([testPayload]),
      );
      
      developer.log('📡 n8n Response: ${response.statusCode} - ${response.body}', name: 'WorkoutService');
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        developer.log('✅ n8n webhook test successful', name: 'WorkoutService');
        return true;
      } else {
        developer.log('❌ n8n webhook test failed: ${response.statusCode}', name: 'WorkoutService');
        return false;
      }
      
    } catch (e) {
      developer.log('⚠️ n8n webhook test error: $e', name: 'WorkoutService');
      return false;
    }
  }
}
