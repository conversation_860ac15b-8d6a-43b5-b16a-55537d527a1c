import 'dart:developer' as developer;
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';

class AuthService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Get current user session
  User? get currentUser => _supabase.auth.currentUser;
  
  // Check if user is signed in
  bool get isSignedIn => currentUser != null;
  
  // Get auth state changes stream
  Stream<AuthState> get authStateChanges => _supabase.auth.onAuthStateChange;

  // Sign up with email and password
  Future<AuthResponse> signUp({
    required String email,
    required String password,
    String? displayName,
  }) async {
    developer.log('📝 AuthService.signUp() called', name: 'AuthService');
    developer.log('  📧 Email: ${email.isNotEmpty ? 'provided' : 'empty'}', name: 'AuthService');
    developer.log('  🔑 Password: ${password.isNotEmpty ? 'provided (${password.length} chars)' : 'empty'}', name: 'AuthService');
    developer.log('  👤 Display name: ${displayName ?? 'not provided'}', name: 'AuthService');
    
    try {
      developer.log('  🚀 Attempting Supabase signUp...', name: 'AuthService');
      
      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
      );
      
      developer.log('  ✅ Supabase signUp completed', name: 'AuthService');
      developer.log('  👤 User: ${response.user != null ? 'created' : 'null'}', name: 'AuthService');

      // If sign up successful and user exists, create profile
      if (response.user != null) {
        developer.log('  📝 Creating user profile...', name: 'AuthService');
        await _createUserProfile(
          userId: response.user!.id,
          email: email,
          displayName: displayName,
        );
        developer.log('  ✅ User profile created', name: 'AuthService');
      }

      developer.log('📝 AuthService.signUp() completed successfully', name: 'AuthService');
      return response;
    } catch (e, stackTrace) {
      developer.log('❌ AuthService.signUp() failed', name: 'AuthService');
      developer.log('  🔴 Error: ${e.toString()}', name: 'AuthService');
      developer.log('  🔴 Error type: ${e.runtimeType}', name: 'AuthService');
      developer.log('  📍 Stack trace: ${stackTrace.toString()}', name: 'AuthService');
      rethrow;
    }
  }

  // Sign in with email and password
  Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    developer.log('🔐 AuthService.signIn() called', name: 'AuthService');
    developer.log('  📧 Email: ${email.isNotEmpty ? 'provided' : 'empty'}', name: 'AuthService');
    developer.log('  🔑 Password: ${password.isNotEmpty ? 'provided (${password.length} chars)' : 'empty'}', name: 'AuthService');
    
    try {
      developer.log('  🚀 Attempting Supabase signInWithPassword with 30s timeout...', name: 'AuthService');
      
      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          developer.log('  ⏰ TIMEOUT: Supabase signIn took longer than 30 seconds', name: 'AuthService');
          throw Exception('Sign in request timed out. Please check your internet connection and try again.');
        },
      );
      
      developer.log('  ✅ Supabase signInWithPassword completed', name: 'AuthService');
      developer.log('  👤 User: ${response.user != null ? 'authenticated' : 'null'}', name: 'AuthService');
      developer.log('  🎫 Session: ${response.session != null ? 'created' : 'null'}', name: 'AuthService');
      
      if (response.user != null) {
        developer.log('  🆔 User ID: ${response.user!.id}', name: 'AuthService');
        developer.log('  📧 User Email: ${response.user!.email}', name: 'AuthService');
      }
      
      developer.log('🔐 AuthService.signIn() completed successfully', name: 'AuthService');
      return response;
    } catch (e, stackTrace) {
      developer.log('❌ AuthService.signIn() failed', name: 'AuthService');
      developer.log('  🔴 Error: ${e.toString()}', name: 'AuthService');
      developer.log('  🔴 Error type: ${e.runtimeType}', name: 'AuthService');
      developer.log('  📍 Stack trace: ${stackTrace.toString()}', name: 'AuthService');
      rethrow;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _supabase.auth.signOut();
    } catch (e) {
      rethrow;
    }
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _supabase.auth.resetPasswordForEmail(email);
    } catch (e) {
      rethrow;
    }
  }

  // Get current user profile
  Future<UserModel?> getCurrentUserProfile() async {
    developer.log('👤 AuthService.getCurrentUserProfile() called', name: 'AuthService');
    
    try {
      if (!isSignedIn) {
        developer.log('  ❌ User not signed in', name: 'AuthService');
        return null;
      }

      developer.log('  🆔 Current user ID: ${currentUser!.id}', name: 'AuthService');
      developer.log('  🔍 Fetching profile from database...', name: 'AuthService');

      final response = await _supabase
          .from('profiles')
          .select()
          .eq('id', currentUser!.id)
          .single();

      developer.log('  ✅ Profile data fetched successfully', name: 'AuthService');
      developer.log('  📊 Profile data keys: ${response.keys.toList()}', name: 'AuthService');

      final userModel = UserModel.fromSupabase(response);
      developer.log('  ✅ UserModel created successfully', name: 'AuthService');
      developer.log('👤 AuthService.getCurrentUserProfile() completed successfully', name: 'AuthService');
      
      return userModel;
    } catch (e, stackTrace) {
      developer.log('❌ AuthService.getCurrentUserProfile() failed', name: 'AuthService');
      developer.log('  🔴 Error: ${e.toString()}', name: 'AuthService');
      developer.log('  🔴 Error type: ${e.runtimeType}', name: 'AuthService');
      developer.log('  📍 Stack trace: ${stackTrace.toString()}', name: 'AuthService');
      return null;
    }
  }

  // Update user profile
  Future<UserModel?> updateUserProfile(Map<String, dynamic> updates) async {
    try {
      if (!isSignedIn) throw Exception('User not signed in');

      // Add updated_at timestamp
      updates['updated_at'] = DateTime.now().toIso8601String();

      final response = await _supabase
          .from('profiles')
          .update(updates)
          .eq('id', currentUser!.id)
          .select()
          .single();

      return UserModel.fromSupabase(response);
    } catch (e) {
      rethrow;
    }
  }

  // Create user profile (called after sign up)
  Future<void> _createUserProfile({
    required String userId,
    required String email,
    String? displayName,
  }) async {
    try {
      final now = DateTime.now().toIso8601String();
      
      await _supabase.from('profiles').insert({
        'id': userId,
        'email': email,
        'display_name': displayName ?? email.split('@')[0],
        'created_at': now,
        'updated_at': now,
        'onboarding_completed': false,
        'fitness_assessment_completed': false,
        'has_completed_preferences': false,
        'height_unit': 'cm',
        'weight_unit': 'kg',
      });
    } catch (e) {
      // Profile might already exist, ignore error
      return;
    }
  }

  // Complete onboarding - update profile with onboarding data
  Future<UserModel?> completeOnboarding(Map<String, dynamic> onboardingData) async {
    try {
      if (!isSignedIn) throw Exception('User not signed in');

      // Mark onboarding as completed
      onboardingData['onboarding_completed'] = true;
      onboardingData['has_completed_preferences'] = true;
      onboardingData['updated_at'] = DateTime.now().toIso8601String();

      final response = await _supabase
          .from('profiles')
          .update(onboardingData)
          .eq('id', currentUser!.id)
          .select()
          .single();

      return UserModel.fromSupabase(response);
    } catch (e) {
      rethrow;
    }
  }

  // Check if user has completed onboarding
  Future<bool> hasCompletedOnboarding() async {
    try {
      final profile = await getCurrentUserProfile();
      return profile?.onboardingCompleted ?? false;
    } catch (e) {
      return false;
    }
  }
}