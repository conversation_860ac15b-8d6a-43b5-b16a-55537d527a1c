import 'package:supabase_flutter/supabase_flutter.dart';

void main() async {
  print('🧪 Testing simple workout creation...');
  
  // Initialize Supabase
  await Supabase.initialize(
    url: 'https://ixqhqvvskdwjnxevqgvl.supabase.co',
    anon<PERSON>ey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml4cWhxdnZza2R3am54ZXZxZ3ZsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0NzI4NzQsImV4cCI6MjA1MDA0ODg3NH0.Ej7Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8',
  );
  
  final client = Supabase.instance.client;
  
  try {
    // Test creating a simple workout
    final testUserId = 'test-user-${DateTime.now().millisecondsSinceEpoch}';
    
    print('Creating workout for user: $testUserId');
    
    final workoutRes = await client
        .from('workouts')
        .insert({
          'user_id': testUserId,
          'name': 'Simple Test Workout',
          'duration': 30,
          'is_active': true,
        })
        .select('id')
        .single();
    
    final workoutId = workoutRes['id'] as String;
    print('✅ Created workout: $workoutId');
    
    // Add a simple exercise
    await client
        .from('workout_exercises')
        .insert({
          'workout_id': workoutId,
          'exercise_id': '1', // Use a simple ID
          'name': 'Push-ups',
          'sets': 3,
          'reps': [10, 10, 10],
          'weight': [0, 0, 0],
          'rest_interval': 60,
          'order_index': 0,
        });
    
    print('✅ Added exercise to workout');
    
    // Verify the workout
    final verifyRes = await client
        .from('workout_exercises')
        .select('*')
        .eq('workout_id', workoutId);
    
    print('✅ Verification: ${verifyRes.length} exercises found');
    print('Exercise data: ${verifyRes.first}');
    
    print('🎉 Simple workout test passed!');
    
  } catch (e) {
    print('❌ Test failed: $e');
  }
}