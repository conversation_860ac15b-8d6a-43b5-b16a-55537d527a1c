import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:openfitv4/config/supabase_config.dart';
import 'package:openfitv4/services/workout_service.dart';

/// Simple test to verify workout creation works
void main() async {
  print('🧪 Testing workout creation...');
  
  // Initialize Supabase
  await Supabase.initialize(
    url: SupabaseConfig.supabaseUrl,
    anonKey: SupabaseConfig.supabaseAnonKey,
  );
  
  final client = Supabase.instance.client;
  final workoutService = WorkoutService(client);
  
  try {
    // Get a test user ID
    final users = await client.from('profiles').select('id').limit(1);
    if (users.isEmpty) {
      print('❌ No users found in database');
      return;
    }
    
    final userId = users.first['id'] as String;
    print('Using test user: $userId');
    
    // Create a test workout
    final workoutId = await workoutService.createWorkout(
      userId: userId,
      name: 'Test Workout - ${DateTime.now().millisecondsSinceEpoch}',
      aiDescription: 'Test workout created by test script',
      duration: 30,
    );
    
    if (workoutId == null) {
      print('❌ Failed to create test workout');
      return;
    }
    
    print('✅ Created test workout: $workoutId');
    
    // Get some exercise IDs from the database
    final exerciseIds = await client
        .from('exercises')
        .select('id, name')
        .limit(3);
    
    if (exerciseIds.isEmpty) {
      print('❌ No exercises found in database');
      return;
    }
    
    // Add test exercises
    for (int i = 0; i < exerciseIds.length; i++) {
      final exerciseId = exerciseIds[i]['id'] as String;
      final exerciseName = exerciseIds[i]['name'] as String;
      
      final success = await workoutService.addExerciseToWorkout(
        workoutId: workoutId,
        exerciseId: exerciseId,
        orderIndex: i,
        repsBySet: [12, 10, 8],
        weightsBySet: [20.0, 25.0, 30.0],
      );
      
      if (success) {
        print('✅ Added exercise: $exerciseName');
      } else {
        print('❌ Failed to add exercise: $exerciseName');
      }
    }
    
    // Test loading the workout
    final loadedWorkout = await workoutService.getWorkoutById(workoutId);
    if (loadedWorkout != null) {
      print('✅ Successfully loaded test workout: ${loadedWorkout.name}');
      print('   Exercises: ${loadedWorkout.exercises.length}');
      for (final ex in loadedWorkout.exercises) {
        print('   - ${ex.exercise.name}: ${ex.sets.length} sets');
        for (int i = 0; i < ex.sets.length; i++) {
          final set = ex.sets[i];
          print('     Set ${i + 1}: ${set.reps} reps @ ${set.weight ?? 0}kg');
        }
      }
    } else {
      print('❌ Failed to load test workout');
    }
    
    print('✅ Workout creation test completed successfully!');
  } catch (e) {
    print('❌ Error during workout creation test: $e');
  }
}