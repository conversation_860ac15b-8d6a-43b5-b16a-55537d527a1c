{"name": "My workflow 3", "nodes": [{"parameters": {"httpMethod": "POST", "path": "f468ffaf-259b-439d-acee-23906b9716eb", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-14380, -1220], "id": "dcd6e810-a918-47b5-957f-d52d44992830", "name": "Webhook", "webhookId": "f468ffaf-259b-439d-acee-23906b9716eb"}, {"parameters": {"model": {"__rl": true, "value": "o4-mini", "mode": "list", "cachedResultName": "o4-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-9020, -900], "id": "dc9aac7b-0daf-4f06-a12a-ad0bb2c30769", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "xfh3lF6nh3CROek5", "name": "OpenAi account 2"}}}, {"parameters": {"promptType": "define", "text": "=I am providing you with several pieces of information to help you generate the next personalized workout plan. Please use all of the following inputs:\n###\nResearched Fitness Guide that is made based on user preferences and you should pay detailed attention to as a reference for determining the next workout:\n{{ $json.fitness_guide }}\n###\nJust-Finished Workout AI Summary:\n{{ $json.just_finished_workout_ai_summary }}\n###\nPrevious Workout Summaries (with Dates, pay attention to dates for comntext of what user has done before the just finsihed workout and what makes sense the next workout to be based on these):\n{{ $json.previous_workout_summaries_and_dates }}\n###\nUser Preferences and Goals:\n{{ $json.user_preferences }}\n\n###\nBased on these inputs, please generate the next workout plan in JSON format using the structure given to you in system prompt:\n\nThe JSON should have a top-level property \"next_workout\" which details:\n\nworkout_name (e.g., \"Full Body Strength Progression\"),\n\nan exercises array with objects for each exercise. Each exercise object should include:\n\nname (e.g., \"Bench Press\"),\n\nsets (an integer),\n\nreps (an array of integers, one per set),\n\nweight (an array of numbers, one per set),\n\nrest_interval (in seconds),\n\norder_index (an integer for ordering excercises in the workout session).\n\nThe JSON should also include a \"workout_rationale\" property. This should be a narrative explanation detailing why you selected each exercise and the rationale behind the specific rep/weight/rest recommendations. Explain how this plan addresses the user’s goals, preferences, and previous performance trends as outlined in the provided inputs.", "hasOutputParser": true, "options": {"systemMessage": "=You are a highly advanced, personalized fitness coach AI. Your objective is to generate the next workout plan for a user using the following inputs given to you:\n- Detailed summaries of the user's recent workouts (1–3 sessions), which include performance metrics, planned vs. actual performance, and feedback.\n- The user’s preferences and training goals (e.g., strength maximization with a focus on reaching failure, progressive overload, balanced recovery, etc.).\n- A research-based fitness guide for the user that outlines a detailed guide for the user that you must also use as a reference in determining what the best next workout is based on user's goals and so far progress and previous workouts. \n\nYour Task:\n\n1. Analyze the Inputs:\n   - Evaluate the recent workout summaries to identify performance trends, areas of fatigue, and exercises that need adjustment. The name of the excercise you provide must exactly match the name of the excercise on the list below.\n   - Consider the user’s training goals and preferences. For example, if the user aims to reach muscular failure safely, ensure the plan incorporates strategies (e.g., slight weight reductions or rep adjustments) that help achieve this without compromising form.\n   - Reference the research-based guide to support your recommendations, ensuring your plan aligns with best practices (such as appropriate rest intervals, progressive overload, and recovery management).\n\n2. Generate the Next Workout:\n   - Choose exercises that complement the user’s past performance and training goals from the excercise list below.\n   - For each exercise, determine:\n     - Sets: The number of sets to perform.\n     - Reps: A dynamic array that details the planned repetitions for each set. This allows for variations between sets (e.g., a pyramid or reverse pyramid scheme).\n     - Weight: A corresponding dynamic array for the weight to be used in each set. Ensure the arrays for reps and weight are the same length as the number of sets.\n     - Rest Interval: The recommended rest period (in seconds) between sets, if applicable.\n     - Order Index: The order in which the exercises should be executed.\n\n3. Explain Your Recommendations:\n   - In a separate section called \\`workout_rationale\\`, provide a detailed narrative explanation covering:\n     - Why you selected each exercise.\n     - The rationale behind the chosen rep and weight schemes, including exact numbers (e.g., “reduce Bench Press weight from 135 lbs to 130 lbs for the final set to safely achieve 10 reps”).\n     - How the recommendations address previous performance issues (e.g., fatigue in the final sets, inability to reach failure, etc.).\n     - How the plan aligns with the user’s specific goals and the research-based guidelines.\n\nOutput Requirements:\n\nYour output must be a JSON object with exactly two top-level properties:\n- \\`next_workout_plan\\` – an object that details the workout plan.\n- \\`workout_rationale\\` – a text explanation of the decisions made in designing the workout.\n\nThe JSON must adhere to the following structure exactly:\n\n{\n  \"next_workout\": {\n    \"workout_name\": \"string\",         // The name of the next workout (e.g., \"Full Body Strength Progression\")\n    \"exercises\": [\n      {\n        \"name\": \"string\",             // Name of the exercise, must match the name from the excercise list below. exactly how it is written and given to  you.\n        \"sets\": \"integer\",            // Total number of sets\n        \"reps\": [ \"integer\", ... ],   // Array of planned reps per set (must match the number of sets)\n        \"weight\": [ \"number\", ... ],  // Array of planned weights per set (must match the number of sets)\n        \"rest_interval\": \"integer\",   // Recommended rest interval in seconds (optional but recommended)\n        \"order_index\": \"integer\"      // The sequence order for the exercise in the workout\n      }\n    ]\n  },\n  \"workout_rationale\": \"string\"         // A comprehensive explanation detailing the rationale behind the workout plan. It will be shown to user and it should be satisfying for the user to read and be happy with the outcome\n}\n\nAdditional Guidelines:\n- Ensure that every array (for reps and weight) correctly reflects the number of sets.\n- Be explicit: if adjustments are made (e.g., reducing weight or changing rep schemes), state the exact numbers and reasoning.\n- The rationale should be clear, concise, and actionable, serving as a complete explanation for the user with the goal of mainting user retention therefore it needs to be personlized for them and their preferences and goals and the deep research guide made for them.\n- The output should be fully self-contained so that any downstream system or human reviewer can understand the workout plan and its underlying logic without needing to reference the raw input data.\n\nExample Output JSON:\n\n{\n  \"next_workout\": {\n    \"workout_name\": \"Full Body Strength Progression\",\n    \"exercises\": [\n      {\n        \"name\": \"Barbell Bench Press\",\n        \"sets\": 3,\n        \"reps\": [10, 8, 6],\n        \"weight\": [135, 135, 130],\n        \"rest_interval\": 90,\n        \"order_index\": 1\n      },\n      {\n        \"name\": \"Chin-Up\",\n        \"sets\": 3,\n        \"reps\": [8, 8, 6],\n        \"weight\": [185, 185, 180],\n        \"rest_interval\": 120,\n        \"order_index\": 2\n      }\n    ]\n  },\n  \"workout_rationale\": \"Based on your recent performance, the Bench Press showed a drop in the final set where you achieved only 8 reps at 135 lbs. To ensure you reach failure safely, we recommend keeping the weight at 135 lbs for the first two sets and reducing it to 130 lbs for the final set, with a rep scheme of 10, 8, and 6. For Squats, while the first two sets met the target, the final set was slightly underperformed; reducing the weight from 185 lbs to 180 lbs in the final set and maintaining a consistent rep scheme of 8, 8, and 6 should help you reach failure without risking form. The rest intervals are set at 90 seconds for Bench Press and 120 seconds for Squats to allow for sufficient recovery, aligning with your preference for balanced recovery. Overall, these adjustments are based on your goal of maximizing strength by reaching failure safely, and they incorporate both your past performance trends and the research-based training guidelines.\"\n}`;\n\n\n\n### These are the list of excercies that you can choose from, make sure you use the names exactly as they are. The are in alphabetical order and therefore you have to determine the best exercises by making sure you take into consideration each exercise that closely resembles what you have decided to be the next workout for the user. In order to optimize your work, first think about what kind of exercises are best for the user and then go over each exercise in the list below to select based on which one in the list resembles closest to your determination of user’s needs.\n\n[\n  {\n    \"name\": \"Ab Wheel Rollout\"\n  },\n  {\n    \"name\": \"Alternating Barbell Split Jump\"\n  },\n  {\n    \"name\": \"Alternating Bodyweight Split Jump\"\n  },\n  {\n    \"name\": \"Alternating Dumbbell Bench Press\"\n  },\n  {\n    \"name\": \"Alternating Dumbbell Curl\"\n  },\n  {\n    \"name\": \"Alternating Dumbell Split Jump\"\n  },\n {\n    \"name\": \"Anderson Front Squat\"\n  },\n  {\n    \"name\": \"Band-Assisted Chin-Up\"\n  },\n  {\n    \"name\": \"Band-Assisted Inverted Row\"\n  },\n  {\n    \"name\": \"Band-Assisted Neutral-Grip Pull-Up\"\n  },\n  {\n    \"name\": \"Band-Assisted Pull-Up\"\n  },\n  {\n    \"name\": \"Band-Assisted Pushup\"\n  },\n  {\n    \"name\": \"Banded Curl\"\n  },\n  {\n    \"name\": \"Banded External Rotation at 90 Degrees Abduction\"\n  },\n  {\n    \"name\": \"Banded Face Pull\"\n  },\n  {\n    \"name\": \"Banded Hip Extension\"\n  },\n  {\n    \"name\": \"Banded No Money\"\n  },\n  {\n    \"name\": \"Banded Pull-Down\"\n  },\n  {\n    \"name\": \"Band Press-Down\"\n  },\n  {\n    \"name\": \"Band Pull-Apart\"\n  },\n  {\n    \"name\": \"Band-Resisted Glute Bridge\"\n  },\n  {\n    \"name\": \"Band-Resisted Pushup\"\n  },\n  {\n    \"name\": \"Band-Resisted Squat\"\n  },\n  {\n    \"name\": \"Barbell Back Squat\"\n  },\n  {\n    \"name\": \"Barbell Bench Press\"\n  },\n  {\n    \"name\": \"Barbell Box Squat\"\n  },\n  {\n    \"name\": \"Barbell Curl\"\n  },\n  {\n    \"name\": \"Barbell Deadlift\"\n  },\n  {\n    \"name\": \"Barbell Front Squat\"\n  },\n  {\n    \"name\": \"Barbell Glute Bridge\"\n  },\n  {\n    \"name\": \"Barbell Hip Thrust\"\n  },\n  {\n    \"name\": \"Barbell Overhead Shrug\"\n  },\n  {\n    \"name\": \"Barbell Push Press\"\n  },\n  {\n    \"name\": \"Barbell Reverse Lunge\"\n  },\n  {\n    \"name\": \"Barbell Reverse Lunge With a Front Squat Grip\"\n  },\n  {\n    \"name\": \"Barbell Romanian Deadlift\"\n  },\n {\n    \"name\": \"Barbell Split Squat\"\n  },\n  {\n    \"name\": \"Barbell Sumo Deadlift\"\n  },\n  {\n    \"name\": \"Bear Crawl\"\n  },\n  {\n    \"name\": \"Bent-Over Dumbbell Row\"\n  },\n  {\n    \"name\": \"Bird Dog\"\n  },\n  {\n    \"name\": \"Bodyweight Cross-Over Step-Up\"\n  },\n  {\n    \"name\": \"Bodyweight Get-Up\"\n  },\n  {\n    \"name\": \"Bodyweight Lateral Squat\"\n  },\n  {\n    \"name\": \"Bodyweight Squat Thrust\"\n  },\n  {\n    \"name\": \"Bodyweight Squat to Box\"\n  },\n  {\n    \"name\": \"Bodyweight Step-Up\"\n  },\n  {\n    \"name\": \"Brady Band Series\"\n  },\n  {\n    \"name\": \"Brady Band Series - Without Band\"\n  },\n  {\n    \"name\": \"Burpee\"\n  },\n  {\n    \"name\": \"Burpee Without Pushup\"\n  },\n  {\n    \"name\": \"Cable External Rotation at 30 Degrees Abduction\"\n  },\n  {\n    \"name\": \"Cable External Rotation at 90 Degrees Abduction\"\n  },\n  {\n    \"name\": \"Cable Pull-Down\"\n  },\n  {\n    \"name\": \"Chest-Supported Dumbbell Row\"\n  },\n  {\n    \"name\": \"Chin-Up\"\n  },\n  {\n    \"name\": \"Close-Grip Barbell Bench Press\"\n  },\n  {\n    \"name\": \"Close-Grip Pushup\"\n  },\n  {\n    \"name\": \"Dead Bug\"\n  },\n  {\n    \"name\": \"Dead Bug With Legs Only\"\n  },\n  {\n    \"name\": \"Deep Neck Flexor Activation and Suboccipital Stretch\"\n  },\n  {\n    \"name\": \"Dragon Flag\"\n  },\n  {\n    \"name\": \"Dumbbell Bench Press\"\n  },\n  {\n    \"name\": \"Dumbbell Cross-Over Step-Up\"\n  },\n  {\n    \"name\": \"Dumbbell Curl\"\n  },\n  {\n    \"name\": \"Dumbbell External Rotation on Knee\"\n  },\n  {\n    \"name\": \"Dumbbell Floor Press\"\n  },\n  {\n    \"name\": \"Dumbbell Full Squat\"\n  },\n  {\n    \"name\": \"Dumbbell Hammer Curl\"\n  }\n{\n    \"name\": \"Dumbbell Overhead Shrug\"\n  },\n  {\n    \"name\": \"Dumbbell Push Press\"\n  },\n  {\n    \"name\": \"Dumbbell Reverse Lunge\"\n  },\n  {\n    \"name\": \"Dumbbell Reverse Lunge to Romanian Deadlift\"\n  },\n  {\n    \"name\": \"Dumbbell Romanian Deadlift\"\n  },\n  {\n    \"name\": \"Dumbbell Split Squat\"\n  },\n  {\n    \"name\": \"Dumbbell Squat Thrust\"\n  },\n  {\n    \"name\": \"Dumbbell Step-Up\"\n  },\n  {\n    \"name\": \"Dumbbell Sumo Deadlift\"\n  },\n  {\n    \"name\": \"Dynamic Blackburn\"\n  },\n  {\n    \"name\": \"Eccentric Chin-Up\"\n  },\n  {\n    \"name\": \"Eccentric Pull-Up\"\n  },\n  {\n    \"name\": \"Explosive Pushup\"\n  },\n  {\n    \"name\": \"Face Pull\"\n  },\n  {\n    \"name\": \"Feet-Elevated Band-Resisted Pushup\"\n  },\n  {\n    \"name\": \"Feet-Elevated Pushup\"\n  },\n  {\n    \"name\": \"Feet-Elevated Pushup to Single-Arm Support\"\n  },\n  {\n    \"name\": \"Forearm Wall-Slide at 135 Degrees\"\n  },\n  {\n    \"name\": \"Goblet Lateral Lunge\"\n  },\n  {\n    \"name\": \"Goblet Lateral Lunge Walk\"\n  },\n  {\n    \"name\": \"Goblet Lateral Squat\"\n  },\n  {\n    \"name\": \"Goblet Lunge\"\n  },\n  {\n    \"name\": \"Goblet Reverse Lunge\"\n  },\n  {\n    \"name\": \"Goblet Split Squat\"\n  },\n  {\n    \"name\": \"Goblet Squat\"\n  },\n {\n    \"name\": \"Goblet Squat to Box\"\n  },\n  {\n    \"name\": \"Goblet Step-Up\"\n  },\n  {\n    \"name\": \"Half-Kneeling Band Chop\"\n  },\n  {\n    \"name\": \"Half-Kneeling Band Lift\"\n  },\n  {\n    \"name\": \"Half-Kneeling Band Overhead Shrug\"\n  },\n  {\n    \"name\": \"Half-Kneeling Cable Chop\"\n  },\n  {\n    \"name\": \"Half-Kneeling Cable Lift\"\n  },\n  {\n    \"name\": \"Half-Kneeling Pallof Press Iso\"\n  },\n  {\n    \"name\": \"Half-Kneeling Pallof Press Iso With Band\"\n  },\n  {\n    \"name\": \"Hand Cross-Over\"\n  },\n  {\n    \"name\": \"Hands-Elevated Pushup\"\n  },\n  {\n    \"name\": \"Hands-Elevated Pushup to Single-Arm Support\"\n  },\n  {\n    \"name\": \"Hanging Unilateral March\"\n  },\n  {\n    \"name\": \"Hinge to Side Plank\"\n  },\n  {\n    \"name\": \"Hip-Belt Squat\"\n  },\n  {\n    \"name\": \"Inchworm\"\n  },\n  {\n    \"name\": \"Inverted Row\"\n  },\n  {\n    \"name\": \"Inverted Row With Weight Vest\"\n  },\n  {\n    \"name\": \"Kettlebell Armbar\"\n  },\n  {\n    \"name\": \"Knees-to-Feet Drill\"\n  },\n  {\n    \"name\": \"Landmine Rainbow\"\n  },\n  {\n    \"name\": \"Long-Lever Plank\"\n  },\n  {\n    \"name\": \"Lying Dumbbell Triceps Extension\"\n  },\n  {\n    \"name\": \"Mountain Climber\"\n  },\n  {\n    \"name\": \"Neutral-Grip Cable Pull-Down\"\n  },\n  {\n    \"name\": \"Neutral-Grip Pull-Up\"\n  },\n  {\n    \"name\": \"Neutral-Grip Seated Band Row\"\n  },\n  {\n    \"name\": \"Neutral-Grip Seated Cable Row\"\n  },\n  {\n    \"name\": \"No Money Drill\"\n  },\n  {\n    \"name\": \"Overhead Band Pallof Press\"\n  },\n  {\n    \"name\": \"Overhead Band Press\"\n  },\n  {\n    \"name\": \"Overhead Band Triceps Extension\"\n  },\n  {\n    \"name\": \"Overhead Barbell Squat\"\n  },\n  {\n    \"name\": \"Overhead Cable Triceps Extension\"\n  },\n  {\n    \"name\": \"Overhead Dumbbell Reverse Lunge\"\n  },\n  {\n    \"name\": \"Pallof Press\"\n  },\n  {\n    \"name\": \"Pallof Press to Overhead\"\n  },\n  {\n    \"name\": \"Pallof Press With Band\"\n  },\n  {\n    \"name\": \"Plank\"\n  },\n  {\n    \"name\": \"Plank Arm March\"\n  },\n  {\n    \"name\": \"Plate Squat\"\n  },\n  {\n    \"name\": \"Prisoner Squat\"\n  },\n  {\n    \"name\": \"Pronated-Grip Seated Band Row\"\n  },\n  {\n    \"name\": \"Pronated-Grip Seated Cable Row\"\n  },\n  {\n    \"name\": \"Prone Hip External Rotation\"\n  },\n  {\n    \"name\": \"Prone Hip Internal Rotation\"\n  },\n  {\n    \"name\": \"Prone Row to External Rotation\"\n  },\n  {\n    \"name\": \"Prone T Raise\"\n  },\n  {\n    \"name\": \"Prone Y Raise\"\n  },\n  {\n    \"name\": \"Prone YTI\"\n  },\n  {\n    \"name\": \"Pull-Up\"\n  },\n  {\n    \"name\": \"Pull-Up With Iso\"\n  },\n  {\n    \"name\": \"Pushup\"\n  },\n  {\n    \"name\": \"Pushup Iso\"\n  },\n  {\n    \"name\": \"Pushup to Single-Arm Support\"\n  },\n  {\n    \"name\": \"Quadruped Extension-Rotation\"\n  },\n  {\n    \"name\": \"Rack Pull\"\n  },\n  {\n    \"name\": \"Reach, Rock, Lift\"\n  },\n  {\n    \"name\": \"Rear-Foot-Elevated Barbell Split Squat\"\n  },\n  {\n    \"name\": \"Rear-Foot-Elevated Bodyweight Split Squat\"\n  },\n  {\n    \"name\": \"Rear-Foot-Elevated Dumbbell Split Squat\"\n  },\n  {\n    \"name\": \"Rear-Foot-Elevated Dumbbell Split Squat Jump\"\n  },\n  {\n    \"name\": \"Rear-Foot-Elevated Goblet Split Squat\"\n  },\n  {\n    \"name\": \"Rear-Foot-Elevated Single-Arm Dumbbell Split Squat\"\n  },\n  {\n    \"name\": \"Renegade Row\"\n  },\n  {\n    \"name\": \"Renegade Row With Pushup\"\n  },\n  {\n    \"name\": \"Renegade Row With Pushup and Feet Elevated\"\n  },\n  {\n    \"name\": \"Reverse Crunch\"\n  },\n  {\n    \"name\": \"Reverse Landmine Lunge\"\n  },\n  {\n    \"name\": \"Reverse Lunge With Posterolateral Reach\"\n  },\n  {\n    \"name\": \"Reverse Pattern Single-Leg Romanian Deadlift\"\n  },\n  {\n    \"name\": \"Ring Plank\"\n  },\n  {\n    \"name\": \"Ring Pushup\"\n  },\n  {\n    \"name\": \"Ring Row\"\n  },\n  {\n    \"name\": \"Ring Row With Feet Elevated\"\n  },\n  {\n    \"name\": \"Rocked-Back Quadruped Extension-Rotation\"\n  },\n  {\n    \"name\": \"Rocking Ankle Mobilization\"\n  },\n  {\n    \"name\": \"Salute Plank\"\n  },\n  {\n    \"name\": \"Scapular Pushup\"\n  },\n  {\n    \"name\": \"Scapular Wall-Slide\"\n  },\n  {\n    \"name\": \"Seated Dumbbell Curl\"\n  },\n  {\n    \"name\": \"Seated Dumbbell Overhead Press\"\n  },\n  {\n    \"name\": \"Side-Lying Banded External Rotation With Abduction\"\n  },\n {\n    \"name\": \"Side-Lying Dumbbell External Rotation With Abduction\"\n  },\n  {\n    \"name\": \"Side-Lying Extension Rotation\"\n  },\n  {\n    \"name\": \"Side-Lying Windmill\"\n  },\n  {\n    \"name\": \"Side Plank\"\n  },\n  {\n    \"name\": \"Single-Arm Band Pull-Apart\"\n  },\n  {\n    \"name\": \"Single-Arm Band Row”\n },\n {\n    \"name\": \"Single-Arm Dumbbell Step-Up\"\n  },\n  {\n    \"name\": \"Single-Arm Half-Kneeling Band Press\"\n  },\n  {\n    \"name\": \"Single-Arm Half-Kneeling Band Pull-Down\"\n  },\n  {\n    \"name\": \"Single-Arm Plank\"\n  },\n  {\n    \"name\": \"Single-Arm Seated Overhead Dumbbell Press\"\n  },\n  {\n    \"name\": \"Single-Arm Standing Band Row\"\n  },\n  {\n    \"name\": \"Single-Arm Standing Cable Row\"\n  },\n  {\n    \"name\": \"Single-Arm Standing Split-Stance Band Press\"\n  },\n  {\n    \"name\": \"Single-Arm Standing Split-Stance Band Row\"\n  },\n  {\n    \"name\": \"Single-Arm Standing Split-Stance Cable Press\"\n  },\n  {\n    \"name\": \"Single-Arm Standing Split-Stance Cable Row\"\n  },\n  {\n    \"name\": \"Single-Arm Walking Dumbbell Farmer’s Carry\"\n  },\n {\n    \"name\": \"Single-Leg Band-Resisted Romanian Deadlift\"\n  },\n  {\n    \"name\": \"Single-Leg Barbell Glute Bridge\"\n  },\n  {\n    \"name\": \"Single-Leg Barbell Romanian Deadlift\"\n  },\n  {\n    \"name\": \"Single-Leg Dumbbell Romanian Deadlift\"\n  },\n  {\n    \"name\": \"Single-Leg Eccentric Squat to Box\"\n  },\n  {\n    \"name\": \"Single-Leg Feet-Elevated Pushup\"\n  },\n  {\n    \"name\": \"Single-Leg Glute Bridge\"\n  },\n  {\n    \"name\": \"Single-Leg Hip Thrust\"\n  },\n  {\n    \"name\": \"Single-Leg Plank\"\n  },\n  {\n    \"name\": \"Single-Leg Pushup\"\n  },\n  {\n    \"name\": \"Single-Leg Single-Arm Dumbbell Romanian Deadlift\"\n  },\n  {\n    \"name\": \"Single-Leg Squat to Box\"\n  },\n  {\n    \"name\": \"Single-Leg Supine Hips-Elevated Leg Curl\"\n  },\n  {\n    \"name\": \"Spiderman Pushup\"\n  },\n  {\n    \"name\": \"Split-Stance Dumbbell Push Press\"\n  },\n  {\n    \"name\": \"Standing Barbell Overhead Press\"\n  },\n  {\n    \"name\": \"Standing Split-Stance Landmine Press\"\n  },\n  {\n    \"name\": \"Standing Thoracic Extension Rotation\"\n  },\n  {\n    \"name\": \"Stir-The-Pot\"\n  },\n  {\n    \"name\": \"Supine Glute Bridge\"\n  },\n  {\n    \"name\": \"Supine Psoas March\"\n  },\n {\n    \"name\": \"T-Bar Row\"\n  },\n  {\n    \"name\": \"T-Pushup\"\n  },\n  {\n    \"name\": \"Trap Bar Deadlift\"\n  },\n  {\n    \"name\": \"Triceps Press-Down\"\n  },\n  {\n    \"name\": \"Turkish Get-up\"\n  },\n  {\n    \"name\": \"Walking Dumbbell Cross-Carry\"\n  },\n  {\n    \"name\": \"Walking Dumbbell Lunge\"\n  },\n  {\n    \"name\": \"Walking Farmer's Carry\"\n  },\n  {\n    \"name\": \"Walking Goblet Carry\"\n  },\n  {\n    \"name\": \"Walking Goblet Heartbeat Carry\"\n  },\n  {\n    \"name\": \"Walking Goblet Lunge\"\n  },\n  {\n    \"name\": \"Walking Knee to Chest\"\n  },\n  {\n    \"name\": \"Walking Single-Arm Bottom-Up Kettlebell Racked Carry\"\n  },\n  {\n    \"name\": \"Walking Spiderman\"\n  },\n  {\n    \"name\": \"Walking Spiderman With Overhead Reach\"\n  },\n  {\n    \"name\": \"Walking Two-Arm Waiter’s Carry\"\n  },\n  {\n    \"name\": \"Walking Waiter's Carry\"\n  },\n  {\n    \"name\": \"Walking Warrior Lunge\"\n  },\n  {\n    \"name\": \"Wall Glute Iso March\"\n  },\n  {\n    \"name\": \"Wall-Press Abs\"\n  },\n  {\n    \"name\": \"Warrior Lunge With Overhead Reach\"\n  },\n  {\n    \"name\": \"Weighted Chin-Up\"\n  },\n  {\n    \"name\": \"Weighted Neutral-Grip Pull-Up\"\n  },\n  {\n    \"name\": \"Weighted Pushup\"\n  },\n  {\n    \"name\": \"Weighted Ring Pushup\"\n  },\n  {\n    \"name\": \"X-Band Walk\"\n  }\n]\n\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-8940, -1120], "id": "829bfebd-deba-47a4-9697-e98df2af20e2", "name": "Determine the excercises for the first workout", "retryOnFail": true}, {"parameters": {"aggregate": "aggregateAllItemData", "include": "specifiedFields", "fieldsToInclude": "id, name", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-6860, -780], "id": "4e48abec-c226-4941-b3fd-2580041e4e08", "name": "Aggregate3", "notes": "For some reason the output receved by AI agent is only one item at a time so here we are combining different json outputs into one jason outputs. works like a charm so far. "}, {"parameters": {"content": "## Getting excercises IDs based on names\nHere the AI node previously provides the names which ids will be found for and then the name along with the ID will be given to create the first workout session next. have the split out and other data modifiers to make data consistent regardless of the output of AI", "height": 500, "width": 600}, "type": "n8n-nodes-base.stickyNote", "position": [-8440, -960], "typeVersion": 1, "id": "553543f0-efd8-4232-a54e-4f8e4644b8ac", "name": "Sticky Note4"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "id", "renameField": true, "outputFieldName": "workout_id"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-7700, -380], "id": "63e3f6ff-dcca-4195-a6cc-d58735aaff96", "name": "Aggregate4", "notes": "For some reason the output receved by AI agent is only one item at a time so here we are combining different json outputs into one jason outputs. works like a charm so far. "}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n  \"title\": \"Next Workout Schema\",\n  \"type\": \"object\",\n  \"properties\": {\n    \"next_workout\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"workout_name\": {\n          \"type\": \"string\",\n          \"description\": \"The name of the next workout (e.g., 'Full Body Strength Progression').\"\n        },\n        \"exercises\": {\n          \"type\": \"array\",\n          \"description\": \"List of exercises in the next workout.\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"name\": {\n                \"type\": \"string\",\n                \"description\": \"Name of the exercise (e.g., 'Bench Press').\"\n              },\n              \"sets\": {\n                \"type\": \"integer\",\n                \"description\": \"Total number of sets.\"\n              },\n              \"reps\": {\n                \"type\": \"array\",\n                \"description\": \"Array of planned reps per set.\",\n                \"items\": {\n                  \"type\": \"integer\"\n                }\n              },\n              \"weight\": {\n                \"type\": \"array\",\n                \"description\": \"Array of planned weights per set.\",\n                \"items\": {\n                  \"type\": \"number\"\n                }\n              },\n              \"rest_interval\": {\n                \"type\": \"integer\",\n                \"description\": \"Recommended rest interval in seconds.\"\n              },\n              \"order_index\": {\n                \"type\": \"integer\",\n                \"description\": \"The order in which the exercise should be executed.\"\n              }\n            },\n            \"required\": [\n              \"name\",\n              \"sets\",\n              \"reps\",\n              \"weight\",\n              \"rest_interval\",\n              \"order_index\"\n            ],\n            \"additionalProperties\": false\n          }\n        }\n      },\n      \"required\": [\n        \"workout_name\",\n        \"exercises\"\n      ],\n      \"additionalProperties\": false\n    },\n    \"workout_rationale\": {\n      \"type\": \"string\",\n      \"description\": \"A comprehensive explanation detailing the rationale behind the workout plan. this will be shown to user\"\n    }\n  },\n  \"required\": [\n    \"next_workout\",\n    \"workout_rationale\"\n  ],\n  \"additionalProperties\": false\n}\n"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [-8740, -920], "id": "da175ee0-a94c-4fa0-aa5a-bc7c8fe01f2c", "name": "Structured Output Parser"}, {"parameters": {"content": "## identifying excercises names for first workout. Probably need to include other attributes such as equipment needed for each name later on. maybe in the form of XML tags or jsons. ", "height": 280, "width": 360}, "type": "n8n-nodes-base.stickyNote", "position": [-9420, -1420], "typeVersion": 1, "id": "93ee0bc1-9696-4ab1-ba39-62e1f02bfe0e", "name": "Sticky Note8"}, {"parameters": {"fieldToSplitOut": "exercises[0], data", "include": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldsToInclude": "workout_id[0]", "options": {"destinationFieldName": "=excercises, excercise_id"}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-6160, -840], "id": "8c57bf23-fa9f-46e5-b867-e820e5af9309", "name": "Split Out3"}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-5300, -920], "id": "f5bedf29-1829-4fbe-af73-08ca76467653", "name": "Aggregate5", "notes": "For some reason the output receved by AI agent is only one item at a time so here we are combining different json outputs into one jason outputs. works like a charm so far. "}, {"parameters": {"content": "## Comments about user data input\n**Probably best to not include session time limitation and focus on overall strategy. then the time limits will be given to the exercise selector agent. \nFilter out any data relevant to each flow. better to receive all user data and filter here ", "height": 260, "width": 380}, "type": "n8n-nodes-base.stickyNote", "position": [-13800, -1360], "typeVersion": 1, "id": "269ee7b8-af2f-4156-8d0a-32e11bc5c744", "name": "Sticky Note9"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "user_id"}, {"fieldToAggregate": "workout_name"}]}, "options": {"mergeLists": false}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-8920, -340], "id": "a9e1372b-1c7f-4b9c-a533-c0557a93494c", "name": "Aggregate7", "retryOnFail": true}, {"parameters": {"content": "messsage also saying on the front end you can select alternatives in the UI or ask the chat to suggest alternatives if an equipment is unavailable. ", "height": 120, "width": 400}, "type": "n8n-nodes-base.stickyNote", "position": [-8640, -1840], "typeVersion": 1, "id": "316771bb-04a7-4cba-902b-b22a5fe6fcbc", "name": "Sticky Note11"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [-8340, -1760], "id": "a39ce706-c85a-40f9-bbc8-e66be0d0a9cb", "name": "Merge1", "retryOnFail": true, "waitBetweenTries": 100}, {"parameters": {"fieldToSplitOut": "output.next_workout.exercises", "options": {"destinationFieldName": "="}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-7620, -740], "id": "4f399579-3419-47a2-b900-b2c009c3aa42", "name": "Split Out4"}, {"parameters": {"content": "Needs more prompt engineering. also from user information other things such as user requested duration needs to be included, right now it is not ", "height": 140}, "type": "n8n-nodes-base.stickyNote", "position": [-9060, -1280], "typeVersion": 1, "id": "8108fbbb-3cd6-414f-85b6-ea1a70e6edce", "name": "Sticky Note12"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "numberInputs": 3, "options": {"includeUnpaired": true}}, "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [-6380, -820], "id": "72fd85e3-9c66-49a2-a500-1bbeb8578304", "name": "Merge3", "retryOnFail": true}, {"parameters": {"content": "Saving the user facing description. also would need to be given to ai chat if user wants to talk about their next workout or previous ones. ", "width": 300}, "type": "n8n-nodes-base.stickyNote", "position": [-8940, -1880], "typeVersion": 1, "id": "9aacaf44-748d-46df-a8af-2b2570d8ffd3", "name": "Sticky Note16"}, {"parameters": {"content": "Getting the last 3 or 4 workouts completed. \n\nIf we could have an overall input of what happened duirng each workout, such as most weights were reduced or  workout was too hard. I think best way to do it would be through an AI summarizer. Maybe also AI summarizier for onboarding agent. could do this by linking workout with sets history and then summarizing all workouts together everytime here. or could make another workflow each time a workout is complete that an AI would create a summary based on what happened with the user and save it in the worout completed table and then pull the last 3 workouts summaries and feed it here. I think that is a better thing to do. ", "height": 520}, "type": "n8n-nodes-base.stickyNote", "position": [-12740, 280], "typeVersion": 1, "id": "6615b440-8d09-4808-bcbe-b0b40d47b396", "name": "Sticky Note17"}, {"parameters": {"fieldToSplitOut": "user_id", "options": {"destinationFieldName": "user_id"}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-12600, 40], "id": "30d0d7b5-1462-46bc-a68e-e235ae32f7b5", "name": "Taking User Id2"}, {"parameters": {"content": "Recieving all the sets and workout info to create an AI asummary"}, "type": "n8n-nodes-base.stickyNote", "position": [-13580, -500], "typeVersion": 1, "id": "f7620ab9-bd42-41b6-9353-f4cefb96855e", "name": "Sticky Note13"}, {"parameters": {"operation": "update", "tableId": "completed_workouts", "matchType": "allFilters", "filters": {"conditions": [{"keyName": "workout_id", "condition": "eq", "keyValue": "={{ $json.workout_id }}"}]}, "fieldsUi": {"fieldValues": [{"fieldId": "completed_workout_summary", "fieldValue": "={{ $json.ai_summary }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-11740, -280], "id": "40a3b227-0b95-49ca-85c4-05de7bf16e9f", "name": "Supabase4", "credentials": {"supabaseApi": {"id": "uVVxVAJWIoyWMJ4R", "name": "Supabase account"}}}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [-12160, -280], "id": "a7a20ac7-d5e3-4b30-be57-deeb9d0973d5", "name": "<PERSON><PERSON>", "retryOnFail": true}, {"parameters": {"fieldToSplitOut": "workout_id", "options": {"destinationFieldName": ""}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-12300, -680], "id": "5ee64a31-1fd1-4ad3-98aa-ffb46e1c46af", "name": "Taking the just finished workout session id"}, {"parameters": {"promptType": "define", "text": "=Here is the information about the just completed workout: \nworkoutname: {{ $json.planned_workout }}, \nworkoutdate: {{ $json.workout_date }},\nplanned_workout: {{ $json.planned_workout }}, \nactual_performed_workout: {{ $json.actual_workout }}, \nuser_feedback_after_performed_workout:{{ $json.user_workout_feedback }},\nadditional_metrics: {{ $json.additional_metrics }}\n", "hasOutputParser": true, "options": {"systemMessage": "=You are an expert workout summarizer. Your task is to analyze the following JSON object, which contains raw data from a user's workout session, and generate a comprehensive, clear, and actionable summary of the session. Your summary should be written as if you were reporting to a personal trainer or coach, highlighting key insights that can inform future workout planning. The goal of this summary is for it to be used for making the next workout more accurate for the user\n\nInstructions:\n-- Act as if your life depends on completing this task perfectly. This is a very important task and many people's lviinng is dependent on it. \nOverview of the Session:\n\nStart your summary by stating the workout name (\"Full Body Strength Training\") and the workout date.\n\nInclude a brief overall assessment based on the data (e.g., general performance and key observations).\n\nMake sure to include all the details necessary for clear understanding of the perfomred workout. such as by how much planned vs actual performed excercises differ and in regards to what their weights, reps are. \n\nExplain the JSON Structure:\n\nClarify that:\n\nworkout_name appears both at the top level and within the planned and actual workout sections, confirming that both refer to the same session.\n\nworkout_date indicates when the workout was completed.\n\nplanned_workout contains the intended exercises along with planned sets, reps, and weight and rest periods between each set.\n\nactual_workout includes the detailed set-by-set performance for each exercise:\n\nEach actual_set provides the order of the set, performed reps, weight, the computed difference from the planned reps (rep_difference), and a difficulty rating (set_feedback_difficulty) which can be \"easy\", \"moderate\", or \"hard\".\n\nfeedback captures the user's overall impressions.\n\nadditional_metrics provides further details such as the duration of the workout, calories burned, a rating, and extra notes.\n\nDetailed Comparison and Analysis:\n\nCompare the planned workout versus the actual performance. Highlight where the user met the plan and where they fell short, using the rep_difference values.\n\nDiscuss the implications of the set_feedback_difficulty for each set. For example, if a set is rated \"hard\", explain that it indicates the user reached near their limit.\n\nIncorporate the overall user feedback and any notes from additional_metrics to provide context.\n\nActionable Insights:\n\nOffer suggestions or questions for further analysis that could help tailor the next workout plan.\n\nYour Final Summary Should:\n\nBe clear and engaging, providing an overall narrative of the workout.\n\nOffer a side-by-side comparison of planned vs. actual performance.\n\nHighlight actionable insights that could be used to improve future workouts.\n\nGenerate the final summary based solely on the above raw JSON data.\n\n\n###\noutput: you will output this summary in a json format under the object ai_summary. You must use data given to you exactly as they are to create this summary, do not change or rely on anything that is not part of the user data given to you. You must include all the excercises user has completed.\nhere is the json schema example as a reference for your to follow: \n{\n  // you must follow the json schema of the following but you are free to include more information in any value than suggestted bellow if you think is helpful.\n  \"ai_summary\": {\n    // [guide] The date of the workout session in YYYY-MM-DD format as exactly given to you.\n    \"date\": \"YYYY-MM-DD\",\n\n    // [guide] The title of the workout as it exactly was given to you.\n    \"workout_name\": \"Your Workout Title\",\n\n    // [guide] Introductory summary: include the session date, optional user name, and list of planned exercises with sets, reps, and weights.\n    \"intro\": \"On YYYY-MM-DD, the user (name) completed a '[Workout Title]' session. The planned workout consisted of Exercise A (X sets of Y reps at Z weight) and Exercise B (...).\",\n\n    // [guide] Detailed breakdown: for each exercise, list observations comparing planned vs. actual performance for each set.\n    \"planned_vs_actual\": {\n      \"Exercise A\": [\n        \"Sets 1 & 2: Achieved Y reps at Z weight with moderate effort.\",\n        \"Set 3: Completed X reps (describe any shortfall or additional reps), rated 'effort', noting any fatigue or ease.\"\n      ],\n      \"Exercise B\": [\n        \"Sets 1: ... \",\n        \"Set 2 ...\"\n      ]\n    },\n\n    // [guide] Overall feedback and metrics: include user-reported comments, session duration, calories burned, session rating, and general observations.\n    \"feedback_and_metrics\": \"Provide a brief summary of subjective experience, key metrics (e.g., duration, calories), session rating, and any notable observations.\",\n\n    // [guide] Actionable recommendations for the next time. Include adjustments per exercise and a general section for overall tips.\n    \"excercise_considerations_for_next_time\": {\n      \"Exercise A\": {\n        // [guide] Suggest how to adjust weight, reps, or rest for the next time.\n        \"suggestion\": \"Describe how to modify weight or reps to improve performance.\",\n        // [guide] Explain why this change will benefit the user based on observed performance.\n        \"rationale\": \"Explain the reason for the adjustment in terms of fatigue, form, or progression.\"\n      },\n      \"Exercise B\": {\n        \"suggestion\": \"Describe next-session adjustment for this exercise.\",\n        \"rationale\": \"Explain why this change is recommended.\"\n      },\n      \"General\": {\n        \"suggestion\": \"Offer general advice (e.g., longer warm-up, increased rest, nutrition tips).\",\n        \"rationale\": \"Explain why this general tip will help overall session performance.\"\n      }\n    }\n  }\n}\n\n\n###\nHere is an output example as a reference for you: \n[\n{\n\"output\": \n{\n\"ai_summary\": \n{\n\"date\": \n\"2025-04-02\",\n\"workout_name\": \n\"Full Body Strength Training\",\n\"intro\": \n\"On April 2, 2025, the user completed a 'Full Body Strength Training' session. The planned workout was designed to include Bench Press (3 sets of 10 reps at 135 lbs with 90 seconds rest between sets) and Squats (3 sets of 8 reps at 185 lbs with 120 seconds rest).\",\n\"planned_vs_actual\": \n{\n\"Bench Press\": \n[\n\"Set 1: Achieved 10 reps at 135 lbs with a 'moderate' effort, matching the plan.\",\n\"Set 2: Again, 10 reps at 135 lbs were successfully completed with 'moderate' difficulty.\",\n\"Set 3: Only 8 reps were performed at 135 lbs, falling 2 reps short. A 'hard' rating indicates the user was nearing fatigue.\"\n],\n\"Squat\": \n[\n\"Set 1: Successfully met the target with 8 reps at 185 lbs and an 'easy' difficulty.\",\n\"Set 2: Completed 8 reps at 185 lbs with a 'moderate' effort, in line with the plan.\",\n\"Set 3: Achieved 7 reps at 185 lbs, 1 rep short of the planned 8, with a 'hard' difficulty, suggesting increased challenge towards the end.\"\n]\n},\n\"feedback_and_metrics\": \n\"The user reported feeling strong on Bench Press but found Squats more challenging in the later sets. The session lasted 60 minutes, burned 450 calories, and the user’s feedback underscores a need to adjust either weight or recovery strategies for Squats in future workouts.\",\n\"next_session_recommendations\": \n{\n\"Bench Press\": \n{\n\"suggestion\": \n\"Maintain 135 lbs for sets 1 and 2. For set 3, consider reducing the weight slightly—perhaps to 130 lbs—to help achieve the full 10 reps without compromising form.\",\n\"rationale\": \n\"The 2-rep deficit in the final set along with a 'hard' difficulty rating indicates that fatigue set in. A slight reduction in weight may allow the user to complete the set as planned.\"\n},\n\"Squat\": \n{\n\"suggestion\": \n\"Continue using 185 lbs for the first two sets. For the third set, reduce the weight to around 180 lbs and consider extending the rest interval to better manage fatigue.\",\n\"rationale\": \n\"The one-rep shortfall and a 'hard' difficulty in the final set suggest that the load and recovery balance might need adjustment to fully meet the planned target.\"\n},\n\"General\": \n{\n\"suggestion\": \n\"Incorporate a longer warm-up or dynamic stretching routine prior to lifting to ensure optimal performance across all exercises.\",\n\"rationale\": \n\"Enhanced warm-up protocols could help prevent early fatigue, facilitating better adherence to the planned rep targets, especially during challenging sets.\"\n}\n}\n}\n}\n}\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-12920, -440], "id": "40e59d3a-92ac-46a2-a831-5418274d916c", "name": "Summarizing the just completed workout", "retryOnFail": true}, {"parameters": {"content": "Getting the last 2 or 3 complted workouts AI summaries to feed"}, "type": "n8n-nodes-base.stickyNote", "position": [-12460, -100], "typeVersion": 1, "id": "a7d873cc-735c-4107-92db-28394c8f222f", "name": "Sticky Note19"}, {"parameters": {"amount": 2}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-11960, -280], "id": "0bdd6aa5-fced-416a-8efb-4fc10f28b916", "name": "Wait", "webhookId": "e0d7adb9-03e1-4a54-a606-795215d4d89d"}, {"parameters": {"content": "Waiting so that when querying the previous AI workout summaries this is not included again. This summary is sepear<PERSON> directly given to next workout Agent"}, "type": "n8n-nodes-base.stickyNote", "position": [-12080, -420], "typeVersion": 1, "id": "e5dec037-c1fe-4628-97ba-983c4be4c829", "name": "Sticky Note20"}, {"parameters": {"operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "completed_workouts", "mode": "list", "cachedResultName": "completed_workouts"}, "limit": 3, "where": {"values": [{"column": "user_id", "value": "={{ $json.user_id }}"}]}, "sort": {"values": [{"column": "created_at", "direction": "DESC"}]}, "options": {"outputColumns": ["completed_workout_summary", "date_completed"]}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-11980, -20], "id": "3a9a3a06-e8c7-43d7-aa4f-39102e306f34", "name": "Postgres", "credentials": {"postgres": {"id": "PCS579lKQka6g6am", "name": "Postgres account"}}}, {"parameters": {"content": "only giving summaries since we are making summaries very detailed so no need o include raw data as well."}, "type": "n8n-nodes-base.stickyNote", "position": [-11820, -380], "typeVersion": 1, "id": "be22a840-fe21-4908-8a21-173641543cb0", "name": "Sticky Note21"}, {"parameters": {"fieldToSplitOut": "user_id", "options": {"destinationFieldName": "user_id"}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-11760, -1080], "id": "de809cf7-a962-4d7f-b8fb-adbd46021089", "name": "Taking User Id3"}, {"parameters": {"operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "profiles", "mode": "list", "cachedResultName": "profiles"}, "limit": 3, "where": {"values": [{"column": "id", "value": "={{ $json.user_id }}"}]}, "combineConditions": "=AND", "options": {"outputColumns": ["excluded_exercises", "additional_notes", "primarygoal", "fitnessgoals", "cardiolevel", "weightliftinglevel", "equipment", "workoutdays", "workoutduration", "workoutfrequency", "display_name", "age", "gender", "height", "weight", "height_unit", "weight_unit", "sport_activity", "weightlifting_level_description", "cardio_level_description"]}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-11260, -1040], "id": "fa5a183e-3c71-4650-af0e-7356e1b559c8", "name": "Postgres1", "credentials": {"postgres": {"id": "PCS579lKQka6g6am", "name": "Postgres account"}}}, {"parameters": {"fieldToSplitOut": "user_id", "options": {"destinationFieldName": "user_id"}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-11740, -1260], "id": "c2e49693-4ae5-406f-b05e-28bbfec2085b", "name": "Taking User Id6"}, {"parameters": {"operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "profiles", "mode": "list", "cachedResultName": "profiles"}, "limit": 3, "where": {"values": [{"column": "id", "value": "={{ $json.user_id }}"}]}, "combineConditions": "=AND", "options": {"outputColumns": ["fitness_guide"]}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-11280, -1260], "id": "3ef137e5-d353-426e-b2b4-07c63b36afdb", "name": "Postgres4", "credentials": {"postgres": {"id": "PCS579lKQka6g6am", "name": "Postgres account"}}}, {"parameters": {"content": "Taking fitness guide", "height": 200}, "type": "n8n-nodes-base.stickyNote", "position": [-11360, -1320], "typeVersion": 1, "id": "df292480-0cf1-4f2b-8628-5dec0a6d39e2", "name": "<PERSON><PERSON> Note26"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4", "mode": "list", "cachedResultName": "gpt-4"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-12960, -260], "id": "382c4303-2aec-4a17-84d8-608d414e54a9", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "xfh3lF6nh3CROek5", "name": "OpenAi account 2"}}}, {"parameters": {"content": "## needs further prompt engineering especially for explaining each json entery and what goes in it and for whay\n", "height": 260}, "type": "n8n-nodes-base.stickyNote", "position": [-13340, -720], "typeVersion": 1, "id": "bf73a957-01e7-4899-99d8-eb82d3731184", "name": "Sticky Note6"}, {"parameters": {"content": "two things. \nRest interval for each excercise sets is now under the workout excercises table which will allow for dynamic changes based on user performance. \n\ntwo, I think we shuld change the reps under workout excercises to be defined as array and let the AI have dynamic reps for each set so not all 3 sets in each excercise for examole would have to have the same number of reps.", "height": 340}, "type": "n8n-nodes-base.stickyNote", "position": [-9980, -1520], "typeVersion": 1, "id": "71362e96-b786-44f6-bd72-4dce9b5b8c03", "name": "Sticky Note14"}, {"parameters": {"content": "Later on might be benficial to include muscles targeted for each excercise to reduce AI geuss work. "}, "type": "n8n-nodes-base.stickyNote", "position": [-14460, -1360], "typeVersion": 1, "id": "b7242ed3-47fa-4470-a9ae-d7b23b1d6dc9", "name": "Sticky Note22"}, {"parameters": {"content": "was giving this to the summarizer but I dont think it is necessary anymore for creating the sumaries Taking user preferences relevant to making the summary for summarizer", "height": 200}, "type": "n8n-nodes-base.stickyNote", "position": [-14400, -640], "typeVersion": 1, "id": "32cd2864-578e-4c17-b274-247f9eac5531", "name": "Sticky Note24"}, {"parameters": {"assignments": {"assignments": [{"id": "22b0d1af-996f-4885-8b60-cde059237547", "name": "workout_name", "value": "={{ $json.workout_name || $json.body || $json }}", "type": "string"}, {"id": "790d7456-6397-4ee8-b7fd-1a3f16969db6", "name": "planned_workout", "value": "={{ $json.planned_workout || { workout_name: ($json.workout_name || $json.body || 'Unknown') } }}", "type": "object"}, {"id": "8840bb15-602b-4885-a742-cdab186acb01", "name": "actual_workout", "value": "={{ $json.actual_workout || { workout_name: ($json.workout_name || $json.body || 'Unknown') } }}", "type": "object"}, {"id": "38a1cb12-2022-4445-8690-f37bae9ad2bd", "name": "user_workout_feedback", "value": "={{ $json.feedback || $json.body || null }}", "type": "string"}, {"id": "8190aa43-575e-4035-9fe7-fa3e8c345ab9", "name": "additional_metrics", "value": "={{ $json.additional_metrics || ($json.duration !== undefined ? $json : {}) }}", "type": "object"}, {"id": "eb3d582b-d755-4395-84cf-d18db19825bc", "name": "workout_date", "value": "={{ $json.workout_date || $json.body || $json }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-13760, -800], "id": "c459042d-6272-4fe3-990d-e329fe7f8e60", "name": "Edit Fields2"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "output", "renameField": true, "outputFieldName": "just_finished_workout_ai_summary"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-11780, -500], "id": "9c0e021d-ff92-42be-a683-876674882474", "name": "Aggregate2"}, {"parameters": {"assignments": {"assignments": [{"id": "7fa14679-5279-4b82-ad3d-f6d54025d917", "name": "ai_summary", "value": "={{ $json.output }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-12400, -260], "id": "0814313a-2eb6-46ee-8d67-8a62f8035a79", "name": "Edit Fields4"}, {"parameters": {"operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "exercises", "mode": "list", "cachedResultName": "exercises"}, "limit": 3, "where": {"values": [{"column": "name", "condition": "LIKE", "value": "={{ $json.name }}"}]}, "combineConditions": "=AND", "options": {"outputColumns": ["id", "name"]}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-7260, -740], "id": "8a7cad11-0aee-4ae6-9775-095f4f24d3a6", "name": "Postgres3", "alwaysOutputData": false, "credentials": {"postgres": {"id": "PCS579lKQka6g6am", "name": "Postgres account"}}}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "output.next_workout.exercises"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-7760, -1140], "id": "77e233d7-6eec-4c20-a59e-0b1e77541149", "name": "Aggregate1"}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "workouts", "mode": "list", "cachedResultName": "workouts"}, "columns": {"mappingMode": "defineBelow", "value": {"user_id": "={{ $json.user_id[0] }}", "name": "={{ $json.workout_name[0] }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "user_id", "displayName": "user_id", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "name", "displayName": "name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "start_time", "displayName": "start_time", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}, {"id": "end_time", "displayName": "end_time", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}, {"id": "is_active", "displayName": "is_active", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true, "removed": true}, {"id": "duration", "displayName": "duration", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "notes", "displayName": "notes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "is_minimized", "displayName": "is_minimized", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true, "removed": true}, {"id": "last_state", "displayName": "last_state", "required": false, "defaultMatch": false, "display": true, "type": "object", "canBeUsedToMatch": true, "removed": true}, {"id": "is_completed", "displayName": "is_completed", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true, "removed": true}, {"id": "session_order", "displayName": "session_order", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"outputColumns": ["id", "name"]}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-8220, -340], "id": "5e85a456-1224-41fb-a4b4-cce5f5c92eda", "name": "Postgres5", "alwaysOutputData": false, "credentials": {"postgres": {"id": "PCS579lKQka6g6am", "name": "Postgres account"}}}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [-9200, -320], "id": "8131d718-9262-463f-8944-73e3d9181023", "name": "Merge5"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "numberInputs": 4, "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [-9700, -1100], "id": "********-63b7-4cc4-bd68-262bab8699a7", "name": "Merge6"}, {"parameters": {"content": "maybe fir 24 hour rest suggestion, there can be a fixated message saying recommending 24 hours rest period if you do not feel recovered the next day. the best indicator for going back to working our is your feeling rested and recovered without intense sourness (a bit sourness is ok)", "height": 260}, "type": "n8n-nodes-base.stickyNote", "position": [-10260, -1440], "typeVersion": 1, "id": "fd2e0520-d367-48a8-be04-aeec5b483071", "name": "Sticky Note15"}, {"parameters": {"operation": "update", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "workouts", "mode": "list", "cachedResultName": "workouts"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $json.id }}", "ai_description": "={{ $json.output.workout_rationale }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "user_id", "displayName": "user_id", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "name", "displayName": "name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "start_time", "displayName": "start_time", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}, {"id": "end_time", "displayName": "end_time", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}, {"id": "is_active", "displayName": "is_active", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true, "removed": true}, {"id": "duration", "displayName": "duration", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "notes", "displayName": "notes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "is_minimized", "displayName": "is_minimized", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true, "removed": true}, {"id": "last_state", "displayName": "last_state", "required": false, "defaultMatch": false, "display": true, "type": "object", "canBeUsedToMatch": true, "removed": true}, {"id": "is_completed", "displayName": "is_completed", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true, "removed": true}, {"id": "session_order", "displayName": "session_order", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}, {"id": "ai_description", "displayName": "ai_description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"outputColumns": ["ai_description", "name", "id"]}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-7760, -1760], "id": "f122d839-0c19-4236-9751-e1b1daf9ba7d", "name": "Postgres6", "alwaysOutputData": false, "credentials": {"postgres": {"id": "PCS579lKQka6g6am", "name": "Postgres account"}}}, {"parameters": {"aggregate": "aggregateAllItemData", "destinationFieldName": "previous_workout_summaries_and_dates", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-11360, -280], "id": "59cde7aa-3297-41f6-adbd-4ff8cade3bea", "name": "Aggregate6"}, {"parameters": {"content": "## need to create a summarizer of summarizers which will be actiaveted after 5 or 10 workouts and create a summary o f what needs to be done and how the user is performaing and analyzing what else is needed and then saving the summary and updating every 5 workouts for example. this will alllow  for  long term looking back at the user and not be limited in the past 3 summaries provided here. perhaps saving it under profile ai summary overall or something and htne having it update it vvia postgres node here with API triggering every 5 workouts for example", "height": 1340}, "type": "n8n-nodes-base.stickyNote", "position": [-12060, 120], "typeVersion": 1, "id": "82a18967-5c1a-486b-9208-d5ae084685f0", "name": "Sticky Note25"}, {"parameters": {"content": "apperantly the AI node will run multiple times if it is feeding multiple nodes. therefore putting one node in between for it to just give one output each time"}, "type": "n8n-nodes-base.stickyNote", "position": [-13080, -520], "typeVersion": 1, "id": "e023c14f-4531-4ca8-a30d-1e074b33870b", "name": "Sticky Note18"}, {"parameters": {"assignments": {"assignments": []}, "includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-12580, -440], "id": "5e389029-5245-4190-a389-2d82eb6cffaf", "name": "<PERSON>"}, {"parameters": {"assignments": {"assignments": []}, "includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-8620, -1120], "id": "72adbb1a-cae8-408a-ab45-9267fb4932a5", "name": "Edit Fields1", "notesInFlow": true, "notes": "apperantly the AI node will run multiple times if it is feeding multiple nodes. therefore putting one node in between for it to just give one output each time"}, {"parameters": {"assignments": {"assignments": [{"id": "e337371d-ef8a-44bb-a737-b0e6d492b716", "name": "user_preferences", "value": "={{ $json.user_preferences[0] }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-10540, -1020], "id": "2dee1f28-a707-49d2-beae-5e0e32a07637", "name": "Stringifying the input1"}, {"parameters": {"aggregate": "aggregateAllItemData", "destinationFieldName": "user_preferences", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-10960, -1020], "id": "5c8a8728-aea0-47ec-ae90-a87f7b6be657", "name": "Aggregate8"}, {"parameters": {"assignments": {"assignments": [{"id": "e337371d-ef8a-44bb-a737-b0e6d492b716", "name": "just_finished_workout_ai_summary", "value": "={{ $json.just_finished_workout_ai_summary }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-10840, -740], "id": "7f8e194f-8292-4196-8cb8-d7acc2514ddb", "name": "Stringifying the input2"}, {"parameters": {"assignments": {"assignments": [{"id": "e337371d-ef8a-44bb-a737-b0e6d492b716", "name": "previous_workout_summaries_and_dates", "value": "={{ $json.previous_workout_summaries_and_dates }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-10560, -680], "id": "f6a27549-89c9-46c2-b36f-2b8ee67a21ee", "name": "Stringifying the input3"}, {"parameters": {"content": "## will be using supabase or postgres functions to create teh API for when something in the table is triggered. probably also for onboarding", "height": 380}, "type": "n8n-nodes-base.stickyNote", "position": [-14840, -1400], "typeVersion": 1, "id": "109d2045-afde-40a7-b025-6695ef905308", "name": "<PERSON><PERSON>"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolCalculator", "typeVersion": 1, "position": [-8840, -900], "id": "33ceaba4-a261-45b1-834a-50b1a5fbe4b5", "name": "Calculator"}, {"parameters": {"content": "Important: Perhaps having excercise specefic history per user to feed to the AI such as last time weight and reps and average overalll. and feedback. this would need to be broiugght to AI attention to another next agent to choose the reps and weights after the first Agent chooses the excercises", "height": 280, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [-7560, -1560], "typeVersion": 1, "id": "bd9b7158-4d98-418b-951d-df0c40f7b165", "name": "Sticky Note2"}, {"parameters": {"content": "For stretches and cardios the same method could be done too that another agent would make the cardio section if it is for another day or later for that day adn same for stretch section ", "height": 280, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [-7320, -1560], "typeVersion": 1, "id": "9add029a-a333-4dfb-a549-c98ba43ce049", "name": "Sticky Note3"}, {"parameters": {"content": "Same thing for warm up and cool off agents. then in the fron end we would stitch them together.. or mayebe same workou table. idk yet. but the overall idea is that those agents will make those sections based on the workout excercises created. ", "height": 280, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [-7080, -1560], "typeVersion": 1, "id": "ca963cdd-1cd0-44c6-ae60-a505a0364f06", "name": "Sticky Note10"}, {"parameters": {"content": "in prompt engineering maybe it can be added that you can change the excercise and replace it next time if the history shows that that there is a better variation suited for the user ", "height": 140}, "type": "n8n-nodes-base.stickyNote", "position": [-8820, -1280], "typeVersion": 1, "id": "f985313e-fcf5-4e27-886e-c0e9dcc9c6c1", "name": "Sticky Note28"}, {"parameters": {"content": "we need to add a binary stating if the excercise needs weight or not such as body weight excercises so it does not show weight on front end. ", "height": 280}, "type": "n8n-nodes-base.stickyNote", "position": [-8000, -1440], "typeVersion": 1, "id": "4fd41148-84d7-4b5f-8be4-7d8802824538", "name": "Sticky Note29"}, {"parameters": {"content": "also would need at some point to make a better list of available excercises and expand and shrink when in fact we have some progress. ", "height": 180}, "type": "n8n-nodes-base.stickyNote", "position": [-9060, -1420], "typeVersion": 1, "id": "3a80b8a3-9869-40ee-bc44-9083bf89c457", "name": "Sticky Note30"}, {"parameters": {"content": "this agent or another one could also create personlized notes for each excercise instructions and also feed to live talking agent too. ", "height": 280, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [-6840, -1560], "typeVersion": 1, "id": "2d5e414e-4754-4038-9d28-39c0fe3d3ba8", "name": "Sticky Note32"}, {"parameters": {"content": "Creating the next workout with name and taking its workout id"}, "type": "n8n-nodes-base.stickyNote", "position": [-8740, -440], "typeVersion": 1, "id": "b04b4440-c5b9-4954-9f71-73629e44c02f", "name": "Sticky Note5"}, {"parameters": {"tableId": "workout_exercises", "fieldsUi": {"fieldValues": [{"fieldId": "workout_id", "fieldValue": "={{ $json['workout_id[0]'] }}"}, {"fieldId": "exercise_id", "fieldValue": "={{ $json.excercise_id.id }}"}, {"fieldId": "sets", "fieldValue": "=\n{{ $json.excercises.sets }}"}, {"fieldId": "weight", "fieldValue": "={{ $json.excercises.weight }}"}, {"fieldId": "order_index", "fieldValue": "={{ $json.excercises.order_index }}"}, {"fieldId": "reps", "fieldValue": "={{ $json.excercises.reps }}"}, {"fieldId": "name", "fieldValue": "={{ $json.excercises.name }}"}, {"fieldId": "rest_interval", "fieldValue": "={{ $json.excercises.rest_interval }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-5800, -800], "id": "14051c47-2bba-4016-b7e2-5d7a99284807", "name": "Supabase", "credentials": {"supabaseApi": {"id": "uVVxVAJWIoyWMJ4R", "name": "Supabase account"}}}, {"parameters": {"content": "might be better to quary these data from supabase directly either here as a flow nodes or from supabase function. right now for example workout ID was missing and I am pulling it here as a node. when workout id is provided, remove the node ", "height": 200, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [-14460, -1060], "typeVersion": 1, "id": "e770b9c8-e9a4-4287-9b72-badc770d172b", "name": "Sticky Note7"}, {"parameters": {"content": "Yeah workout id needs to be provided here in api. added temprorarly to the api example and removed the node that was looking for wrkout id", "height": 200, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [-14460, -880], "typeVersion": 1, "id": "67d3f30f-eab5-467f-a394-0334505d8b4a", "name": "Sticky Note27"}, {"parameters": {"content": "takign preferences and golas"}, "type": "n8n-nodes-base.stickyNote", "position": [-11320, -1080], "typeVersion": 1, "id": "ca151d1f-d621-4a92-804c-d77c10fff663", "name": "Sticky Note31"}, {"parameters": {"content": "for AI summaries and descriptions, do a deep research to see what points and things would be most interesting or important to include in htose descriptions for both the user to see but also next AI"}, "type": "n8n-nodes-base.stickyNote", "position": [-12420, 320], "typeVersion": 1, "id": "67ff139e-160e-4f65-984c-46852d35a0ea", "name": "Sticky Note1"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-12400, 200], "id": "201dce12-f934-46a1-b965-abec051a8bdc", "name": "Wait1", "webhookId": "08345bf9-6d6c-478f-8c5d-1b1d203b27e0", "disabled": true}, {"parameters": {"fieldToSplitOut": "body", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-14060, -1180], "id": "40ae7f55-98e3-4a83-8993-9e2f1ca8b82e", "name": "Split Out"}], "pinData": {"Webhook": [{"json": {"headers": {"host": "sciwell.app.n8n.cloud", "user-agent": "Dart/3.6 (dart:io)", "content-length": "846", "accept-encoding": "gzip, br", "cdn-loop": "cloudflare; loops=1; subreqs=1", "cf-connecting-ip": "*************", "cf-ew-via": "15", "cf-ipcountry": "US", "cf-ray": "950ba9a0f12c07f9-IAD", "cf-visitor": "{\"scheme\":\"https\"}", "cf-worker": "n8n.cloud", "content-type": "application/json; charset=utf-8", "x-forwarded-for": "*************, *************", "x-forwarded-host": "sciwell.app.n8n.cloud", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "traefik-prod-users-gwc-9-7bb7d4c57c-8ggrt", "x-is-trusted": "yes", "x-real-ip": "*************"}, "params": {}, "query": {}, "body": [{"user_id": "4d8a8d40-def8-4179-a4cd-ec3eebbbdeb8", "workout_id": "94275974-4ff5-4141-ab0a-9d10316649a8", "workout_name": "Test", "workout_date": "2025-06-16T16:25:20.722888Z", "planned_workout": {"workout_name": "Test", "exercises": [{"order": 1, "exercise": "Weighted Chin-Up", "planned_sets": 3, "planned_reps": 10, "planned_weight": 0, "rest_interval": 60}]}, "actual_workout": {"workout_name": "Test", "exercises": [{"exercise": "Weighted Chin-Up", "actual_sets": [{"set_order": 1, "performed_reps": 10, "performed_weight": 0, "rep_difference": 0, "set_feedback_difficulty": null}, {"set_order": 2, "performed_reps": 0, "performed_weight": 0, "rep_difference": 10, "set_feedback_difficulty": null}, {"set_order": 3, "performed_reps": 10, "performed_weight": 0, "rep_difference": 0, "set_feedback_difficulty": null}]}]}, "feedback": null, "additional_metrics": {"duration": 0, "calories_burned": 0}}], "webhookUrl": "https://sciwell.app.n8n.cloud/webhook/f468ffaf-259b-439d-acee-23906b9716eb", "executionMode": "production"}}]}, "connections": {"Webhook": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Determine the excercises for the first workout", "type": "ai_languageModel", "index": 0}]]}, "Determine the excercises for the first workout": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Determine the excercises for the first workout", "type": "ai_outputParser", "index": 0}]]}, "Split Out3": {"main": [[{"node": "Supabase", "type": "main", "index": 0}]]}, "Aggregate4": {"main": [[{"node": "Merge3", "type": "main", "index": 2}]]}, "Aggregate3": {"main": [[{"node": "Merge3", "type": "main", "index": 1}]]}, "Aggregate7": {"main": [[{"node": "Postgres5", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "Postgres6", "type": "main", "index": 0}]]}, "Split Out4": {"main": [[{"node": "Postgres3", "type": "main", "index": 0}]]}, "Merge3": {"main": [[{"node": "Split Out3", "type": "main", "index": 0}]]}, "Taking User Id2": {"main": [[{"node": "Postgres", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Taking the just finished workout session id": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Summarizing the just completed workout": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Supabase4", "type": "main", "index": 0}]]}, "Postgres": {"main": [[{"node": "Aggregate6", "type": "main", "index": 0}]]}, "Taking User Id3": {"main": [[{"node": "Postgres1", "type": "main", "index": 0}]]}, "Taking User Id6": {"main": [[{"node": "Postgres4", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Summarizing the just completed workout", "type": "ai_languageModel", "index": 0}]]}, "Edit Fields2": {"main": [[{"node": "Summarizing the just completed workout", "type": "main", "index": 0}]]}, "Aggregate2": {"main": [[{"node": "Stringifying the input2", "type": "main", "index": 0}]]}, "Edit Fields4": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Postgres3": {"main": [[{"node": "Aggregate3", "type": "main", "index": 0}]]}, "Aggregate1": {"main": [[{"node": "Merge3", "type": "main", "index": 0}]]}, "Postgres5": {"main": [[{"node": "Aggregate4", "type": "main", "index": 0}, {"node": "Merge1", "type": "main", "index": 0}]]}, "Merge5": {"main": [[{"node": "Aggregate7", "type": "main", "index": 0}]]}, "Postgres4": {"main": [[{"node": "Merge6", "type": "main", "index": 2}]]}, "Postgres1": {"main": [[{"node": "Aggregate8", "type": "main", "index": 0}]]}, "Merge6": {"main": [[{"node": "Determine the excercises for the first workout", "type": "main", "index": 0}]]}, "Aggregate6": {"main": [[{"node": "Stringifying the input3", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Edit Fields4", "type": "main", "index": 0}, {"node": "Aggregate2", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Aggregate1", "type": "main", "index": 0}, {"node": "Split Out4", "type": "main", "index": 0}, {"node": "Merge5", "type": "main", "index": 1}, {"node": "Merge1", "type": "main", "index": 1}]]}, "Stringifying the input1": {"main": [[{"node": "Merge6", "type": "main", "index": 3}]]}, "Aggregate8": {"main": [[{"node": "Stringifying the input1", "type": "main", "index": 0}]]}, "Stringifying the input3": {"main": [[{"node": "Merge6", "type": "main", "index": 1}]]}, "Stringifying the input2": {"main": [[{"node": "Merge6", "type": "main", "index": 0}]]}, "Calculator": {"ai_tool": [[{"node": "Determine the excercises for the first workout", "type": "ai_tool", "index": 0}]]}, "Supabase": {"main": [[{"node": "Aggregate5", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}, {"node": "Taking the just finished workout session id", "type": "main", "index": 0}, {"node": "Taking User Id2", "type": "main", "index": 0}, {"node": "Taking User Id3", "type": "main", "index": 0}, {"node": "Taking User Id6", "type": "main", "index": 0}, {"node": "Merge5", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "8b25f3ad-dbee-4f0f-9176-843bceede4dc", "meta": {"templateCredsSetupCompleted": true, "instanceId": "6195c139c872709f5410ba81a9d6cc1e5c549703764aa55ff6b328be53cf73c2"}, "id": "w3COrMWy9p3X4J91", "tags": [{"createdAt": "2025-01-20T02:47:40.139Z", "updatedAt": "2025-01-20T02:47:40.139Z", "id": "OG1V0ctH1pvPrnbo", "name": "Ready For testing in app"}, {"createdAt": "2025-01-25T17:15:32.668Z", "updatedAt": "2025-01-25T17:15:32.668Z", "id": "gvHNr7P1PzjMofy4", "name": "Agents"}, {"createdAt": "2025-06-05T03:43:00.596Z", "updatedAt": "2025-06-05T03:43:00.596Z", "id": "ubQhIuVGH0ki6r9n", "name": "openfit"}]}