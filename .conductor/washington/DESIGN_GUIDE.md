# OpenFit Design Guide

This design guide provides comprehensive design standards and guidelines for the OpenFit fitness and nutrition app. All developers should follow these specifications to maintain design consistency across the application.

## ⚠️ CRITICAL: Theme-First Development

**ALL UI COMPONENTS MUST USE THEME CONFIGURATION**

Never hardcode colors, text styles, or spacing values directly in widgets. Always use:
1. `Theme.of(context)` for accessing theme data
2. `theme.textTheme` for text styles
3. `theme.colorScheme` for colors
4. `AppSpacing` constants for spacing
5. `AppBorderRadius` constants for border radius

This ensures:
- Consistent design across the app
- Easy theme switching (light/dark mode)
- Single source of truth for design tokens
- Maintainable and scalable codebase

## 🎨 Color Palette

### Primary Colors
- **Primary Orange**: `#F97316` - Main accent color for CTAs and highlights
- **Dark Background**: `#111214` - Primary background color for dark theme
- **Pure White**: `#FFFFFF` - Text on dark backgrounds and primary elements
- **Medium Gray**: `#676C75` - Secondary backgrounds and dividers
- **Light Gray**: `#9EA0A5` - Secondary text and inactive elements

### Color Usage Guidelines
- Use Primary Orange (`#F97316`) for:
  - Primary action buttons
  - Active states
  - Important highlights
  - Progress indicators
  
- Use Dark Background (`#111214`) for:
  - Main screen backgrounds
  - Card backgrounds in light mode
  - Navigation bars
  
- Use grays for:
  - Disabled states
  - Placeholder text
  - Dividers and borders
  - Secondary information

### Gradients
- **Overlay Gradient**: Linear gradient from transparent to `#111214`
  - Use for image overlays
  - Direction: Top to bottom
  - Opacity: 0% to 100%

## 📐 Typography

### Font Family
**Primary Font**: Work Sans
- Available weights: 500 (Medium), 800 (Extra Bold)
- Fallback: System default sans-serif

### Type Scale

#### Display
- **Size**: 96px
- **Weight**: 800 (Extra Bold)
- **Line Height**: 1em
- **Letter Spacing**: -2%
- **Use**: Main screen titles, hero text

#### Body Text
- **Size**: 14px
- **Weight**: 500 (Medium)
- **Line Height**: 1.173em (≈16.4px)
- **Letter Spacing**: -0.2%
- **Use**: Regular body text, descriptions

### Text Color Guidelines
- Primary text on dark: `#FFFFFF`
- Secondary text on dark: `#9EA0A5`
- Primary text on light: `#111214`
- Accent text: `#F97316`

## 📏 Spacing System

### Base Unit
Use multiples of 8px for consistent spacing:

- **xs**: 8px
- **sm**: 16px
- **md**: 24px
- **lg**: 32px
- **xl**: 40px
- **2xl**: 48px
- **3xl**: 64px
- **4xl**: 128px

### Common Spacing Patterns
- **Screen Padding**: 16px horizontal
- **Section Spacing**: 48px between major sections
- **Component Gap**: 16px between related elements
- **Item Spacing**: 8px between list items
- **Card Padding**: 8px internal padding

## 📱 Layout Guidelines

### Screen Dimensions
- **Mobile Width**: 375px (iPhone standard)
- **Mobile Height**: 812px (iPhone X and newer)
- **Safe Area**: Account for status bar (44px) and home indicator (34px)

### Component Dimensions
- **Buttons**:
  - Full width: 343px (screen width - 32px padding)
  - Height: 48px (standard), 56px (large)
  - Border radius: 16px

- **Cards**:
  - Border radius: 40px (large cards), 16px (small cards)
  - Padding: 8px minimum

- **Input Fields**:
  - Height: 48px
  - Border radius: 12px
  - Horizontal padding: 16px

### Grid System
- Use a 8px grid for alignment
- Main content area: 343px (375px - 32px total padding)
- Column gap: 16px for multi-column layouts

## 🎯 Interactive Elements

### Button States
1. **Default**: Background `#F97316`, Text `#FFFFFF`
2. **Pressed**: Background `#EA580C` (darker shade), Text `#FFFFFF`
3. **Disabled**: Background `#676C75`, Text `#9EA0A5`
4. **Outlined**: Border `#F97316`, Text `#F97316`, Background transparent

### Touch Targets
- Minimum touch target: 44x44px
- Recommended: 48x48px
- Add padding if visual element is smaller

### Animations
- **Duration**: 200-300ms for micro-interactions
- **Easing**: Use ease-in-out for smooth transitions
- **Properties**: Animate opacity, transform, and color changes

## 🖼️ Images and Icons

### Image Handling
- **Aspect Ratios**: 
  - Hero images: 16:9
  - Profile images: 1:1 (circular)
  - Thumbnail images: 4:3
  
- **Border Radius**:
  - Profile images: 50% (circular)
  - Content images: 8px or 16px
  
### Icon Guidelines
- **Size**: 24x24px (standard), 20x20px (small), 32x32px (large)
- **Color**: Use `#FFFFFF` on dark backgrounds, `#111214` on light
- **Style**: Use outlined icons for navigation, filled for actions

## 🌓 Dark Mode

The app primarily uses a dark theme with the following specifications:

### Dark Theme Colors
- **Background**: `#111214`
- **Surface**: `#1A1B1E`
- **Primary Text**: `#FFFFFF`
- **Secondary Text**: `#9EA0A5`
- **Accent**: `#F97316`
- **Divider**: `#676C75` at 20% opacity

### Elevation
Use subtle shadows or lighter backgrounds to show elevation:
- **Level 0**: `#111214` (base)
- **Level 1**: `#1A1B1E`
- **Level 2**: `#232427`
- **Level 3**: `#2C2D30`

## 📱 Platform-Specific Guidelines

### iOS
- Respect iOS safe areas
- Use iOS-style navigation patterns
- Include home indicator (134x5px rounded bar)
- Status bar height: 44px

### Android
- Follow Material Design principles where applicable
- Use Android-style back navigation
- Status bar height: 24px

## ✅ Design Checklist

Before implementing any UI component, ensure:

- [ ] Colors match the defined palette
- [ ] Typography follows the type scale
- [ ] Spacing uses the 8px grid system
- [ ] Touch targets meet minimum size requirements
- [ ] Border radius is consistent with similar components
- [ ] Component works in both light and dark modes (if applicable)
- [ ] Accessibility requirements are met (contrast ratios, text sizes)
- [ ] Animation duration and easing are consistent

## 🔧 Implementation Notes

### Flutter-Specific Guidelines

1. **Use Theme**: Define all colors and text styles in the app theme
2. **Responsive Design**: Use `MediaQuery` for responsive layouts
3. **Consistent Widgets**: Create reusable widgets for common patterns
4. **Asset Management**: Optimize images for different screen densities

### Code Examples

#### ✅ CORRECT - Using Theme
```dart
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
      ),
      child: Text(
        'Hello OpenFit',
        style: textTheme.headlineMedium,
      ),
    );
  }
}
```

#### ❌ WRONG - Hardcoded Values
```dart
// DON'T DO THIS!
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24), // Wrong: hardcoded spacing
      decoration: BoxDecoration(
        color: Color(0xFF111214), // Wrong: hardcoded color
        borderRadius: BorderRadius.circular(16), // Wrong: hardcoded radius
      ),
      child: Text(
        'Hello OpenFit',
        style: TextStyle( // Wrong: hardcoded text style
          fontSize: 28,
          fontWeight: FontWeight.w700,
          color: Colors.white,
        ),
      ),
    );
  }
}
```

### Naming Conventions

- **Colors**: Use semantic names (e.g., `primaryOrange`, `backgroundDark`)
- **Text Styles**: Name by usage (e.g., `displayLarge`, `bodyMedium`)
- **Spacing**: Use t-shirt sizes (e.g., `spacingMd`, `paddingLg`)

## 📚 Component Library

Common components to be implemented:

1. **PrimaryButton**: Orange background, white text, 16px radius
2. **SecondaryButton**: Outlined style with orange border
3. **InputField**: Dark background, white text, 12px radius
4. **Card**: Dark surface, 16px or 40px radius depending on size
5. **NavigationBar**: Dark background with orange active states
6. **Avatar**: Circular profile images with optional border

---

*This design guide is based on the Sandow UI Kit Figma design and should be updated as the design evolves.*