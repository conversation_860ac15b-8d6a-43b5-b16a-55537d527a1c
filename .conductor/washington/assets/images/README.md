# Image Assets Directory

## Required Images

### yoga_woman.jpg
- **Description**: Background image for welcome screen showing a woman practicing yoga
- **Recommended Size**: 1420x812px or higher
- **Format**: JPG or PNG
- **Figma Asset URL**: http://localhost:3845/assets/23bafc3e6686ac053e53659b83a114e4620e52dc.png

To add the image:
1. Download the yoga woman image from your Figma design
2. Save it as `yoga_woman.jpg` or `yoga_woman.png` in this directory
3. If using PNG format, update the asset path in `lib/screens/welcome_screen.dart`

## How to Enable the Image

Once you have the actual image file, update `welcome_screen.dart`:

Replace the gradient placeholder section with:
```dart
// Background image with yoga woman
Positioned.fill(
  child: Container(
    decoration: const BoxDecoration(
      image: DecorationImage(
        image: AssetImage('assets/images/yoga_woman.jpg'),
        fit: BoxFit.cover,
        alignment: Alignment.topCenter,
      ),
    ),
  ),
),
```

The app currently shows a gradient placeholder that looks good while the actual image is pending.