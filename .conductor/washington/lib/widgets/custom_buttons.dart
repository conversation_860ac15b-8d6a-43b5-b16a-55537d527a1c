import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';

class CustomElevatedButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String text;
  // Standardize defaults app-wide
  final double? height;            // default 56
  final double? borderRadius;      // default 16
  final Color? backgroundColor;
  final Color? foregroundColor;
  final Widget? icon;

  const CustomElevatedButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.height = 56,
    this.borderRadius = 16,
    this.backgroundColor,
    this.foregroundColor,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return SizedBox(
      width: double.infinity,
      height: height,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? theme.colorScheme.primary,
          foregroundColor: foregroundColor ?? theme.colorScheme.onPrimary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius!),
          ),
        ),
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null) ...[
                icon!,
                const SizedBox(width: AppSpacing.xs),
              ],
              Text(
                text,
                textAlign: TextAlign.center,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: foregroundColor ?? theme.colorScheme.onPrimary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CustomOutlinedButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String text;
  // Standardize defaults app-wide
  final double? height;            // default 56
  final double? borderRadius;      // default 16
  final Color? borderColor;
  final Color? textColor;
  final bool isPrimary;

  const CustomOutlinedButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.height = 56,
    this.borderRadius = 16,
    this.borderColor,
    this.textColor,
    this.isPrimary = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    final effectiveBorderColor = isPrimary
        ? (borderColor ?? theme.colorScheme.primary)
        : (borderColor ?? AppColors.grayLight.withValues(alpha: 0.4));
    
    final effectiveTextColor = isPrimary
        ? (textColor ?? theme.colorScheme.primary)
        : (textColor ?? AppColors.grayLight);
    
    return SizedBox(
      width: double.infinity,
      height: height,
      child: OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: effectiveBorderColor),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius!),
          ),
        ),
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: theme.textTheme.titleMedium?.copyWith(
              color: effectiveTextColor,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
      ),
    );
  }
}

class CustomSecondaryButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String text;
  // Standardize defaults app-wide
  final double? height;            // default 56
  final double? borderRadius;      // default 16

  const CustomSecondaryButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.height = 56,
    this.borderRadius = 16,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return SizedBox(
      width: double.infinity,
      height: height,
      child: OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: AppColors.grayLight.withValues(alpha: 0.4)),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius!),
          ),
        ),
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: theme.textTheme.titleMedium?.copyWith(
              color: AppColors.grayLight,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
      ),
    );
  }
}
