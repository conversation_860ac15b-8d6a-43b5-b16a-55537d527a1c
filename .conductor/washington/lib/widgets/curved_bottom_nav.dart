import 'package:flutter/material.dart';

/// Curved bottom navigation bar with a center concave notch for a floating orb.
/// Theme-first: colors come from Theme.of(context).colorScheme and dimensions
/// use constants with reasonable defaults. No labels are shown (icon-only).
///
/// Slots:
/// - leftItem: typically Home
/// - rightItem: typically Profile
/// - centerOrb: visual only placeholder for Workout (not tappable per request)
///
/// Usage:
/// Scaffold(
///   bottomNavigationBar: CurvedBottomNav(
///     currentIndex: 0,
///     onTap: (i) { ... }, // i: 0=left, 1=right
///     leftItem: Icons.home_rounded,
///     rightItem: Icons.person_rounded,
///     centerOrb: YourOrbWidget(), // or null for default orb
///   ),
/// )
class CurvedBottomNav extends StatelessWidget {
  const CurvedBottomNav({
    super.key,
    required this.currentIndex,
    required this.onTap,
    this.leftItem = Icons.home_rounded,
    this.rightItem = Icons.person_rounded,
    this.centerOrb,
    this.height = 86,
    this.notchRadius = 38,
    this.iconSize = 28,
    this.edgeRadius = 24,
  });

  final int currentIndex;
  final ValueChanged<int> onTap;
  final IconData leftItem;
  final IconData rightItem;
  final Widget? centerOrb;

  /// Total height of the navigation bar (including curvature).
  final double height;

  /// Radius of the concave notch.
  final double notchRadius;

  /// Icon size for side items.
  final double iconSize;

  /// Outer corner radius of the bar ends.
  final double edgeRadius;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final scheme = theme.colorScheme;

    return SizedBox(
      height: height,
      child: Stack(
        clipBehavior: Clip.none,
        alignment: Alignment.bottomCenter,
        children: [
          // Painted curved background with notch - transparent background
          CustomPaint(
            size: Size.fromHeight(height),
            painter: _CurvedBarPainter(
              background: Colors.transparent, // Transparent background
              shadowColor: Colors.transparent, // Remove shadow
              notchRadius: notchRadius,
              borderRadius: 0, // Remove border radius for seamless edges
            ),
          ),

            // Interactive icons row
            Positioned.fill(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _NavIconButton(
                      icon: leftItem,
                      selected: currentIndex == 0,
                      iconSize: iconSize,
                      onPressed: () => onTap(0),
                    ),
                    _NavIconButton(
                      icon: rightItem,
                      selected: currentIndex == 1,
                      iconSize: iconSize,
                      onPressed: () => onTap(1),
                    ),
                  ],
                ),
              ),
            ),

            // Center orb placeholder (not tappable per requirements)
            Positioned(
              // Position orb to sit properly in the notch without being cut off
              bottom: 20,
              child: _CenterOrb(
                radius: notchRadius - 2,
                scheme: scheme,
              ),
            ),
          ],
        ),
      );
  }
}

class _NavIconButton extends StatelessWidget {
  const _NavIconButton({
    required this.icon,
    required this.selected,
    required this.onPressed,
    required this.iconSize,
  });

  final IconData icon;
  final bool selected;
  final VoidCallback onPressed;
  final double iconSize;

  @override
  Widget build(BuildContext context) {
    final scheme = Theme.of(context).colorScheme;
    final color = selected ? scheme.onSurface : scheme.onSurface.withValues(alpha: 0.72);

    return Semantics(
      button: true,
      selected: selected,
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, size: iconSize, color: color),
        splashRadius: iconSize + 8,
      ),
    );
  }
}

class _CenterOrb extends StatelessWidget {
  const _CenterOrb({
    required this.radius,
    required this.scheme,
  });

  final double radius;
  final ColorScheme scheme;

  @override
  Widget build(BuildContext context) {
    final double diameter = radius * 2;

    return IgnorePointer(
      // non-tappable placeholder
      child: Container(
        width: diameter,
        height: diameter,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: RadialGradient(
            colors: [
              scheme.primary,
              scheme.primary.withValues(alpha: 0.8),
            ],
            stops: const [0.5, 1.0],
          ),
        ),
        alignment: Alignment.center,
        child: Icon(
          Icons.fitness_center,
          color: Colors.white,
          size: radius * 0.7,
        ),
      ),
    );
  }
}

class _CurvedBarPainter extends CustomPainter {
  _CurvedBarPainter({
    required this.background,
    required this.shadowColor,
    required this.notchRadius,
    required this.borderRadius,
  });

  final Color background;
  final Color shadowColor;
  final double notchRadius;
  final double borderRadius;

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    final centerX = width / 2;
    final notchR = notchRadius;
    // Make the concave notch properly sized for the orb
    final notchDepth = notchR + 8;

    final path = Path();

    // Start bottom-left - no rounded corner, straight edge
    path.moveTo(0, height);
    path.lineTo(0, 0);

    // Left to pre-notch
    final notchStartX = centerX - notchR * 1.6;
    path.lineTo(notchStartX, 0);

    // Concave notch using cubic curves for smoothness (wider and deeper)
    final cp1 = Offset(centerX - notchR * 1.05, 0);
    final cp2 = Offset(centerX - notchR * 1.25, notchDepth - notchR * 0.7);
    final notchBottomLeft = Offset(centerX, notchDepth);
    path.cubicTo(cp1.dx, cp1.dy, cp2.dx, cp2.dy, notchBottomLeft.dx, notchBottomLeft.dy);

    final cp3 = Offset(centerX + notchR * 1.25, notchDepth - notchR * 0.7);
    final cp4 = Offset(centerX + notchR * 1.05, 0);
    final notchEndX = centerX + notchR * 1.6;
    path.cubicTo(cp3.dx, cp3.dy, cp4.dx, cp4.dy, notchEndX, 0);

    // Top-right to edge - no rounded corner
    path.lineTo(width, 0);
    path.lineTo(width, height);

    // Bottom edge
    path.lineTo(0, height);
    path.close();

    // No shadow - just fill
    final fillPaint = Paint()..color = background;
    canvas.drawPath(path, fillPaint);
  }

  @override
  bool shouldRepaint(covariant _CurvedBarPainter oldDelegate) {
    return oldDelegate.background != background ||
        oldDelegate.shadowColor != shadowColor ||
        oldDelegate.notchRadius != notchRadius ||
        oldDelegate.borderRadius != borderRadius;
  }
}
