import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:openfitv4/theme/app_theme.dart';

class WheelPicker extends StatelessWidget {
  final int minValue;
  final int maxValue;
  final int selectedValue;
  final Function(int) onChanged;
  final String? suffix;
  final double itemHeight;
  
  const WheelPicker({
    super.key,
    required this.minValue,
    required this.maxValue,
    required this.selectedValue,
    required this.onChanged,
    this.suffix,
    this.itemHeight = 60,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: AppColors.surfaceDark,
        borderRadius: BorderRadius.circular(24),
      ),
      child: Stack(
        children: [
          // Selection indicator
          Center(
            child: Container(
              height: itemHeight,
              margin: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: theme.colorScheme.primary.withValues(alpha: 0.3),
                  width: 2,
                ),
              ),
            ),
          ),
          // Picker
          CupertinoPicker(
            scrollController: FixedExtentScrollController(
              initialItem: selectedValue - minValue,
            ),
            itemExtent: itemHeight,
            onSelectedItemChanged: (index) {
              onChanged(index + minValue);
            },
            children: List.generate(
              maxValue - minValue + 1,
              (index) {
                final value = index + minValue;
                final isSelected = value == selectedValue;
                return Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '$value',
                        style: theme.textTheme.displaySmall?.copyWith(
                          fontSize: isSelected ? 42 : 28,
                          fontWeight: FontWeight.w700,
                          color: isSelected
                            ? theme.colorScheme.primary
                            : AppColors.grayMedium,
                        ),
                      ),
                      if (suffix != null && isSelected) ...[
                        const SizedBox(width: 8),
                        Text(
                          suffix!,
                          style: theme.textTheme.titleLarge?.copyWith(
                            color: AppColors.grayLight,
                          ),
                        ),
                      ],
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class DoubleWheelPicker extends StatelessWidget {
  final int minValue1;
  final int maxValue1;
  final int selectedValue1;
  final Function(int) onChanged1;
  final String suffix1;
  
  final int minValue2;
  final int maxValue2;
  final int selectedValue2;
  final Function(int) onChanged2;
  final String suffix2;
  
  const DoubleWheelPicker({
    super.key,
    required this.minValue1,
    required this.maxValue1,
    required this.selectedValue1,
    required this.onChanged1,
    required this.suffix1,
    required this.minValue2,
    required this.maxValue2,
    required this.selectedValue2,
    required this.onChanged2,
    required this.suffix2,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: AppColors.surfaceDark,
        borderRadius: BorderRadius.circular(24),
      ),
      child: Stack(
        children: [
          // Selection indicator
          Center(
            child: Container(
              height: 60,
              margin: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: theme.colorScheme.primary.withValues(alpha: 0.3),
                  width: 2,
                ),
              ),
            ),
          ),
          // Pickers
          Row(
            children: [
              // First picker
              Expanded(
                child: CupertinoPicker(
                  scrollController: FixedExtentScrollController(
                    initialItem: selectedValue1 - minValue1,
                  ),
                  itemExtent: 60,
                  onSelectedItemChanged: (index) {
                    onChanged1(index + minValue1);
                  },
                  children: List.generate(
                    maxValue1 - minValue1 + 1,
                    (index) {
                      final value = index + minValue1;
                      final isSelected = value == selectedValue1;
                      return Center(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '$value',
                              style: theme.textTheme.displaySmall?.copyWith(
                                fontSize: isSelected ? 42 : 28,
                                fontWeight: FontWeight.w700,
                                color: isSelected
                                  ? theme.colorScheme.primary
                                  : AppColors.grayMedium,
                              ),
                            ),
                            if (isSelected) ...[
                              const SizedBox(width: 4),
                              Text(
                                suffix1,
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: AppColors.grayLight,
                                ),
                              ),
                            ],
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
              // Second picker
              Expanded(
                child: CupertinoPicker(
                  scrollController: FixedExtentScrollController(
                    initialItem: selectedValue2 - minValue2,
                  ),
                  itemExtent: 60,
                  onSelectedItemChanged: (index) {
                    onChanged2(index + minValue2);
                  },
                  children: List.generate(
                    maxValue2 - minValue2 + 1,
                    (index) {
                      final value = index + minValue2;
                      final isSelected = value == selectedValue2;
                      return Center(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '$value',
                              style: theme.textTheme.displaySmall?.copyWith(
                                fontSize: isSelected ? 42 : 28,
                                fontWeight: FontWeight.w700,
                                color: isSelected
                                  ? theme.colorScheme.primary
                                  : AppColors.grayMedium,
                              ),
                            ),
                            if (isSelected) ...[
                              const SizedBox(width: 4),
                              Text(
                                suffix2,
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: AppColors.grayLight,
                                ),
                              ),
                            ],
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}