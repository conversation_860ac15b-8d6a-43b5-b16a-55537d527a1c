import 'package:openfitv4/models/workout_data.dart';

class SampleWorkouts {
  // Sample exercises
  static const List<Exercise> exercises = [
    Exercise(
      id: 'push_up',
      name: 'Push-ups',
      muscle: 'Chest, Triceps, Shoulders',
      equipment: 'Bodyweight',
      difficulty: 'Beginner',
      instructions: 'Start in plank position. Lower your body until chest nearly touches floor. Push back up.',
      imageUrl: 'assets/images/chest_press.png',
      tips: [
        'Keep your core tight',
        'Maintain straight line from head to heels',
        'Control the movement'
      ],
    ),
    Exercise(
      id: 'squat',
      name: 'Squats',
      muscle: 'Quadriceps, Glutes, Hamstrings',
      equipment: 'Bodyweight',
      difficulty: 'Beginner',
      instructions: 'Stand with feet shoulder-width apart. Lower down as if sitting in chair. Return to start.',
      imageUrl: 'assets/images/yoga_woman.png',
      tips: [
        'Keep knees behind toes',
        'Keep chest up',
        'Go as low as comfortable'
      ],
    ),
    Exercise(
      id: 'plank',
      name: 'Plank',
      muscle: 'Core, Shoulders',
      equipment: 'Bodyweight',
      difficulty: 'Beginner',
      instructions: 'Hold push-up position with forearms on ground. Keep body straight.',
      imageUrl: 'assets/images/chest_press.png',
      tips: [
        'Keep hips level',
        'Breathe normally',
        'Engage core muscles'
      ],
    ),
    Exercise(
      id: 'dumbbell_press',
      name: 'Dumbbell Bench Press',
      muscle: 'Chest, Triceps, Shoulders',
      equipment: 'Dumbbells, Bench',
      difficulty: 'Intermediate',
      instructions: 'Lie on bench with dumbbells. Press weights up and together. Lower with control.',
      imageUrl: 'assets/images/chest_press.png',
      tips: [
        'Keep feet flat on floor',
        'Control the weight',
        'Full range of motion'
      ],
    ),
    Exercise(
      id: 'deadlift',
      name: 'Deadlift',
      muscle: 'Hamstrings, Glutes, Back',
      equipment: 'Barbell',
      difficulty: 'Advanced',
      instructions: 'Stand with feet hip-width apart. Grip bar, keep back straight, lift by extending hips.',
      imageUrl: 'assets/images/yoga_woman.png',
      tips: [
        'Keep bar close to body',
        'Drive through heels',
        'Keep back neutral'
      ],
    ),
    Exercise(
      id: 'burpee',
      name: 'Burpees',
      muscle: 'Full Body',
      equipment: 'Bodyweight',
      difficulty: 'Intermediate',
      instructions: 'Squat down, jump back to plank, do push-up, jump feet forward, jump up.',
      imageUrl: 'assets/images/chest_press.png',
      tips: [
        'Land softly on jumps',
        'Keep core engaged',
        'Maintain good form'
      ],
    ),
  ];

  // Sample workouts
  static List<Workout> get allWorkouts => [
    // Upper Body Strength
    Workout(
      id: 'upper_strength',
      name: 'Upper Body Strength',
      description: 'Build upper body strength with compound movements',
      imageUrl: 'assets/images/chest_press.png',
      category: 'Strength',
      difficulty: 'Intermediate',
      duration: 45,
      caloriesBurn: 320,
      equipment: ['Dumbbells', 'Bench'],
      muscleGroups: ['Chest', 'Back', 'Shoulders', 'Arms'],
      exercises: [
        WorkoutExercise(
          exercise: exercises[3], // Dumbbell Press
          sets: [
            WorkoutSet(setNumber: 1, reps: 12, weight: 25, isWarmup: true),
            WorkoutSet(setNumber: 2, reps: 10, weight: 35),
            WorkoutSet(setNumber: 3, reps: 8, weight: 40),
            WorkoutSet(setNumber: 4, reps: 6, weight: 45),
          ],
          restTime: 90,
        ),
        WorkoutExercise(
          exercise: exercises[0], // Push-ups
          sets: [
            WorkoutSet(setNumber: 1, reps: 15),
            WorkoutSet(setNumber: 2, reps: 12),
            WorkoutSet(setNumber: 3, reps: 10),
          ],
          restTime: 60,
        ),
      ],
    ),

    // Full Body HIIT
    Workout(
      id: 'hiit_cardio',
      name: 'HIIT Cardio Blast',
      description: 'High-intensity interval training for maximum calorie burn',
      imageUrl: 'assets/images/yoga_woman.png',
      category: 'HIIT',
      difficulty: 'Advanced',
      duration: 25,
      caloriesBurn: 400,
      equipment: ['Bodyweight'],
      muscleGroups: ['Full Body'],
      exercises: [
        WorkoutExercise(
          exercise: exercises[5], // Burpees
          sets: [
            WorkoutSet(setNumber: 1, reps: 10, duration: 30),
            WorkoutSet(setNumber: 2, reps: 10, duration: 30),
            WorkoutSet(setNumber: 3, reps: 10, duration: 30),
            WorkoutSet(setNumber: 4, reps: 10, duration: 30),
          ],
          restTime: 30,
        ),
        WorkoutExercise(
          exercise: exercises[1], // Squats
          sets: [
            WorkoutSet(setNumber: 1, reps: 20, duration: 45),
            WorkoutSet(setNumber: 2, reps: 20, duration: 45),
            WorkoutSet(setNumber: 3, reps: 20, duration: 45),
          ],
          restTime: 15,
        ),
      ],
    ),

    // Back Workout
    Workout(
      id: 'back_workout',
      name: 'Back Workout',
      description: 'Strengthen and define your back muscles',
      imageUrl: 'assets/images/chest_press.png',
      category: 'Strength',
      difficulty: 'Intermediate',
      duration: 40,
      caloriesBurn: 280,
      equipment: ['Barbell', 'Dumbbells'],
      muscleGroups: ['Back', 'Biceps'],
      exercises: [
        WorkoutExercise(
          exercise: exercises[4], // Deadlift
          sets: [
            WorkoutSet(setNumber: 1, reps: 8, weight: 135, isWarmup: true),
            WorkoutSet(setNumber: 2, reps: 6, weight: 185),
            WorkoutSet(setNumber: 3, reps: 5, weight: 205),
            WorkoutSet(setNumber: 4, reps: 4, weight: 225),
          ],
          restTime: 120,
        ),
      ],
    ),

    // Core & Abs
    Workout(
      id: 'core_abs',
      name: 'Core & Abs',
      description: 'Strengthen your core with targeted exercises',
      imageUrl: 'assets/images/yoga_woman.png',
      category: 'Strength',
      difficulty: 'Beginner',
      duration: 20,
      caloriesBurn: 150,
      equipment: ['Bodyweight'],
      muscleGroups: ['Core', 'Abs'],
      exercises: [
        WorkoutExercise(
          exercise: exercises[2], // Plank
          sets: [
            WorkoutSet(setNumber: 1, reps: 1, duration: 30),
            WorkoutSet(setNumber: 2, reps: 1, duration: 45),
            WorkoutSet(setNumber: 3, reps: 1, duration: 60),
          ],
          restTime: 30,
        ),
      ],
    ),

    // Leg Day
    Workout(
      id: 'leg_day',
      name: 'Leg Day Power',
      description: 'Build powerful legs with this intense workout',
      imageUrl: 'assets/images/yoga_woman.png',
      category: 'Strength',
      difficulty: 'Advanced',
      duration: 50,
      caloriesBurn: 380,
      equipment: ['Barbell', 'Dumbbells'],
      muscleGroups: ['Quadriceps', 'Hamstrings', 'Glutes', 'Calves'],
      exercises: [
        WorkoutExercise(
          exercise: exercises[1], // Squats
          sets: [
            WorkoutSet(setNumber: 1, reps: 15, isWarmup: true),
            WorkoutSet(setNumber: 2, reps: 12),
            WorkoutSet(setNumber: 3, reps: 10),
            WorkoutSet(setNumber: 4, reps: 8),
          ],
          restTime: 90,
        ),
      ],
    ),
  ];

  // Sample programs
  static List<WorkoutProgram> get programs => [
    WorkoutProgram(
      id: 'beginner_strength',
      name: 'Beginner Strength',
      description: '4-week program to build foundational strength',
      imageUrl: 'assets/images/chest_press.png',
      weeks: 4,
      workoutsPerWeek: 3,
      difficulty: 'Beginner',
      goal: 'Build Strength',
      workouts: [allWorkouts[0], allWorkouts[3], allWorkouts[4]],
    ),
    WorkoutProgram(
      id: 'hiit_shred',
      name: 'HIIT Shred',
      description: '6-week fat burning program',
      imageUrl: 'assets/images/yoga_woman.png',
      weeks: 6,
      workoutsPerWeek: 4,
      difficulty: 'Advanced',
      goal: 'Fat Loss',
      workouts: [allWorkouts[1], allWorkouts[3]],
    ),
  ];

  // Today's workouts (scheduled)
  static List<Workout> get todaysWorkouts {
    final today = DateTime.now();
    return allWorkouts.take(2).map((workout) => Workout(
      id: workout.id,
      name: workout.name,
      description: workout.description,
      imageUrl: workout.imageUrl,
      category: workout.category,
      difficulty: workout.difficulty,
      duration: workout.duration,
      caloriesBurn: workout.caloriesBurn,
      equipment: workout.equipment,
      muscleGroups: workout.muscleGroups,
      exercises: workout.exercises,
      scheduledDate: today,
    )).toList();
  }

  // Recent workouts (completed)
  static List<Workout> get recentWorkouts {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    
    return allWorkouts.take(3).map((workout) => Workout(
      id: workout.id,
      name: workout.name,
      description: workout.description,
      imageUrl: workout.imageUrl,
      category: workout.category,
      difficulty: workout.difficulty,
      duration: workout.duration,
      caloriesBurn: workout.caloriesBurn,
      equipment: workout.equipment,
      muscleGroups: workout.muscleGroups,
      exercises: workout.exercises,
      scheduledDate: yesterday,
      isCompleted: true,
    )).toList();
  }
}