class UserModel {
  final String id;
  final String? email;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Personal Information
  final String? displayName;
  final String? gender;
  final int? age;
  final double? height;
  final String? heightUnit;
  final double? weight;
  final String? weightUnit;
  
  // Fitness Goals and Preferences
  final List<String>? fitnessGoalsArray;
  final String? fitnessGoalPrimary;
  final List<String>? fitnessGoalsOrder;
  final String? sportOfChoice;
  final String? specificSportActivity;
  
  // Fitness Levels
  final int? cardioFitnessLevel;
  final int? weightliftingFitnessLevel;
  final String? fitnessExperience;
  final String? trainingExperienceLevel;
  final int? fitnessLevel;
  
  // Equipment and Environment
  final List<String>? equipment;
  final List<String>? workoutEnvironment;
  final List<String>? exercisePreferences;
  final List<String>? exercisesToAvoid;
  final List<String>? physicalLimitations;
  
  // Workout Schedule
  final int? workoutFrequency;
  final int? workoutDuration;
  final List<String>? workoutDays;
  final int? preferredWorkoutDaysCount;
  final String? preferredWorkoutDuration;
  final String? scheduleFlexibility;
  
  // Health and Diet
  final List<String>? healthConditions;
  final List<String>? dietaryRestrictions;
  final List<String>? dietPreferences;
  final bool? takingSupplements;
  final List<String>? supplements;
  final int? mealsPerDay;
  final int? caloricGoal;
  final String? sleepQuality;
  
  // App State
  final bool onboardingCompleted;
  final bool fitnessAssessmentCompleted;
  final bool hasCompletedPreferences;
  final String? fitnessGuide;
  final Map<String, dynamic>? metadata;
  final String? additionalHealthInfo;
  final String? additionalNotes;

  const UserModel({
    required this.id,
    this.email,
    required this.createdAt,
    required this.updatedAt,
    this.displayName,
    this.gender,
    this.age,
    this.height,
    this.heightUnit = 'cm',
    this.weight,
    this.weightUnit = 'kg',
    this.fitnessGoalsArray,
    this.fitnessGoalPrimary,
    this.fitnessGoalsOrder,
    this.sportOfChoice,
    this.specificSportActivity,
    this.cardioFitnessLevel,
    this.weightliftingFitnessLevel,
    this.fitnessExperience,
    this.trainingExperienceLevel,
    this.fitnessLevel,
    this.equipment,
    this.workoutEnvironment,
    this.exercisePreferences,
    this.exercisesToAvoid,
    this.physicalLimitations,
    this.workoutFrequency,
    this.workoutDuration,
    this.workoutDays,
    this.preferredWorkoutDaysCount,
    this.preferredWorkoutDuration,
    this.scheduleFlexibility,
    this.healthConditions,
    this.dietaryRestrictions,
    this.dietPreferences,
    this.takingSupplements,
    this.supplements,
    this.mealsPerDay,
    this.caloricGoal,
    this.sleepQuality,
    this.onboardingCompleted = false,
    this.fitnessAssessmentCompleted = false,
    this.hasCompletedPreferences = false,
    this.fitnessGuide,
    this.metadata,
    this.additionalHealthInfo,
    this.additionalNotes,
  });

  factory UserModel.fromSupabase(Map<String, dynamic> data) {
    return UserModel(
      id: data['id'] as String,
      email: data['email'] as String?,
      createdAt: DateTime.parse(data['created_at'] as String),
      updatedAt: DateTime.parse(data['updated_at'] as String),
      displayName: data['display_name'] as String?,
      gender: data['gender'] as String?,
      age: data['age'] as int?,
      height: data['height'] != null ? double.tryParse(data['height'].toString()) : null,
      heightUnit: data['height_unit'] as String? ?? 'cm',
      weight: data['weight'] != null ? double.tryParse(data['weight'].toString()) : null,
      weightUnit: data['weight_unit'] as String? ?? 'kg',
      fitnessGoalsArray: data['fitness_goals_array'] != null 
          ? List<String>.from(data['fitness_goals_array']) 
          : null,
      fitnessGoalPrimary: data['fitness_goal_primary'] as String?,
      fitnessGoalsOrder: data['fitness_goals_order'] != null 
          ? List<String>.from(data['fitness_goals_order']) 
          : null,
      sportOfChoice: data['sport_of_choice'] as String?,
      specificSportActivity: data['specific_sport_activity'] as String?,
      cardioFitnessLevel: data['cardio_fitness_level'] as int?,
      weightliftingFitnessLevel: data['weightlifting_fitness_level'] as int?,
      fitnessExperience: data['fitness_experience'] as String?,
      trainingExperienceLevel: data['training_experience_level'] as String?,
      fitnessLevel: data['fitness_level'] as int?,
      equipment: data['equipment'] != null 
          ? List<String>.from(data['equipment']) 
          : null,
      workoutEnvironment: data['workout_environment'] != null 
          ? List<String>.from(data['workout_environment']) 
          : null,
      exercisePreferences: data['exercise_preferences'] != null 
          ? List<String>.from(data['exercise_preferences']) 
          : null,
      exercisesToAvoid: data['exercises_to_avoid'] != null 
          ? List<String>.from(data['exercises_to_avoid']) 
          : null,
      physicalLimitations: data['physical_limitations'] != null 
          ? List<String>.from(data['physical_limitations']) 
          : null,
      workoutFrequency: data['workout_frequency'] as int?,
      workoutDuration: data['workout_duration'] as int?,
      workoutDays: data['workout_days'] != null 
          ? List<String>.from(data['workout_days']) 
          : null,
      preferredWorkoutDaysCount: data['preferred_workout_days_count'] as int?,
      preferredWorkoutDuration: data['preferred_workout_duration'] as String?,
      scheduleFlexibility: data['schedule_flexibility'] as String?,
      healthConditions: data['health_conditions'] != null 
          ? List<String>.from(data['health_conditions']) 
          : null,
      dietaryRestrictions: data['dietary_restrictions'] != null 
          ? List<String>.from(data['dietary_restrictions']) 
          : null,
      dietPreferences: data['diet_preferences'] != null 
          ? List<String>.from(data['diet_preferences']) 
          : null,
      takingSupplements: data['taking_supplements'] as bool?,
      supplements: data['supplements'] != null 
          ? List<String>.from(data['supplements']) 
          : null,
      mealsPerDay: data['meals_per_day'] as int?,
      caloricGoal: data['caloric_goal'] as int?,
      sleepQuality: data['sleep_quality'] as String?,
      onboardingCompleted: data['onboarding_completed'] as bool? ?? false,
      fitnessAssessmentCompleted: data['fitness_assessment_completed'] as bool? ?? false,
      hasCompletedPreferences: data['has_completed_preferences'] as bool? ?? false,
      fitnessGuide: data['fitness_guide'] as String?,
      metadata: data['metadata'] as Map<String, dynamic>?,
      additionalHealthInfo: data['additional_health_info'] as String?,
      additionalNotes: data['additional_notes'] as String?,
    );
  }

  Map<String, dynamic> toSupabase() {
    return {
      'id': id,
      'email': email,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'display_name': displayName,
      'gender': gender,
      'age': age,
      'height': height,
      'height_unit': heightUnit,
      'weight': weight,
      'weight_unit': weightUnit,
      'fitness_goals_array': fitnessGoalsArray,
      'fitness_goal_primary': fitnessGoalPrimary,
      'fitness_goals_order': fitnessGoalsOrder,
      'sport_of_choice': sportOfChoice,
      'specific_sport_activity': specificSportActivity,
      'cardio_fitness_level': cardioFitnessLevel,
      'weightlifting_fitness_level': weightliftingFitnessLevel,
      'fitness_experience': fitnessExperience,
      'training_experience_level': trainingExperienceLevel,
      'fitness_level': fitnessLevel,
      'equipment': equipment,
      'workout_environment': workoutEnvironment,
      'exercise_preferences': exercisePreferences,
      'exercises_to_avoid': exercisesToAvoid,
      'physical_limitations': physicalLimitations,
      'workout_frequency': workoutFrequency,
      'workout_duration': workoutDuration,
      'workout_days': workoutDays,
      'preferred_workout_days_count': preferredWorkoutDaysCount,
      'preferred_workout_duration': preferredWorkoutDuration,
      'schedule_flexibility': scheduleFlexibility,
      'health_conditions': healthConditions,
      'dietary_restrictions': dietaryRestrictions,
      'diet_preferences': dietPreferences,
      'taking_supplements': takingSupplements,
      'supplements': supplements,
      'meals_per_day': mealsPerDay,
      'caloric_goal': caloricGoal,
      'sleep_quality': sleepQuality,
      'onboarding_completed': onboardingCompleted,
      'fitness_assessment_completed': fitnessAssessmentCompleted,
      'has_completed_preferences': hasCompletedPreferences,
      'fitness_guide': fitnessGuide,
      'metadata': metadata,
      'additional_health_info': additionalHealthInfo,
      'additional_notes': additionalNotes,
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? displayName,
    String? gender,
    int? age,
    double? height,
    String? heightUnit,
    double? weight,
    String? weightUnit,
    List<String>? fitnessGoalsArray,
    String? fitnessGoalPrimary,
    List<String>? fitnessGoalsOrder,
    String? sportOfChoice,
    String? specificSportActivity,
    int? cardioFitnessLevel,
    int? weightliftingFitnessLevel,
    String? fitnessExperience,
    String? trainingExperienceLevel,
    int? fitnessLevel,
    List<String>? equipment,
    List<String>? workoutEnvironment,
    List<String>? exercisePreferences,
    List<String>? exercisesToAvoid,
    List<String>? physicalLimitations,
    int? workoutFrequency,
    int? workoutDuration,
    List<String>? workoutDays,
    int? preferredWorkoutDaysCount,
    String? preferredWorkoutDuration,
    String? scheduleFlexibility,
    List<String>? healthConditions,
    List<String>? dietaryRestrictions,
    List<String>? dietPreferences,
    bool? takingSupplements,
    List<String>? supplements,
    int? mealsPerDay,
    int? caloricGoal,
    String? sleepQuality,
    bool? onboardingCompleted,
    bool? fitnessAssessmentCompleted,
    bool? hasCompletedPreferences,
    String? fitnessGuide,
    Map<String, dynamic>? metadata,
    String? additionalHealthInfo,
    String? additionalNotes,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      displayName: displayName ?? this.displayName,
      gender: gender ?? this.gender,
      age: age ?? this.age,
      height: height ?? this.height,
      heightUnit: heightUnit ?? this.heightUnit,
      weight: weight ?? this.weight,
      weightUnit: weightUnit ?? this.weightUnit,
      fitnessGoalsArray: fitnessGoalsArray ?? this.fitnessGoalsArray,
      fitnessGoalPrimary: fitnessGoalPrimary ?? this.fitnessGoalPrimary,
      fitnessGoalsOrder: fitnessGoalsOrder ?? this.fitnessGoalsOrder,
      sportOfChoice: sportOfChoice ?? this.sportOfChoice,
      specificSportActivity: specificSportActivity ?? this.specificSportActivity,
      cardioFitnessLevel: cardioFitnessLevel ?? this.cardioFitnessLevel,
      weightliftingFitnessLevel: weightliftingFitnessLevel ?? this.weightliftingFitnessLevel,
      fitnessExperience: fitnessExperience ?? this.fitnessExperience,
      trainingExperienceLevel: trainingExperienceLevel ?? this.trainingExperienceLevel,
      fitnessLevel: fitnessLevel ?? this.fitnessLevel,
      equipment: equipment ?? this.equipment,
      workoutEnvironment: workoutEnvironment ?? this.workoutEnvironment,
      exercisePreferences: exercisePreferences ?? this.exercisePreferences,
      exercisesToAvoid: exercisesToAvoid ?? this.exercisesToAvoid,
      physicalLimitations: physicalLimitations ?? this.physicalLimitations,
      workoutFrequency: workoutFrequency ?? this.workoutFrequency,
      workoutDuration: workoutDuration ?? this.workoutDuration,
      workoutDays: workoutDays ?? this.workoutDays,
      preferredWorkoutDaysCount: preferredWorkoutDaysCount ?? this.preferredWorkoutDaysCount,
      preferredWorkoutDuration: preferredWorkoutDuration ?? this.preferredWorkoutDuration,
      scheduleFlexibility: scheduleFlexibility ?? this.scheduleFlexibility,
      healthConditions: healthConditions ?? this.healthConditions,
      dietaryRestrictions: dietaryRestrictions ?? this.dietaryRestrictions,
      dietPreferences: dietPreferences ?? this.dietPreferences,
      takingSupplements: takingSupplements ?? this.takingSupplements,
      supplements: supplements ?? this.supplements,
      mealsPerDay: mealsPerDay ?? this.mealsPerDay,
      caloricGoal: caloricGoal ?? this.caloricGoal,
      sleepQuality: sleepQuality ?? this.sleepQuality,
      onboardingCompleted: onboardingCompleted ?? this.onboardingCompleted,
      fitnessAssessmentCompleted: fitnessAssessmentCompleted ?? this.fitnessAssessmentCompleted,
      hasCompletedPreferences: hasCompletedPreferences ?? this.hasCompletedPreferences,
      fitnessGuide: fitnessGuide ?? this.fitnessGuide,
      metadata: metadata ?? this.metadata,
      additionalHealthInfo: additionalHealthInfo ?? this.additionalHealthInfo,
      additionalNotes: additionalNotes ?? this.additionalNotes,
    );
  }
}