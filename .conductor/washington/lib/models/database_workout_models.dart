import 'workout_data.dart';

// Database Exercise model (matches 'exercises' table)
class DBExercise {
  final String id;
  final String name;
  final String? description;
  final String? videoUrl;
  final String? primaryMuscle;
  final String? equipment;
  final String? category;
  final String? secondaryMuscle;
  final String? instructions;
  final String? verticalVideo;
  final DateTime createdAt;

  DBExercise({
    required this.id,
    required this.name,
    this.description,
    this.videoUrl,
    this.primaryMuscle,
    this.equipment,
    this.category,
    this.secondaryMuscle,
    this.instructions,
    this.verticalVideo,
    required this.createdAt,
  });

  factory DBExercise.fromJson(Map<String, dynamic> json) {
    return DBExercise(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      videoUrl: json['video_url'] as String?,
      primaryMuscle: json['primary_muscle'] as String?,
      equipment: json['equipment'] as String?,
      category: json['category'] as String?,
      secondaryMuscle: json['secondary_muscle'] as String?,
      instructions: json['instructions'] as String?,
      verticalVideo: json['vertical_video'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'video_url': videoUrl,
      'primary_muscle': primaryMuscle,
      'equipment': equipment,
      'category': category,
      'secondary_muscle': secondaryMuscle,
      'instructions': instructions,
      'vertical_video': verticalVideo,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// Database Workout Exercise model (matches 'workout_exercises' table)
class DBWorkoutExercise {
  final String id;
  final String workoutId;
  final String exerciseId;
  final String name;
  final int? sets;
  final int? orderIndex;
  final int? restInterval;
  final List<String>? weight;
  final List<String>? reps;
  final bool? completed;
  final DateTime? createdAt;
  
  // Joined exercise data
  final DBExercise? exercise;

  DBWorkoutExercise({
    required this.id,
    required this.workoutId,
    required this.exerciseId,
    required this.name,
    this.sets,
    this.orderIndex,
    this.restInterval,
    this.weight,
    this.reps,
    this.completed,
    this.createdAt,
    this.exercise,
  });

  factory DBWorkoutExercise.fromJson(Map<String, dynamic> json) {
    return DBWorkoutExercise(
      id: json['id'] as String,
      workoutId: json['workout_id'] as String,
      exerciseId: json['exercise_id'] as String,
      name: json['name'] as String,
      sets: json['sets'] as int?,
      orderIndex: json['order_index'] as int?,
      restInterval: json['rest_interval'] as int?,
      weight: json['weight'] != null 
        ? (json['weight'] as List).cast<String>()
        : null,
      reps: json['reps'] != null
        ? (json['reps'] as List).cast<String>()
        : null,
      completed: json['completed'] as bool?,
      createdAt: json['created_at'] != null 
        ? DateTime.parse(json['created_at'] as String)
        : null,
      exercise: json['exercise'] != null
        ? DBExercise.fromJson(json['exercise'] as Map<String, dynamic>)
        : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'workout_id': workoutId,
      'exercise_id': exerciseId,
      'name': name,
      'sets': sets,
      'order_index': orderIndex,
      'rest_interval': restInterval,
      'weight': weight,
      'reps': reps,
      'completed': completed,
      'created_at': createdAt?.toIso8601String(),
    };
  }
}

// Database Workout model (matches 'workouts' table)
class DBWorkout {
  final String id;
  final String userId;
  final String name;
  final DateTime? startTime;
  final DateTime? endTime;
  final bool? isActive;
  final int? duration; // computed field in seconds
  final String? notes;
  final DateTime? createdAt;
  final int? rating;
  final bool? isMinimized;
  final Map<String, dynamic>? lastState;
  final bool? isCompleted;
  final int? sessionOrder;
  final DateTime? updatedAt;
  final String? aiDescription;
  
  // Related data
  final List<DBWorkoutExercise> exercises;

  DBWorkout({
    required this.id,
    required this.userId,
    required this.name,
    this.startTime,
    this.endTime,
    this.isActive,
    this.duration,
    this.notes,
    this.createdAt,
    this.rating,
    this.isMinimized,
    this.lastState,
    this.isCompleted,
    this.sessionOrder,
    this.updatedAt,
    this.aiDescription,
    this.exercises = const [],
  });

  factory DBWorkout.fromJson(Map<String, dynamic> json) {
    return DBWorkout(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      name: json['name'] as String,
      startTime: json['start_time'] != null 
        ? DateTime.parse(json['start_time'] as String)
        : null,
      endTime: json['end_time'] != null
        ? DateTime.parse(json['end_time'] as String)
        : null,
      isActive: json['is_active'] as bool?,
      duration: json['duration'] as int?,
      notes: json['notes'] as String?,
      createdAt: json['created_at'] != null
        ? DateTime.parse(json['created_at'] as String)
        : null,
      rating: json['rating'] as int?,
      isMinimized: json['is_minimized'] as bool?,
      lastState: json['last_state'] as Map<String, dynamic>?,
      isCompleted: json['is_completed'] as bool?,
      sessionOrder: json['session_order'] as int?,
      updatedAt: json['updated_at'] != null
        ? DateTime.parse(json['updated_at'] as String)
        : null,
      aiDescription: json['ai_description'] as String?,
      exercises: json['workout_exercises'] != null
        ? (json['workout_exercises'] as List)
            .map((e) => DBWorkoutExercise.fromJson(e as Map<String, dynamic>))
            .toList()
        : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,
      'start_time': startTime?.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'is_active': isActive,
      'notes': notes,
      'created_at': createdAt?.toIso8601String(),
      'rating': rating,
      'is_minimized': isMinimized,
      'last_state': lastState,
      'is_completed': isCompleted,
      'session_order': sessionOrder,
      'updated_at': updatedAt?.toIso8601String(),
      'ai_description': aiDescription,
    };
  }

  // Helper methods
  int get exerciseCount => exercises.length;
  
  int get completedExerciseCount => 
    exercises.where((e) => e.completed ?? false).length;
  
  double get progress => 
    exerciseCount > 0 ? completedExerciseCount / exerciseCount : 0.0;

  String get formattedDuration {
    if (duration == null) return '--:--';
    final hours = duration! ~/ 3600;
    final minutes = (duration! % 3600) ~/ 60;
    final seconds = duration! % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }
}

// Database Completed Workout model (matches 'completed_workouts' table)
class DBCompletedWorkout {
  final int id;
  final String workoutId;
  final String? userId;
  final DateTime? dateCompleted;
  final int? duration;
  final int? caloriesBurned;
  final int? rating;
  final String? userFeedback;
  final Map<String, dynamic>? completedWorkoutSummary;
  final DateTime createdAt;

  DBCompletedWorkout({
    required this.id,
    required this.workoutId,
    this.userId,
    this.dateCompleted,
    this.duration,
    this.caloriesBurned,
    this.rating,
    this.userFeedback,
    this.completedWorkoutSummary,
    required this.createdAt,
  });

  factory DBCompletedWorkout.fromJson(Map<String, dynamic> json) {
    return DBCompletedWorkout(
      id: json['id'] as int,
      workoutId: json['workout_id'] as String,
      userId: json['user_id'] as String?,
      dateCompleted: json['date_completed'] != null
        ? DateTime.parse(json['date_completed'] as String)
        : null,
      duration: json['duration'] as int?,
      caloriesBurned: json['calories_burned'] as int?,
      rating: json['rating'] as int?,
      userFeedback: json['user_feedback_completed_workout'] as String?,
      completedWorkoutSummary: json['completed_workout_summary'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'workout_id': workoutId,
      'user_id': userId,
      'date_completed': dateCompleted?.toIso8601String(),
      'duration': duration,
      'calories_burned': caloriesBurned,
      'rating': rating,
      'user_feedback_completed_workout': userFeedback,
      'completed_workout_summary': completedWorkoutSummary,
    };
  }
}

// Helper to convert database models to app models
class WorkoutModelConverter {
  static List<WorkoutSet> convertToWorkoutSets(List<String>? reps, List<String>? weights) {
    if (reps == null || reps.isEmpty) return [];
    
    final sets = <WorkoutSet>[];
    for (int i = 0; i < reps.length; i++) {
      final repsValue = int.tryParse(reps[i]) ?? 0;
      final weightValue = weights != null && i < weights.length 
        ? double.tryParse(weights[i]) 
        : null;
      
      sets.add(WorkoutSet(
        setNumber: i + 1,
        reps: repsValue,
        weight: weightValue,
        isCompleted: false,
      ));
    }
    
    return sets;
  }

  static Exercise convertToExercise(DBExercise dbExercise) {
    return Exercise(
      id: dbExercise.id,
      name: dbExercise.name,
      muscle: dbExercise.primaryMuscle ?? 'Unknown',
      equipment: dbExercise.equipment ?? 'None',
      difficulty: _getDifficultyFromCategory(dbExercise.category),
      instructions: dbExercise.instructions ?? '',
      imageUrl: '', // Will need to handle images separately
      videoUrl: dbExercise.videoUrl,
      tips: [], // Can extract from instructions if needed
    );
  }

  static WorkoutExercise convertToWorkoutExercise(DBWorkoutExercise dbWorkoutExercise) {
    final exercise = dbWorkoutExercise.exercise != null 
      ? convertToExercise(dbWorkoutExercise.exercise!)
      : Exercise(
          id: dbWorkoutExercise.exerciseId,
          name: dbWorkoutExercise.name,
          muscle: 'Unknown',
          equipment: 'None',
          difficulty: 'Intermediate',
          instructions: '',
          imageUrl: '',
        );
    
    final sets = convertToWorkoutSets(dbWorkoutExercise.reps, dbWorkoutExercise.weight);
    
    return WorkoutExercise(
      exercise: exercise,
      sets: sets,
      restTime: dbWorkoutExercise.restInterval ?? 60,
      notes: null,
    );
  }

  static Workout convertToWorkout(DBWorkout dbWorkout) {
    final exercises = dbWorkout.exercises
      .map((e) => convertToWorkoutExercise(e))
      .toList();
    
    // Extract muscle groups from exercises
    final muscleGroups = <String>{};
    final equipment = <String>{};
    
    for (final exercise in dbWorkout.exercises) {
      if (exercise.exercise != null) {
        if (exercise.exercise!.primaryMuscle != null) {
          muscleGroups.add(exercise.exercise!.primaryMuscle!);
        }
        if (exercise.exercise!.equipment != null && 
            exercise.exercise!.equipment != 'None') {
          equipment.add(exercise.exercise!.equipment!);
        }
      }
    }
    
    return Workout(
      id: dbWorkout.id,
      name: dbWorkout.name,
      description: dbWorkout.notes ?? dbWorkout.aiDescription ?? '',
      imageUrl: '', // Will need to handle images
      category: _getCategoryFromExercises(dbWorkout.exercises),
      difficulty: 'Intermediate', // Could calculate based on exercises
      duration: dbWorkout.duration != null ? dbWorkout.duration! ~/ 60 : 30,
      caloriesBurn: 0, // Can calculate or get from completed_workouts
      equipment: equipment.toList(),
      muscleGroups: muscleGroups.toList(),
      exercises: exercises,
      scheduledDate: dbWorkout.createdAt,
      isCompleted: dbWorkout.isCompleted ?? false,
      isFavorite: false,
    );
  }

  static String _getDifficultyFromCategory(String? category) {
    // Map database categories to difficulty levels
    switch (category?.toLowerCase()) {
      case 'beginner':
        return 'Beginner';
      case 'advanced':
        return 'Advanced';
      default:
        return 'Intermediate';
    }
  }

  static String _getCategoryFromExercises(List<DBWorkoutExercise> exercises) {
    // Determine workout category based on exercise types
    if (exercises.isEmpty) return 'Strength';
    
    final categories = <String>{};
    for (final exercise in exercises) {
      if (exercise.exercise?.category != null) {
        categories.add(exercise.exercise!.category!);
      }
    }
    
    if (categories.contains('strength training')) return 'Strength';
    if (categories.contains('cardio')) return 'Cardio';
    if (categories.contains('flexibility')) return 'Yoga';
    
    return 'Strength'; // Default
  }
}

