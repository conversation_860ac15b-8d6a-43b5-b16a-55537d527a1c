import 'package:flutter/material.dart';

// Exercise model
class Exercise {
  final String id;
  final String name;
  final String muscle;
  final String equipment;
  final String difficulty;
  final String instructions;
  final String imageUrl;
  final String? videoUrl;
  final List<String> tips;
  
  const Exercise({
    required this.id,
    required this.name,
    required this.muscle,
    required this.equipment,
    required this.difficulty,
    required this.instructions,
    required this.imageUrl,
    this.videoUrl,
    this.tips = const [],
  });
}

// Workout Set model
class WorkoutSet {
  final int setNumber;
  final int reps;
  final double? weight;
  final int? duration; // in seconds
  final bool isWarmup;
  bool isCompleted;
  
  WorkoutSet({
    required this.setNumber,
    required this.reps,
    this.weight,
    this.duration,
    this.isWarmup = false,
    this.isCompleted = false,
  });
}

// Exercise in a workout
class WorkoutExercise {
  final Exercise exercise;
  final List<WorkoutSet> sets;
  final int restTime; // in seconds
  final String? notes;
  // Supabase workout_exercises row id used for mutations (reorder/delete/replace)
  final String? sourceId;
  
  WorkoutExercise({
    required this.exercise,
    required this.sets,
    this.restTime = 60,
    this.notes,
    this.sourceId,
  });
  
  int get completedSets => sets.where((s) => s.isCompleted).length;
  int get totalSets => sets.length;
  bool get isCompleted => completedSets == totalSets;
}

// Workout model
class Workout {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final String category; // Strength, Cardio, HIIT, Yoga, etc.
  final String difficulty; // Beginner, Intermediate, Advanced
  final int duration; // in minutes
  final int caloriesBurn;
  final List<String> equipment;
  final List<String> muscleGroups;
  final List<WorkoutExercise> exercises;
  final DateTime? scheduledDate;
  final bool isCompleted;
  final bool isFavorite;
  
  const Workout({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.category,
    required this.difficulty,
    required this.duration,
    required this.caloriesBurn,
    required this.equipment,
    required this.muscleGroups,
    required this.exercises,
    this.scheduledDate,
    this.isCompleted = false,
    this.isFavorite = false,
  });
  
  int get totalExercises => exercises.length;
  int get completedExercises => exercises.where((e) => e.isCompleted).length;
  double get progress => totalExercises > 0 ? completedExercises / totalExercises : 0;
}

// Workout Program (collection of workouts)
class WorkoutProgram {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final int weeks;
  final int workoutsPerWeek;
  final String difficulty;
  final String goal;
  final List<Workout> workouts;
  
  const WorkoutProgram({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.weeks,
    required this.workoutsPerWeek,
    required this.difficulty,
    required this.goal,
    required this.workouts,
  });
}

// Workout categories
class WorkoutCategory {
  final String id;
  final String name;
  final IconData icon;
  final Color color;
  
  const WorkoutCategory({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
  });
}

// Sample categories
class WorkoutCategories {
  static const List<WorkoutCategory> all = [
    WorkoutCategory(
      id: 'strength',
      name: 'Strength',
      icon: Icons.fitness_center,
      color: Color(0xFF3B82F6),
    ),
    WorkoutCategory(
      id: 'cardio',
      name: 'Cardio',
      icon: Icons.directions_run,
      color: Color(0xFFEF4444),
    ),
    WorkoutCategory(
      id: 'hiit',
      name: 'HIIT',
      icon: Icons.whatshot,
      color: Color(0xFFF97316),
    ),
    WorkoutCategory(
      id: 'yoga',
      name: 'Yoga',
      icon: Icons.self_improvement,
      color: Color(0xFF10B981),
    ),
    WorkoutCategory(
      id: 'stretching',
      name: 'Stretching',
      icon: Icons.accessibility_new,
      color: Color(0xFF8B5CF6),
    ),
  ];
}
