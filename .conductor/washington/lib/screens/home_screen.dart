import 'package:flutter/material.dart';
import 'dart:ui';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/screens/workout/workout_screen.dart';
import 'package:openfitv4/widgets/custom_buttons.dart';
import 'package:openfitv4/data/sample_workouts.dart';
import 'package:openfitv4/models/workout_data.dart';
import 'package:openfitv4/screens/workout/workout_detail_screen.dart';
import 'package:openfitv4/widgets/curved_bottom_nav.dart';
import 'package:openfitv4/widgets/theme_selector.dart';
import 'package:provider/provider.dart';
import 'package:openfitv4/providers/auth_provider.dart';
import 'package:openfitv4/providers/workout_provider.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with SingleTickerProviderStateMixin {
  int _selectedIndex = 0;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabScaleAnimation;
  
  @override
  void initState() {
    super.initState();
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fabScaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _fabAnimationController,
        curve: Curves.elasticOut,
      ),
    );
    _fabAnimationController.forward();
    
    // Load workouts when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final workoutProvider = Provider.of<WorkoutProvider>(context, listen: false);
      workoutProvider.initialize();
    });
  }
  
  @override
  void dispose() {
    _fabAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: IndexedStack(
          index: _selectedIndex,
          children: [
            // Dashboard
            _buildDashboard(context),
            // Profile
            _buildProfile(context),
          ],
        ),
      ),
      extendBody: true,
      bottomNavigationBar: CurvedBottomNav(
        currentIndex: _selectedIndex,
        onTap: (i) {
          setState(() {
            _selectedIndex = i;
            _fabAnimationController
              ..reset()
              ..forward();
          });
        },
        leftItem: Icons.home_rounded,
        rightItem: Icons.person_rounded,
        // Center orb is a non-interactive placeholder per requirements.
        centerOrb: null,
        height: 80,
        notchRadius: 34,
        iconSize: 26,
      ),
    );
  }
  
  // legacy bottom bar icon builder removed (icons provided by CurvedBottomNav)
  
  // replaced by non-interactive center orb used in CurvedBottomNav
  
  Widget _buildDashboard(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final workoutProvider = Provider.of<WorkoutProvider>(context);
    
    // Get next workout from provider, fallback to sample if empty
    final activeWorkouts = workoutProvider.activeWorkouts;
    final _nextWorkout = activeWorkouts.isNotEmpty 
        ? activeWorkouts.first 
        : (workoutProvider.allWorkouts.isNotEmpty 
            ? workoutProvider.allWorkouts.first
            : SampleWorkouts.allWorkouts.first);
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.sm),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome back!',
                    style: textTheme.bodyLarge?.copyWith(
                      color: AppColors.grayLight,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Let\'s crush your goals',
                    style: textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ],
              ),
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                  borderRadius: BorderRadius.circular(AppBorderRadius.md),
                ),
                child: Icon(
                  Icons.notifications_none,
                  color: theme.colorScheme.onPrimary,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.lg),
          
          // Today's Stats Card
          Container(
            padding: const EdgeInsets.all(AppSpacing.lg),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  theme.colorScheme.primary,
                  theme.colorScheme.primary.withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.primary.withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'Today\'s Progress',
                  style: textTheme.titleLarge?.copyWith(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.w800,
                    fontSize: 20,
                  ),
                ),
                const SizedBox(height: AppSpacing.lg),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildStatItem(
                      context,
                      icon: Icons.local_fire_department,
                      value: '1,850',
                      label: 'Calories',
                      onPrimary: true,
                    ),
                    Container(
                      width: 1,
                      height: 50,
                      color: theme.colorScheme.onPrimary.withValues(alpha: 0.2),
                    ),
                    _buildStatItem(
                      context,
                      icon: Icons.directions_walk,
                      value: '8,432',
                      label: 'Steps',
                      onPrimary: true,
                    ),
                    Container(
                      width: 1,
                      height: 50,
                      color: theme.colorScheme.onPrimary.withValues(alpha: 0.2),
                    ),
                    _buildStatItem(
                      context,
                      icon: Icons.timer,
                      value: '45',
                      label: 'Minutes',
                      onPrimary: true,
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppSpacing.lg),

          // Ready to train? (Next Workout Card)
          Text(
            'Ready to train?',
            style: textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.w800,
              fontSize: 28,
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            'Your next workout is optimized and ready',
            style: textTheme.bodyLarge?.copyWith(
              color: AppColors.grayLight,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: AppSpacing.md),

          Container(
            decoration: BoxDecoration(
              color: AppColors.surfaceDark,
              borderRadius: BorderRadius.circular(24),
              border: Border.all(
                color: AppColors.grayDark.withValues(alpha: 0.3),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(AppSpacing.lg),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header row with icon + title
                  Row(
                    children: [
                      Container(
                        width: 56,
                        height: 56,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Theme.of(context).colorScheme.primary.withValues(alpha: 0.9),
                              Theme.of(context).colorScheme.primary,
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: const Icon(
                          Icons.fitness_center,
                          color: Colors.white,
                          size: 28,
                        ),
                      ),
                      const SizedBox(width: AppSpacing.md),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _nextWorkout.name,
                              style: textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.w800,
                                fontSize: 22,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              _nextWorkout.category,
                              style: textTheme.bodyLarge?.copyWith(
                                color: AppColors.grayLight,
                                fontSize: 15,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppSpacing.lg),

                  // Stats row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _nextStat(context, Icons.access_time, 'Duration', '${_nextWorkout.duration} min'),
                      Container(
                        width: 1,
                        height: 45,
                        color: AppColors.grayDark.withValues(alpha: 0.3),
                      ),
                      _nextStat(context, Icons.local_fire_department, 'Calories', '${_nextWorkout.caloriesBurn} kcal'),
                      Container(
                        width: 1,
                        height: 45,
                        color: AppColors.grayDark.withValues(alpha: 0.3),
                      ),
                      _nextStat(
                        context,
                        Icons.repeat,
                        'Sets',
                        _nextWorkout.exercises.isNotEmpty
                            ? '${_nextWorkout.exercises.first.sets.length} × ${_nextWorkout.exercises.first.sets.first.reps}'
                            : '0 × 0',
                      ),
                    ],
                  ),

                  const SizedBox(height: AppSpacing.lg),

                  // Focus chips
                  Container(
                    padding: const EdgeInsets.all(AppSpacing.md),
                    decoration: BoxDecoration(
                      color: AppColors.backgroundDark.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: AppColors.grayDark.withValues(alpha: 0.2),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Today's Focus",
                          style: textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w700,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: AppSpacing.sm),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: _buildFocusChips(context, _nextWorkout).take(3).toList(),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: AppSpacing.lg),

                  // CTAs
                  CustomElevatedButton(
                    onPressed: () {
                      // Match the flow from workout page: go to WorkoutDetailScreen first
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (_) => WorkoutDetailScreen(workout: _nextWorkout)),
                      );
                    },
                    text: 'Start Workout',
                    icon: const Icon(Icons.play_arrow, size: 24),
                    borderRadius: 20,
                    height: 60,
                  ),
                  const SizedBox(height: AppSpacing.sm),
                  CustomSecondaryButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (_) => WorkoutDetailScreen(workout: _nextWorkout)),
                      );
                    },
                    text: 'View Details',
                    borderRadius: 20,
                    height: 60,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: AppSpacing.lg),

          // Quick Actions
          Text(
            'Quick Actions',
            style: textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            mainAxisSpacing: AppSpacing.sm,
            crossAxisSpacing: AppSpacing.sm,
            childAspectRatio: 1.5,
            children: [
              _buildQuickAction(
                context,
                icon: Icons.play_arrow,
                title: 'Start Workout',
                subtitle: 'Chest & Triceps',
                color: const Color(0xFF3B82F6),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (_) => const WorkoutScreen()),
                  );
                },
              ),
              _buildQuickAction(
                context,
                icon: Icons.restaurant,
                title: 'Log Meal',
                subtitle: 'Track nutrition',
                color: const Color(0xFF10B981),
              ),
              _buildQuickAction(
                context,
                icon: Icons.water_drop,
                title: 'Hydration',
                subtitle: '5/8 glasses',
                color: const Color(0xFF06B6D4),
              ),
              _buildQuickAction(
                context,
                icon: Icons.bedtime,
                title: 'Sleep',
                subtitle: '7.5 hours',
                color: const Color(0xFF8B5CF6),
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.lg),
          
          // Recent Workouts
          Text(
            'Recent Workouts',
            style: textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          
          _buildWorkoutItem(context, 'Upper Body Strength', 'Yesterday', '45 min'),
          _buildWorkoutItem(context, 'HIIT Cardio', '2 days ago', '30 min'),
          _buildWorkoutItem(context, 'Lower Body Power', '3 days ago', '50 min'),
        ],
      ),
    );
  }
  
  Widget _buildStatItem(
    BuildContext context, {
    required IconData icon,
    required String value,
    required String label,
    bool onPrimary = false,
  }) {
    final theme = Theme.of(context);
    final color = onPrimary ? theme.colorScheme.onPrimary : theme.colorScheme.onSurface;
    
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          icon,
          color: color.withValues(alpha: 0.9),
          size: 28,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: theme.textTheme.headlineSmall?.copyWith(
            color: color,
            fontWeight: FontWeight.w800,
            fontSize: 24,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: color.withValues(alpha: 0.8),
            fontSize: 14,
          ),
        ),
      ],
    );
  }
  
  Widget _buildQuickAction(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    VoidCallback? onTap,
  }) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.sm),
        decoration: BoxDecoration(
          color: AppColors.surfaceDark,
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 28),
            const SizedBox(height: AppSpacing.xs),
            Text(
              title,
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              subtitle,
              style: theme.textTheme.bodySmall?.copyWith(
                color: AppColors.grayLight,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // Helpers for Next Workout card
  Widget _nextStat(BuildContext context, IconData icon, String label, String value) {
    final theme = Theme.of(context);
    return Column(
      children: [
        Icon(icon, size: 20, color: AppColors.grayLight),
        const SizedBox(height: 8),
        Text(
          value,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w800,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: AppColors.grayLight,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  List<Widget> _buildFocusChips(BuildContext context, Workout workout) {
    // Derive focus topics from workout muscle groups or equipment, falling back to 3 items.
    final chips = <String>[];
    if (workout.muscleGroups.isNotEmpty) {
      chips.addAll(workout.muscleGroups.take(3));
    } else if (workout.equipment.isNotEmpty) {
      chips.addAll(workout.equipment.take(3));
    } else if (workout.exercises.isNotEmpty) {
      chips.addAll(workout.exercises.take(3).map((e) => e.exercise.muscle));
    } else {
      chips.addAll(const ['Strength', 'Form', 'Tempo']);
    }
    return chips.map((t) => _focusChip(context, t)).toList();
  }

  Widget _focusChip(BuildContext context, String text) {
    final theme = Theme.of(context);
    return Expanded(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: AppColors.surfaceDark,
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: AppColors.grayDark.withValues(alpha: 0.3),
          ),
        ),
        child: Text(
          text,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  Widget _buildWorkoutItem(BuildContext context, String title, String date, String duration) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.xs),
      padding: const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: AppColors.surfaceDark,
        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppBorderRadius.xs),
            ),
            child: Icon(
              Icons.fitness_center,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  date,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: AppColors.grayLight,
                  ),
                ),
              ],
            ),
          ),
          Text(
            duration,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
  
  
  Widget _buildProfile(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final auth = context.watch<AuthProvider>();
    final profile = auth.user; // nullable until loaded

    String activityLabel(int? c, int? w) {
      if (c == null && w == null) return '—';
      final avg = ((c ?? 1) + (w ?? 1)) / 2.0;
      if (avg >= 3.5) return 'Elite';
      if (avg >= 2.5) return 'Advanced';
      if (avg >= 1.5) return 'Moderate';
      return 'Beginner';
    }

    String titleCase(String s) {
      if (s.isEmpty) return s;
      return s
          .replaceAll('_', ' ')
          .split(' ')
          .map((w) => w.isEmpty ? w : '${w[0].toUpperCase()}${w.substring(1)}')
          .join(' ');
    }

    String goalLabel(String? primary, String? goal, List<dynamic>? arr) {
      if (primary != null && primary.isNotEmpty) return titleCase(primary);
      if (goal != null && goal.isNotEmpty) return titleCase(goal);
      if (arr != null && arr.isNotEmpty) return titleCase('${arr.first}');
      return '—';
    }

    final String email = profile?.email ?? '—';
    final String displayName = profile?.displayName ?? (email != '—' ? email.split('@').first : '—');
    final String weight = profile?.weight != null ? '${profile!.weight!.round()} kg' : '—';
    final String height = profile?.height != null ? '${profile!.height!.round()} cm' : '—';
    final String activity = activityLabel(profile?.cardioFitnessLevel, profile?.weightliftingFitnessLevel);
    final String goal = goalLabel(profile?.fitnessGoalPrimary, null, profile?.fitnessGoalsArray);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.sm),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SizedBox(height: AppSpacing.sm),

          // Profile Header Card
          Container(
            padding: const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              color: AppColors.surfaceDark,
              borderRadius: BorderRadius.circular(AppBorderRadius.lg),
              border: Border.all(
                color: theme.colorScheme.primary.withValues(alpha: 0.15),
              ),
            ),
            child: Row(
              children: [
                // Avatar
                Container(
                  width: 72,
                  height: 72,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        theme.colorScheme.primary.withValues(alpha: 0.9),
                        theme.colorScheme.primary,
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: Icon(
                    Icons.person,
                    size: 40,
                    color: theme.colorScheme.onPrimary,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                // Name & Email
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        displayName,
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w700,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        email,
                        style: textTheme.bodyMedium?.copyWith(
                          color: AppColors.grayLight,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                // Edit button
                TextButton.icon(
                  onPressed: () {
                    // TODO: Navigate to edit profile when implemented
                  },
                  icon: Icon(
                    Icons.edit_outlined,
                    color: theme.colorScheme.primary,
                    size: 18,
                  ),
                  label: Text(
                    'Edit',
                    style: textTheme.labelLarge?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSpacing.sm,
                      vertical: AppSpacing.xs,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: AppSpacing.sm),

          // Quick Stats
          Row(
            children: [
              Expanded(
                child: _buildProfileStatCard(
                  context,
                  icon: Icons.monitor_weight_outlined,
                  label: 'Weight',
                  value: weight,
                ),
              ),
              const SizedBox(width: AppSpacing.xs),
              Expanded(
                child: _buildProfileStatCard(
                  context,
                  icon: Icons.height,
                  label: 'Height',
                  value: height,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.xs),
          Row(
            children: [
              Expanded(
                child: _buildProfileStatCard(
                  context,
                  icon: Icons.directions_run,
                  label: 'Activity',
                  value: activity,
                ),
              ),
              const SizedBox(width: AppSpacing.xs),
              Expanded(
                child: _buildProfileStatCard(
                  context,
                  icon: Icons.flag_outlined,
                  label: 'Goal',
                  value: goal,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppSpacing.sm),

          // Theme Selector
          _buildSectionHeader(context, 'Appearance'),
          const ThemeSelector(),

          const SizedBox(height: AppSpacing.xs),

          // Sections
          _buildSectionHeader(context, 'Account'),
          _buildProfileOption(context, Icons.person_outline, 'Edit Profile'),
          _buildProfileOption(context, Icons.settings, 'Settings'),
          _buildProfileOption(context, Icons.notifications_none, 'Notifications'),

          const SizedBox(height: AppSpacing.xs),

          _buildSectionHeader(context, 'Privacy & Help'),
          _buildProfileOption(context, Icons.lock_outline, 'Privacy'),
          _buildProfileOption(context, Icons.help_outline, 'Help & Support'),
          _buildProfileOption(context, Icons.info_outline, 'About'),

          const SizedBox(height: AppSpacing.xs),

          _buildSectionHeader(context, 'Developer'),
          Container(
            margin: const EdgeInsets.only(bottom: AppSpacing.xs),
            decoration: BoxDecoration(
              color: AppColors.surfaceDark,
              borderRadius: BorderRadius.circular(AppBorderRadius.sm),
              border: Border.all(
                color: theme.colorScheme.primary.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: ListTile(
              leading: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.12),
                  borderRadius: BorderRadius.circular(AppBorderRadius.xs),
                ),
                child: Icon(
                  Icons.restart_alt,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
              ),
              title: Text(
                'Restart Onboarding',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              subtitle: Text(
                'Test login and onboarding flow',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: AppColors.grayLight,
                ),
              ),
              trailing: Icon(
                Icons.chevron_right,
                color: AppColors.grayMedium,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.sm,
                vertical: AppSpacing.xs,
              ),
              onTap: () {
                // In debug builds, allow directly testing onboarding without forcing sign-out.
                if (!bool.fromEnvironment('dart.vm.product')) {
                  // Bypass auth and go straight to onboarding to test the flow.
                  Navigator.of(context).pushNamed('/onboarding');
                  return;
                }
                // Release behavior: go to welcome (sign-in flow).
                Navigator.of(context).pushNamedAndRemoveUntil(
                  '/welcome',
                  (route) => false,
                );
              },
            ),
          ),

          const SizedBox(height: AppSpacing.lg),

          // Sign Out Button
          CustomElevatedButton(
            onPressed: () {
              Navigator.of(context).pushNamedAndRemoveUntil(
                '/welcome',
                (route) => false,
              );
            },
            text: 'Sign Out',
            backgroundColor: theme.colorScheme.error.withValues(alpha: 0.15),
            foregroundColor: theme.colorScheme.error,
          ),
        ],
      ),
    );
  }
  
  Widget _buildProfileOption(BuildContext context, IconData icon, String title) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.xs),
      decoration: BoxDecoration(
        color: AppColors.surfaceDark,
        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        border: Border.all(
          color: AppColors.grayDark,
          width: 1,
        ),
      ),
      child: ListTile(
        leading: Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.12),
            borderRadius: BorderRadius.circular(AppBorderRadius.xs),
          ),
          child: Icon(icon, color: theme.colorScheme.primary, size: 20),
        ),
        title: Text(
          title,
          style: theme.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        trailing: Icon(
          Icons.chevron_right,
          color: AppColors.grayMedium,
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.sm,
          vertical: AppSpacing.xs,
        ),
        onTap: () {
          // Handle option tap
        },
      ),
    );
  }
  Widget _buildProfileStatCard(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.surfaceDark,
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.12),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.12),
              borderRadius: BorderRadius.circular(AppBorderRadius.xs),
            ),
            child: Icon(icon, color: theme.colorScheme.primary, size: 22),
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(label, style: textTheme.bodySmall?.copyWith(color: AppColors.grayLight)),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w700),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
      child: Text(
        title,
        style: theme.textTheme.titleSmall?.copyWith(
          color: AppColors.grayLight,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
