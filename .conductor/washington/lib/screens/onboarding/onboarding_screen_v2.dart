import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/models/onboarding_data.dart';
import 'package:openfitv4/providers/auth_provider.dart';
import 'package:openfitv4/screens/onboarding/pages/page1_about_you.dart';
import 'package:openfitv4/screens/onboarding/pages/page2_fitness_goals.dart';
import 'package:openfitv4/screens/onboarding/pages/page3_fitness_levels.dart';
import 'package:openfitv4/screens/onboarding/pages/page4_equipment.dart';
import 'package:openfitv4/screens/onboarding/pages/page5_schedule.dart';
import 'package:openfitv4/screens/onboarding/pages/page6_additional_info.dart';
import 'package:openfitv4/screens/onboarding/pages/page7_plan_ready.dart';

class OnboardingScreenV2 extends StatefulWidget {
  const OnboardingScreenV2({super.key});

  @override
  State<OnboardingScreenV2> createState() => _OnboardingScreenV2State();
}

class _OnboardingScreenV2State extends State<OnboardingScreenV2> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  
  // Use prefilled test data for easy testing
  late OnboardingData _data;
  
  @override
  void initState() {
    super.initState();
    // Load existing profile from Supabase to prefill onboarding.
    // Falls back to test data if fetch fails.
    // Start empty so UI doesn't lock in test defaults like "John Doe"
    _data = OnboardingData(
      fullName: '',
      gender: null,
      age: null,
      heightInches: null,
      weightPounds: null,
      isMetric: true,
      fitnessGoals: const [],
      goalPriorities: const [],
      cardioLevel: 1,
      weightliftingLevel: 1,
      selectedEquipment: const [],
      workoutsPerWeek: 3,
      sessionDuration: 45,
      workoutDays: const [],
      additionalInfo: '',
      additionalComments: '',
      cardioNotes: '',
      sportDetails: '',
    );
    _prefillFromSupabase();
  }

  void _goToNextPage() {
    if (_canGoNext()) {
      if (_currentPage < 6) {
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  void _goToPreviousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _prefillFromSupabase() async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final client = Supabase.instance.client;

      // Prefer RPC if available; fall back to view.
      List<dynamic> rows = [];
      try {
        final res = await client.rpc('get_onboarding_preload');
        if (res is List && res.isNotEmpty) {
          rows = res;
        }
      } catch (_) {
        // RPC may not exist; ignore and try the view.
      }

      if (rows.isEmpty) {
        final userId = authProvider.user?.id;
        if (userId != null) {
          final viewRes = await client
              .from('onboarding_preload')
              .select()
              .eq('id', userId)
              .limit(1);
          if (viewRes.isNotEmpty) {
            rows = viewRes;
          }
        }
      }

      if (rows.isEmpty) {
        // Try directly from profiles table as fallback
        final userId = authProvider.user?.id;
        if (userId != null) {
          final profileRes = await client
              .from('profiles')
              .select()
              .eq('id', userId)
              .limit(1);
          if (profileRes.isNotEmpty) {
            rows = profileRes;
          }
        }
      }

      if (rows.isEmpty) return;

      final row = rows.first as Map<String, dynamic>;

      // Map DB values into onboarding model safely
      final gender = (row['gender'] as String?)?.trim();
      final age = row['age'] is int ? row['age'] as int : int.tryParse('${row['age'] ?? ''}');
      final heightStr = row['height']?.toString();
      final weightStr = row['weight']?.toString();
      final heightUnit = (row['height_unit'] as String?) ?? 'cm';
      final weightUnit = (row['weight_unit'] as String?) ?? 'kg';

      // Convert to model's expected units (model stores inches/pounds)
      double? heightInches;
      if (heightStr != null && heightStr.isNotEmpty) {
        final h = double.tryParse(heightStr);
        if (h != null) {
          heightInches = heightUnit.toLowerCase() == 'cm' ? (h / 2.54) : h; // assume already inches otherwise
        }
      }

      double? weightPounds;
      if (weightStr != null && weightStr.isNotEmpty) {
        final w = double.tryParse(weightStr);
        if (w != null) {
          weightPounds = weightUnit.toLowerCase() == 'kg' ? (w / 0.453592) : w; // assume already lbs otherwise
        }
      }

      // Fitness goals - handle multiple formats
      List<String> fitnessGoalsList = [];
      if (row['fitness_goals_array'] != null && row['fitness_goals_array'] is List) {
        fitnessGoalsList = (row['fitness_goals_array'] as List).cast<String>();
      } else if (row['fitness_goal_unified'] != null || row['fitness_goal'] != null) {
        final dynamicGoal = row['fitness_goal_unified'] ?? row['fitness_goal'] ?? row['fitness_goal_primary'];
        if (dynamicGoal is String && dynamicGoal.isNotEmpty) {
          fitnessGoalsList = [dynamicGoal];
        }
      }

      // Goal priorities/order
      final goalPriorities = (row['fitness_goals_order'] as List?)?.cast<String>() ?? [];

      // Optional arrays/ints
      final workoutDays = (row['workout_days'] as List?)?.cast<String>() ?? [];
      final equipment = (row['equipment'] as List?)?.cast<String>() ?? [];
      final workoutFrequency = row['workout_frequency'] is int ? row['workout_frequency'] as int : int.tryParse('${row['workout_frequency'] ?? ''}');
      final workoutDuration = row['workout_duration'] is int ? row['workout_duration'] as int : int.tryParse('${row['workout_duration'] ?? ''}');
      final cardioLevel = row['cardio_fitness_level'] is int ? row['cardio_fitness_level'] as int : int.tryParse('${row['cardio_fitness_level'] ?? ''}') ?? 1;
      final weightliftingLevel = row['weightlifting_fitness_level'] is int ? row['weightlifting_fitness_level'] as int : int.tryParse('${row['weightlifting_fitness_level'] ?? ''}') ?? 1;

      // Build a new OnboardingData instance from Supabase values
      // Prefer display_name; fallback to email local-part so it's never empty
      String? fallbackNameFromEmail(String? email) {
        if (email == null || email.isEmpty) return null;
        final parts = email.split('@');
        return parts.isNotEmpty ? parts.first : null;
      }

      final mapped = OnboardingData(
        fullName: (row['display_name'] as String?)?.trim().isNotEmpty == true
            ? (row['display_name'] as String).trim()
            : (fallbackNameFromEmail(row['email'] as String?) ?? ''),
        gender: (gender == null || gender.isEmpty) ? null : gender.toLowerCase(),
        otherGenderText: null,
        age: age,
        heightInches: heightInches,
        weightPounds: weightPounds,
        isMetric: true, // DB stores metric; UI converts
        fitnessGoals: fitnessGoalsList,
        goalPriorities: goalPriorities,
        sportDetails: (row['specific_sport_activity'] as String?) ?? (row['sport_of_choice'] as String?) ?? (row['sport_activity'] as String?),
        cardioLevel: cardioLevel.clamp(0, 4),
        cardioNotes: (row['fitness_experience'] as String?) ?? (row['cardio_level_description'] as String?) ?? '',
        weightliftingLevel: weightliftingLevel.clamp(0, 4),
        additionalComments: (row['additional_health_info'] as String?) ?? '',
        selectedEquipment: equipment,
        workoutsPerWeek: workoutFrequency ?? 3,
        sessionDuration: workoutDuration ?? 45,
        noTimePreference: row['schedule_flexibility'] == 'very_flexible' || row['schedule_flexibility'] == 'flexible',
        workoutDays: _mapDayNamesToIndices(workoutDays),
        noDayPreference: row['schedule_flexibility'] == 'very_flexible',
        additionalInfo: (row['additional_notes'] as String?) ?? '',
      );

      setState(() {
        _data = mapped;
      });
    } catch (e) {
      // Silent fallback to test data if anything fails
    }
  }

  List<int> _mapDayNamesToIndices(List<String> days) {
    const map = {
      'sunday': 0,
      'monday': 1,
      'tuesday': 2,
      'wednesday': 3,
      'thursday': 4,
      'friday': 5,
      'saturday': 6,
    };
    return days
        .map((d) => map[d.toString().toLowerCase()] )
        .whereType<int>()
        .toList();
  }
  
  bool _canGoNext() {
    switch (_currentPage) {
      case 0: return _data.isPage1Valid;
      case 1: return _data.isPage2Valid;
      case 2: return _data.isPage3Valid;
      case 3: return _data.isPage4Valid;
      case 4: return _data.isPage5Valid;
      case 5: return _data.isPage6Valid;
      case 6: return true;
      default: return false;
    }
  }
  
  Future<void> _generatePlan() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    try {
      // Persist completed onboarding before plan generation/navigation
      final payload = _convertToSupabaseFormat(_data);

      // Visual feedback
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Saving your fitness profile...')),
      );

      final ok = await authProvider.completeOnboarding(payload);

      if (!mounted) return;
      
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      if (!mounted) return;

      if (ok) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Profile saved successfully!'),
            backgroundColor: Theme.of(context).colorScheme.primary,
            duration: const Duration(seconds: 1),
          ),
        );
        // TODO: trigger actual plan generation or navigate if needed
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authProvider.error ?? 'Failed to save profile'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  Map<String, dynamic> _convertToSupabaseFormat(OnboardingData data) {
    // Derive schedule fields consistently
    final days = data.workoutDays.map((i) => _getDayName(i)).toList();
    final preferredDaysCount = days.length;
    final preferredDuration = data.sessionDuration != null ? '${data.sessionDuration} minutes' : null;
    final scheduleFlex =
        data.noTimePreference && data.noDayPreference ? 'very_flexible' :
        (data.noTimePreference || data.noDayPreference) ? 'flexible' : 'fixed';

    return {
      // Identity
      'display_name': data.fullName,
      'gender': data.gender == 'other' ? data.otherGenderText?.toLowerCase() : data.gender?.toLowerCase(),
      'age': data.age,

      // Anthropometrics (stored in metric)
      'height': data.heightInches != null ? (data.heightInches! * 2.54) : null,
      'height_unit': 'cm',
      'weight': data.weightPounds != null ? (data.weightPounds! * 0.453592) : null,
      'weight_unit': 'kg',

      // Goals
      'fitness_goals_array': data.fitnessGoals,
      'fitness_goal_primary': data.fitnessGoals.isNotEmpty ? data.fitnessGoals.first : null,
      'fitness_goals_order': data.goalPriorities,
      'sport_of_choice': data.sportDetails,
      'specific_sport_activity': data.sportDetails,

      // Levels
      'cardio_fitness_level': data.cardioLevel,
      'weightlifting_fitness_level': data.weightliftingLevel,
      'fitness_experience': data.cardioNotes,
      'training_experience_level': _getExperienceLevel(data.weightliftingLevel),
      'fitness_level': _getOverallFitnessLevel(data.cardioLevel, data.weightliftingLevel),

      // Equipment & environment
      'equipment': data.selectedEquipment,

      // Schedule (write to all related columns used by your schema)
      'workout_frequency': data.workoutsPerWeek,
      'workout_duration': data.sessionDuration,
      'workout_days': days,
      'preferred_workout_days_count': preferredDaysCount,
      'preferred_workout_duration': preferredDuration,
      'schedule_flexibility': scheduleFlex,

      // Back-compat aliases seen in your row
      'workout_frequency_days': data.workoutsPerWeek,
      'workout_duration_preference': preferredDuration,

      // Notes
      'additional_health_info': data.additionalComments,
      'additional_notes': data.additionalInfo,

      // App state flags
      'onboarding_completed': true,
      'fitness_assessment_completed': true,
      'has_completed_preferences': true,

      // Metadata
      'metadata': {
        'onboarding_version': '2.0',
        'onboarding_completed_at': DateTime.now().toIso8601String(),
        'time_preference': data.noTimePreference ? 'flexible' : 'fixed',
        'day_preference': data.noDayPreference ? 'flexible' : 'fixed',
        'is_metric': data.isMetric,
        'original_height_inches': data.heightInches,
        'original_weight_pounds': data.weightPounds,
        'other_gender_text': data.gender == 'other' ? data.otherGenderText : null,
      },
    };
  }

  String _getDayName(int dayIndex) {
    const days = [
      'Sunday', 'Monday', 'Tuesday', 'Wednesday', 
      'Thursday', 'Friday', 'Saturday'
    ];
    return days[dayIndex];
  }

  String _getExperienceLevel(int level) {
    switch (level) {
      case 0: return 'beginner';
      case 1: return 'beginner';
      case 2: return 'intermediate';
      case 3: return 'advanced';
      case 4: return 'elite';
      default: return 'intermediate';
    }
  }

  int _getOverallFitnessLevel(int cardioLevel, int weightliftingLevel) {
    return ((cardioLevel + weightliftingLevel) / 2).round();
  }
  

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Header with navigation
            Container(
              padding: const EdgeInsets.all(AppSpacing.sm),
              child: Row(
                children: [
                  // Back button
                  if (_currentPage > 0)
                    GestureDetector(
                      onTap: _goToPreviousPage,
                      child: Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: AppColors.surfaceDark,
                          borderRadius: BorderRadius.circular(18),
                        ),
                        child: Icon(
                          Icons.arrow_back,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    )
                  else
                    GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: AppColors.surfaceDark,
                          borderRadius: BorderRadius.circular(18),
                        ),
                        child: Icon(
                          Icons.close,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ),
                  
                  const SizedBox(width: AppSpacing.sm),
                  
                  // Title
                  Expanded(
                    child: Text(
                      'Assessment',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  
                  const SizedBox(width: AppSpacing.sm),
                  
                  // Progress indicator
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSpacing.sm,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF172554),
                      borderRadius: BorderRadius.circular(11),
                    ),
                    child: Text(
                      '${_currentPage + 1}/7',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // PageView
            Expanded(
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                children: [
                  Page1AboutYou(
                    data: _data,
                    onDataChanged: () => setState(() {}),
                  ),
                  Page2FitnessGoals(
                    data: _data,
                    onDataChanged: () => setState(() {}),
                  ),
                  Page3FitnessLevels(
                    data: _data,
                    onDataChanged: () => setState(() {}),
                  ),
                  Page4Equipment(
                    data: _data,
                    onDataChanged: () => setState(() {}),
                  ),
                  Page5Schedule(
                    data: _data,
                    onDataChanged: () => setState(() {}),
                  ),
                  Page6AdditionalInfo(
                    data: _data,
                    onDataChanged: () => setState(() {}),
                  ),
                  Page7PlanReady(
                    data: _data,
                    // Ensure we persist BEFORE leaving this screen
                    onGeneratePlan: () async {
                      await _generatePlan();
                    },
                    onEditAnswers: () {
                      _pageController.animateToPage(
                        0,
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    },
                    onSaveAndExit: () async {
                      // Save a draft without marking completed
                      final authProvider = Provider.of<AuthProvider>(context, listen: false);
                      final draft = _convertToSupabaseFormat(_data);
                      draft['onboarding_completed'] = false; // leave incomplete
                      draft['fitness_assessment_completed'] = false;
                      draft['has_completed_preferences'] = true; // you completed preferences pages
                      try {
                        final ok = await authProvider.completeOnboarding(draft);
                        if (!mounted) return;
                        
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(ok ? 'Progress saved' : (authProvider.error ?? 'Failed to save')),
                            backgroundColor: ok ? Theme.of(context).colorScheme.primary : Theme.of(context).colorScheme.error,
                          ),
                        );
                        if (ok) {
                          Navigator.pop(context);
                        }
                      } catch (e) {
                        if (!mounted) return;
                        
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Error saving draft: $e'),
                            backgroundColor: Theme.of(context).colorScheme.error,
                          ),
                        );
                      }
                    },
                  ),
                ],
              ),
            ),
            
            // Bottom navigation
            if (_currentPage < 6)
              Container(
                padding: const EdgeInsets.all(AppSpacing.sm),
                child: SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton(
                    onPressed: _canGoNext() ? _goToNextPage : null,
                    style: theme.elevatedButtonTheme.style?.copyWith(
                      shape: WidgetStateProperty.all(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(19),
                        ),
                      ),
                    ),
                    child: Text(
                      _currentPage < 5 ? 'Continue' : 'Complete',
                      style: theme.textTheme.titleMedium,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
