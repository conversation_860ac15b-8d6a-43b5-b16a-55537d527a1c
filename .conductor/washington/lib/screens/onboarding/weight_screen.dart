import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:openfitv4/theme/app_theme.dart';

class WeightScreen extends StatefulWidget {
  final Function(double) onNext;
  final double? selectedWeight;
  
  const WeightScreen({
    super.key,
    required this.onNext,
    this.selectedWeight,
  });

  @override
  State<WeightScreen> createState() => _WeightScreenState();
}

class _WeightScreenState extends State<WeightScreen> {
  late double _selectedWeight;
  late FixedExtentScrollController _kgController;
  late FixedExtentScrollController _decimalController;
  bool _isKg = true;

  @override
  void initState() {
    super.initState();
    _selectedWeight = widget.selectedWeight ?? 70.0;
    _kgController = FixedExtentScrollController(
      initialItem: _selectedWeight.floor() - 30,
    );
    _decimalController = FixedExtentScrollController(
      initialItem: ((_selectedWeight % 1) * 10).round(),
    );
  }

  @override
  void dispose() {
    _kgController.dispose();
    _decimalController.dispose();
    super.dispose();
  }


  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Question
          Text(
            'What is your weight?',
            style: textTheme.headlineMedium?.copyWith(
              fontSize: 30,
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppSpacing.xxl),
          
          // Weight picker
          Container(
            height: 200,
            decoration: BoxDecoration(
              color: AppColors.surfaceDark,
              borderRadius: BorderRadius.circular(32),
            ),
            child: Stack(
              children: [
                // Selection indicator
                Center(
                  child: Container(
                    height: 60,
                    margin: const EdgeInsets.symmetric(horizontal: AppSpacing.lg),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: theme.colorScheme.primary.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                  ),
                ),
                // Picker
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Whole numbers
                    SizedBox(
                      width: 100,
                      child: CupertinoPicker(
                        scrollController: _kgController,
                        itemExtent: 60,
                        onSelectedItemChanged: (index) {
                          setState(() {
                            final whole = index + 30;
                            final decimal = _selectedWeight % 1;
                            _selectedWeight = whole + decimal;
                          });
                        },
                        children: List.generate(170, (index) {
                          final weight = index + 30;
                          final isSelected = weight == _selectedWeight.floor();
                          return Center(
                            child: Text(
                              '$weight',
                              style: textTheme.displaySmall?.copyWith(
                                fontSize: isSelected ? 48 : 32,
                                fontWeight: FontWeight.w700,
                                color: isSelected
                                  ? theme.colorScheme.primary
                                  : AppColors.grayMedium,
                              ),
                            ),
                          );
                        }),
                      ),
                    ),
                    // Decimal point
                    Text(
                      '.',
                      style: textTheme.displaySmall?.copyWith(
                        fontSize: 48,
                        fontWeight: FontWeight.w700,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    // Decimal numbers
                    SizedBox(
                      width: 60,
                      child: CupertinoPicker(
                        scrollController: _decimalController,
                        itemExtent: 60,
                        onSelectedItemChanged: (index) {
                          setState(() {
                            final whole = _selectedWeight.floor();
                            _selectedWeight = whole + (index / 10);
                          });
                        },
                        children: List.generate(10, (index) {
                          final isSelected = index == ((_selectedWeight % 1) * 10).round();
                          return Center(
                            child: Text(
                              '$index',
                              style: textTheme.displaySmall?.copyWith(
                                fontSize: isSelected ? 48 : 32,
                                fontWeight: FontWeight.w700,
                                color: isSelected
                                  ? theme.colorScheme.primary
                                  : AppColors.grayMedium,
                              ),
                            ),
                          );
                        }),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppSpacing.lg),
          
          // Unit toggle
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildUnitToggle(
                context,
                label: 'kg',
                isSelected: _isKg,
                onTap: () {
                  setState(() {
                    _isKg = true;
                  });
                },
              ),
              const SizedBox(width: AppSpacing.xs),
              _buildUnitToggle(
                context,
                label: 'lbs',
                isSelected: !_isKg,
                onTap: () {
                  setState(() {
                    _isKg = false;
                  });
                },
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.xxl),
          
          // Next button
          SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: () {
                widget.onNext(_selectedWeight);
              },
              style: theme.elevatedButtonTheme.style?.copyWith(
                shape: WidgetStateProperty.all(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(19),
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Next',
                    style: textTheme.titleMedium,
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  const Icon(Icons.arrow_forward, size: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildUnitToggle(
    BuildContext context, {
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.xs,
        ),
        decoration: BoxDecoration(
          color: isSelected
            ? theme.colorScheme.primary
            : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: isSelected
            ? null
            : Border.all(
                color: AppColors.grayMedium,
                width: 1,
              ),
        ),
        child: Text(
          label,
          style: theme.textTheme.titleMedium?.copyWith(
            color: isSelected
              ? theme.colorScheme.onPrimary
              : AppColors.grayLight,
          ),
        ),
      ),
    );
  }
}