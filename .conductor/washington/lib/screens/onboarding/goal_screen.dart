import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';

class GoalScreen extends StatefulWidget {
  final Function(String) onNext;
  final String? selectedGoal;
  
  const GoalScreen({
    super.key,
    required this.onNext,
    this.selectedGoal,
  });

  @override
  State<GoalScreen> createState() => _GoalScreenState();
}

class _GoalScreenState extends State<GoalScreen> {
  String? _selectedGoal;

  final List<Map<String, dynamic>> _goals = [
    {
      'id': 'lose_weight',
      'title': 'Lose Weight',
      'description': 'Burn fat and slim down',
      'icon': Icons.trending_down,
      'color': Color(0xFFEF4444), // Red
    },
    {
      'id': 'gain_muscle',
      'title': 'Gain Muscle',
      'description': 'Build strength and muscle mass',
      'icon': Icons.fitness_center,
      'color': Color(0xFF3B82F6), // Blue
    },
    {
      'id': 'get_fitter',
      'title': 'Get Fitter',
      'description': 'Improve overall fitness',
      'icon': Icons.favorite,
      'color': Color(0xFF10B981), // Green
    },
    {
      'id': 'gain_weight',
      'title': 'Gain Weight',
      'description': 'Healthy weight gain',
      'icon': Icons.trending_up,
      'color': Color(0xFF8B5CF6), // Purple
    },
    {
      'id': 'maintain_weight',
      'title': 'Maintain Weight',
      'description': 'Stay at current weight',
      'icon': Icons.balance,
      'color': Color(0xFFF59E0B), // Amber
    },
    {
      'id': 'improve_health',
      'title': 'Improve Health',
      'description': 'Focus on overall wellness',
      'icon': Icons.health_and_safety,
      'color': Color(0xFF06B6D4), // Cyan
    },
  ];

  @override
  void initState() {
    super.initState();
    _selectedGoal = widget.selectedGoal;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
      child: Column(
        children: [
          const SizedBox(height: AppSpacing.lg),
          
          // Question
          Text(
            'What is your goal?',
            style: textTheme.headlineMedium?.copyWith(
              fontSize: 30,
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppSpacing.xs),
          
          // Subtitle
          Text(
            'We\'ll create a personalized plan for you',
            style: textTheme.bodyLarge?.copyWith(
              color: AppColors.grayLight,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          // Goal options in grid
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisSpacing: AppSpacing.xs,
              crossAxisSpacing: AppSpacing.xs,
              childAspectRatio: 1.1,
            ),
            itemCount: _goals.length,
            itemBuilder: (context, index) {
              final goal = _goals[index];
              final isSelected = _selectedGoal == goal['id'];
              return _buildGoalOption(
                context,
                id: goal['id'],
                title: goal['title'],
                description: goal['description'],
                icon: goal['icon'],
                color: goal['color'],
                isSelected: isSelected,
                onTap: () {
                  setState(() {
                    _selectedGoal = goal['id'];
                  });
                },
              );
            },
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          // Complete button
          SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: _selectedGoal != null
                ? () {
                    widget.onNext(_selectedGoal!);
                  }
                : null,
              style: theme.elevatedButtonTheme.style?.copyWith(
                shape: WidgetStateProperty.all(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(19),
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Complete',
                    style: textTheme.titleMedium,
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  const Icon(Icons.check, size: 20),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: AppSpacing.lg),
        ],
      ),
    );
  }
  
  Widget _buildGoalOption(
    BuildContext context, {
    required String id,
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.sm),
        decoration: BoxDecoration(
          color: AppColors.surfaceDark,
          borderRadius: BorderRadius.circular(19),
          border: isSelected
            ? Border.all(
                color: color,
                width: 2,
              )
            : null,
          boxShadow: isSelected
            ? [
                BoxShadow(
                  color: color.withValues(alpha: 0.25),
                  blurRadius: 0,
                  spreadRadius: 4,
                ),
              ]
            : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: isSelected
                  ? color.withValues(alpha: 0.2)
                  : AppColors.grayDark.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: isSelected ? color : AppColors.grayLight,
                size: 24,
              ),
            ),
            
            const SizedBox(height: AppSpacing.xs),
            
            // Title
            Text(
              title,
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: isSelected
                  ? color
                  : theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 2),
            
            // Description
            Text(
              description,
              style: theme.textTheme.bodySmall?.copyWith(
                color: AppColors.grayLight,
                fontSize: 11,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}