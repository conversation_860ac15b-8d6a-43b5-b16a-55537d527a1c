import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/models/onboarding_data.dart';

class Page2FitnessGoals extends StatefulWidget {
  final OnboardingData data;
  final VoidCallback onDataChanged;
  
  const Page2FitnessGoals({
    super.key,
    required this.data,
    required this.onDataChanged,
  });

  @override
  State<Page2FitnessGoals> createState() => _Page2FitnessGoalsState();
}

class _Page2FitnessGoalsState extends State<Page2FitnessGoals> {
  late TextEditingController _sportController;
  
  @override
  void initState() {
    super.initState();
    _sportController = TextEditingController(text: widget.data.sportDetails);
  }
  
  @override
  void dispose() {
    _sportController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    
    // Keep controller in sync with latest model data
    if (_sportController.text != (widget.data.sportDetails ?? '')) {
      final newText = widget.data.sportDetails ?? '';
      _sportController
        ..text = newText
        ..selection = TextSelection.fromPosition(TextPosition(offset: newText.length));
    }
    
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppSpacing.lg),
          
          // Title
          Text(
            "What's your fitness goal?",
            style: textTheme.headlineMedium?.copyWith(
              fontSize: 32,
              fontWeight: FontWeight.w700,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xs),
          
          // Helper text
          Text(
            'Select all that apply',
            style: textTheme.bodyLarge?.copyWith(
              color: AppColors.grayLight,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          // Goal cards
          ...FitnessGoal.catalog.map((goal) {
            final isSelected = widget.data.fitnessGoals.contains(goal['id']);
            return Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.xs),
              child: _buildGoalCard(
                goal['id'] as String,
                goal['title'] as String,
                goal['description'] as String,
                _getIconData(goal['icon'] as String),
                isSelected,
                goal['requiresDetails'] == true,
              ),
            );
          }),
          
          // Priority order (if goals selected)
          if (widget.data.fitnessGoals.isNotEmpty) ...[
            const SizedBox(height: AppSpacing.lg),
            Text(
              'Sort your priorities',
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              'Drag to reorder',
              style: textTheme.bodySmall?.copyWith(
                color: AppColors.grayLight,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Container(
              decoration: BoxDecoration(
                color: AppColors.surfaceDark,
                borderRadius: BorderRadius.circular(16),
              ),
              child: ReorderableListView(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                onReorder: (oldIndex, newIndex) {
                  setState(() {
                    if (newIndex > oldIndex) {
                      newIndex -= 1;
                    }
                    final item = widget.data.goalPriorities.removeAt(oldIndex);
                    widget.data.goalPriorities.insert(newIndex, item);
                    widget.onDataChanged();
                  });
                },
                children: widget.data.goalPriorities.map((goalId) {
                  final goal = FitnessGoal.catalog.firstWhere(
                    (g) => g['id'] == goalId,
                  );
                  return ListTile(
                    key: ValueKey(goalId),
                    leading: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        _getIconData(goal['icon'] as String),
                        color: theme.colorScheme.primary,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      goal['title'] as String,
                      style: textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    trailing: Icon(
                      Icons.drag_handle,
                      color: AppColors.grayMedium,
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
          
          const SizedBox(height: AppSpacing.sm),
          
          // Tip
          Container(
            padding: const EdgeInsets.all(AppSpacing.sm),
            decoration: BoxDecoration(
              color: AppColors.surfaceDark,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: AppSpacing.xs),
                Expanded(
                  child: Text(
                    'You can change this anytime',
                    style: textTheme.bodySmall?.copyWith(
                      color: AppColors.grayLight,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppSpacing.xl),
        ],
      ),
    );
  }
  
  Widget _buildGoalCard(
    String id,
    String title,
    String description,
    IconData icon,
    bool isSelected,
    bool requiresDetails,
  ) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: () {
        setState(() {
          if (isSelected) {
            widget.data.fitnessGoals.remove(id);
            widget.data.goalPriorities.remove(id);
            if (id == 'sport') {
              widget.data.sportDetails = null;
            }
          } else {
            widget.data.fitnessGoals.add(id);
            widget.data.goalPriorities.add(id);
          }
          widget.onDataChanged();
        });
      },
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.sm),
        decoration: BoxDecoration(
          color: AppColors.surfaceDark,
          borderRadius: BorderRadius.circular(16),
          border: isSelected
            ? Border.all(
                color: theme.colorScheme.primary,
                width: 2,
              )
            : null,
          boxShadow: isSelected
            ? [
                BoxShadow(
                  color: theme.colorScheme.primary.withValues(alpha: 0.25),
                  blurRadius: 0,
                  spreadRadius: 2,
                ),
              ]
            : null,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: isSelected
                      ? theme.colorScheme.primary.withValues(alpha: 0.2)
                      : AppColors.grayDark.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: isSelected
                      ? theme.colorScheme.primary
                      : AppColors.grayLight,
                    size: 24,
                  ),
                ),
                const SizedBox(width: AppSpacing.sm),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isSelected
                            ? theme.colorScheme.primary
                            : theme.colorScheme.onSurface,
                        ),
                      ),
                      Text(
                        description,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: AppColors.grayLight,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (requiresDetails && isSelected) ...[
              const SizedBox(height: AppSpacing.sm),
              TextFormField(
                controller: _sportController,
                style: theme.textTheme.bodyMedium,
                onChanged: (value) {
                  widget.data.sportDetails = value;
                  widget.onDataChanged();
                },
                decoration: InputDecoration(
                  hintText: 'e.g., Marathon, Volleyball...',
                  filled: true,
                  fillColor: AppColors.backgroundDark,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: AppSpacing.sm,
                    vertical: AppSpacing.xs,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'sports_soccer': return Icons.sports_soccer;
      case 'fitness_center': return Icons.fitness_center;
      case 'directions_run': return Icons.directions_run;
      case 'favorite': return Icons.favorite;
      case 'trending_down': return Icons.trending_down;
      default: return Icons.fitness_center;
    }
  }
}