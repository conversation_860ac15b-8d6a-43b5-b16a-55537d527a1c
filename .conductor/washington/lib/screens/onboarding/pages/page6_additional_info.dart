import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/models/onboarding_data.dart';

class Page6AdditionalInfo extends StatefulWidget {
  final OnboardingData data;
  final VoidCallback onDataChanged;
  
  const Page6AdditionalInfo({
    super.key,
    required this.data,
    required this.onDataChanged,
  });

  @override
  State<Page6AdditionalInfo> createState() => _Page6AdditionalInfoState();
}

class _Page6AdditionalInfoState extends State<Page6AdditionalInfo> {
  late TextEditingController _controller;
  
  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.data.additionalInfo);
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    
    // Keep controller in sync with latest model data
    if (_controller.text != (widget.data.additionalInfo ?? '')) {
      final newText = widget.data.additionalInfo ?? '';
      _controller
        ..text = newText
        ..selection = TextSelection.fromPosition(TextPosition(offset: newText.length));
    }
    
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppSpacing.lg),
          
          Text(
            'Anything else we should know?',
            style: textTheme.headlineMedium?.copyWith(
              fontSize: 28,
              fontWeight: FontWeight.w700,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xs),
          
          Text(
            'Share any additional information that might help us personalize your experience better.',
            style: textTheme.bodyLarge?.copyWith(
              color: AppColors.grayLight,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          TextFormField(
            controller: _controller,
            style: textTheme.bodyLarge,
            maxLines: 8,
            maxLength: 1000,
            onChanged: (value) {
              widget.data.additionalInfo = value;
              widget.onDataChanged();
            },
            decoration: InputDecoration(
              hintText: 'Examples: previous injuries, specific goals, equipment you have access to, or any other relevant details...',
              hintStyle: textTheme.bodyMedium?.copyWith(
                color: AppColors.grayMedium,
              ),
              filled: true,
              fillColor: AppColors.surfaceDark,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide.none,
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide(
                  color: theme.colorScheme.primary,
                  width: 2,
                ),
              ),
              counterText: '',
            ),
          ),
          
          const SizedBox(height: AppSpacing.sm),
          
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpacing.xs,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: AppColors.surfaceDark,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'Optional',
                  style: textTheme.bodySmall?.copyWith(
                    color: AppColors.grayLight,
                  ),
                ),
              ),
              const Spacer(),
              Text(
                '${_controller.text.length}/1000',
                style: textTheme.bodySmall?.copyWith(
                  color: AppColors.grayMedium,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.lg),
          
          Container(
            padding: const EdgeInsets.all(AppSpacing.sm),
            decoration: BoxDecoration(
              color: AppColors.surfaceDark,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.lock_outline,
                  color: AppColors.grayLight,
                  size: 20,
                ),
                const SizedBox(width: AppSpacing.xs),
                Expanded(
                  child: Text(
                    'Your information is kept private and only used to enhance your experience.',
                    style: textTheme.bodySmall?.copyWith(
                      color: AppColors.grayLight,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppSpacing.xl),
        ],
      ),
    );
  }
}