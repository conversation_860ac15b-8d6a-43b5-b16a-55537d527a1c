import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/models/onboarding_data.dart';

class Page4Equipment extends StatefulWidget {
  final OnboardingData data;
  final VoidCallback onDataChanged;
  
  const Page4Equipment({
    super.key,
    required this.data,
    required this.onDataChanged,
  });

  @override
  State<Page4Equipment> createState() => _Page4EquipmentState();
}

class _Page4EquipmentState extends State<Page4Equipment> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppSpacing.lg),
          
          Text(
            'Do you have equipment?',
            style: textTheme.headlineMedium?.copyWith(
              fontSize: 28,
              fontWeight: FontWeight.w700,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xs),
          
          Text(
            'Select all equipment you have access to',
            style: textTheme.bodyLarge?.copyWith(
              color: AppColors.grayLight,
            ),
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          // Equipment count
          Container(
            padding: const EdgeInsets.all(AppSpacing.sm),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${widget.data.selectedEquipment.length} equipments selected',
              style: textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          // Presets
          Row(
            children: [
              _buildPresetButton('Minimal home'),
              const SizedBox(width: AppSpacing.xs),
              _buildPresetButton('Commercial gym'),
              const SizedBox(width: AppSpacing.xs),
              _buildPresetButton('None'),
            ],
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          // Equipment grid
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              mainAxisSpacing: AppSpacing.xs,
              crossAxisSpacing: AppSpacing.xs,
              childAspectRatio: 1,
            ),
            itemCount: Equipment.catalog.length,
            itemBuilder: (context, index) {
              final equipment = Equipment.catalog[index];
              final isSelected = widget.data.selectedEquipment.contains(equipment['id']);
              
              return GestureDetector(
                onTap: () {
                  setState(() {
                    if (isSelected) {
                      widget.data.selectedEquipment.remove(equipment['id']);
                    } else {
                      widget.data.selectedEquipment.add(equipment['id'] as String);
                    }
                    widget.onDataChanged();
                  });
                },
                child: Container(
                  padding: const EdgeInsets.all(AppSpacing.xs),
                  decoration: BoxDecoration(
                    color: isSelected
                      ? theme.colorScheme.primary.withValues(alpha: 0.2)
                      : AppColors.surfaceDark,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected
                        ? theme.colorScheme.primary
                        : AppColors.grayDark,
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.fitness_center,
                        color: isSelected
                          ? theme.colorScheme.primary
                          : AppColors.grayLight,
                        size: 28,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        equipment['name'] as String,
                        style: textTheme.bodySmall?.copyWith(
                          fontSize: 10,
                          color: isSelected
                            ? theme.colorScheme.primary
                            : AppColors.grayLight,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
          
          const SizedBox(height: AppSpacing.xl),
        ],
      ),
    );
  }
  
  Widget _buildPresetButton(String label) {
    final theme = Theme.of(context);
    
    return Expanded(
      child: OutlinedButton(
        onPressed: () {
          setState(() {
            if (label == 'None') {
              widget.data.selectedEquipment.clear();
            } else if (label == 'Minimal home') {
              widget.data.selectedEquipment = [
                'bodyweight',
                'dumbbells_light',
                'bands',
              ];
            } else {
              widget.data.selectedEquipment = [
                'barbell',
                'plates',
                'bench',
                'rack',
                'cable_machine',
                'machines_selector',
              ];
            }
            widget.onDataChanged();
          });
        },
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: theme.colorScheme.primary),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          label,
          style: theme.textTheme.bodySmall,
        ),
      ),
    );
  }
}