import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/models/onboarding_data.dart';

class Page5Schedule extends StatefulWidget {
  final OnboardingData data;
  final VoidCallback onDataChanged;
  
  const Page5Schedule({
    super.key,
    required this.data,
    required this.onDataChanged,
  });

  @override
  State<Page5Schedule> createState() => _Page5ScheduleState();
}

class _Page5ScheduleState extends State<Page5Schedule> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppSpacing.lg),
          
          Text(
            'How many days/week will you commit?',
            style: textTheme.headlineMedium?.copyWith(
              fontSize: 26,
              fontWeight: FontWeight.w700,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xs),
          
          Text(
            'When science meets your lifestyle',
            style: textTheme.bodyLarge?.copyWith(
              color: AppColors.grayLight,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          // Days per week
          Text(
            'Workouts per week',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(7, (index) {
              final days = index + 1;
              final isSelected = widget.data.workoutsPerWeek == days;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    widget.data.workoutsPerWeek = days;
                    widget.onDataChanged();
                  });
                },
                child: Container(
                  width: 44,
                  height: 44,
                  decoration: BoxDecoration(
                    color: isSelected
                      ? theme.colorScheme.primary
                      : AppColors.surfaceDark,
                    borderRadius: BorderRadius.circular(12),
                    border: !isSelected
                      ? Border.all(color: AppColors.grayDark)
                      : null,
                  ),
                  child: Center(
                    child: Text(
                      '$days',
                      style: textTheme.titleMedium?.copyWith(
                        color: isSelected
                          ? theme.colorScheme.onPrimary
                          : theme.colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              );
            }),
          ),
          
          const SizedBox(height: AppSpacing.xs),
          Text(
            'Science says between at least 2 and optimally 4 days a week',
            style: textTheme.bodySmall?.copyWith(
              color: AppColors.grayLight,
            ),
          ),
          
          const SizedBox(height: AppSpacing.lg),
          
          // Session duration
          Text(
            'Session duration',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          
          if (!widget.data.noTimePreference) ...[
            Container(
              padding: const EdgeInsets.all(AppSpacing.sm),
              decoration: BoxDecoration(
                color: AppColors.surfaceDark,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  Text(
                    '${widget.data.sessionDuration} min',
                    style: textTheme.headlineSmall?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  Slider(
                    value: widget.data.sessionDuration?.toDouble() ?? 45,
                    min: 15,
                    max: 120,
                    divisions: 21,
                    onChanged: (value) {
                      setState(() {
                        widget.data.sessionDuration = value.round();
                        widget.onDataChanged();
                      });
                    },
                  ),
                ],
              ),
            ),
          ],
          
          CheckboxListTile(
            title: Text(
              'No preference, optimize the time for me',
              style: textTheme.bodyMedium,
            ),
            value: widget.data.noTimePreference,
            onChanged: (value) {
              setState(() {
                widget.data.noTimePreference = value ?? false;
                if (widget.data.noTimePreference) {
                  widget.data.sessionDuration = null;
                } else {
                  widget.data.sessionDuration = 45;
                }
                widget.onDataChanged();
              });
            },
            controlAffinity: ListTileControlAffinity.leading,
          ),
          
          const SizedBox(height: AppSpacing.lg),
          
          // Day preferences
          Text(
            'Preferred workout days',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          
          if (!widget.data.noDayPreference) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(7, (index) {
                final days = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
                final isSelected = widget.data.workoutDays.contains(index);
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      if (isSelected) {
                        widget.data.workoutDays.remove(index);
                      } else {
                        widget.data.workoutDays.add(index);
                      }
                      widget.onDataChanged();
                    });
                  },
                  child: Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      color: isSelected
                        ? theme.colorScheme.primary
                        : AppColors.surfaceDark,
                      shape: BoxShape.circle,
                      border: !isSelected
                        ? Border.all(color: AppColors.grayDark)
                        : null,
                    ),
                    child: Center(
                      child: Text(
                        days[index],
                        style: textTheme.titleSmall?.copyWith(
                          color: isSelected
                            ? theme.colorScheme.onPrimary
                            : theme.colorScheme.onSurface,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                );
              }),
            ),
          ],
          
          CheckboxListTile(
            title: Text(
              'No preference, optimize the days for me',
              style: textTheme.bodyMedium,
            ),
            value: widget.data.noDayPreference,
            onChanged: (value) {
              setState(() {
                widget.data.noDayPreference = value ?? false;
                widget.onDataChanged();
              });
            },
            controlAffinity: ListTileControlAffinity.leading,
          ),
          
          const SizedBox(height: AppSpacing.xl),
        ],
      ),
    );
  }
}