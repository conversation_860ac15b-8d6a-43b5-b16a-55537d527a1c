import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/models/workout_data.dart';
import 'package:openfitv4/data/sample_workouts.dart';
import 'package:openfitv4/providers/workout_provider.dart';
import 'package:openfitv4/screens/workout/workout_detail_screen.dart';

enum SortType { popularity, duration, alphabetical }

class _SortDropdown extends StatefulWidget {
  final void Function(SortType sort) onChanged;
  const _SortDropdown({required this.onChanged});

  @override
  State<_SortDropdown> createState() => _SortDropdownState();
}

class _SortDropdownState extends State<_SortDropdown> {
  SortType _current = SortType.popularity;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.xs),
      decoration: BoxDecoration(
        color: AppColors.surfaceDark,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: AppColors.grayDark),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<SortType>(
          value: _current,
          dropdownColor: AppColors.backgroundDark,
          icon: Icon(Icons.keyboard_arrow_down_rounded, color: AppColors.grayLight, size: 18),
          style: theme.textTheme.bodyMedium?.copyWith(color: AppColors.grayLight),
          onChanged: (val) {
            if (val == null) return;
            setState(() => _current = val);
            widget.onChanged(val);
          },
          items: const [
            DropdownMenuItem(
              value: SortType.popularity,
              child: Text('Popularity'),
            ),
            DropdownMenuItem(
              value: SortType.duration,
              child: Text('Duration'),
            ),
            DropdownMenuItem(
              value: SortType.alphabetical,
              child: Text('A–Z'),
            ),
          ],
        ),
      ),
    );
  }
}

class _EmptySearchState extends StatelessWidget {
  final String query;
  final VoidCallback onClear;
  const _EmptySearchState({required this.query, required this.onClear});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 88,
              height: 88,
              decoration: BoxDecoration(
                color: AppColors.surfaceDark,
                shape: BoxShape.circle,
              ),
              child: Icon(Icons.search_off_rounded, color: AppColors.grayMedium, size: 40),
            ),
            const SizedBox(height: AppSpacing.md),
            Text(
              'No results',
              style: textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700),
            ),
            const SizedBox(height: AppSpacing.xs),
            Text(
              'We couldn\'t find workouts for "$query". Try another keyword.',
              style: textTheme.bodyMedium?.copyWith(color: AppColors.grayLight),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.lg),
            ElevatedButton.icon(
              onPressed: onClear,
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm, vertical: AppSpacing.xs),
              ),
              icon: const Icon(Icons.clear),
              label: const Text('Clear search'),
            ),
          ],
        ),
      ),
    );
  }
}

class WorkoutScreen extends StatefulWidget {
  const WorkoutScreen({super.key});

  @override
  State<WorkoutScreen> createState() => _WorkoutScreenState();
}

class _WorkoutScreenState extends State<WorkoutScreen> with SingleTickerProviderStateMixin {
  // Simplified "Search Workout" screen state
  final TextEditingController _searchController = TextEditingController(text: '');
  String _query = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Load workouts when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadWorkouts();
    });
  }

  Future<void> _loadWorkouts() async {
    setState(() => _isLoading = true);
    final provider = Provider.of<WorkoutProvider>(context, listen: false);
    await provider.loadAllWorkouts();
    setState(() => _isLoading = false);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final workoutProvider = Provider.of<WorkoutProvider>(context);

    // Get workouts from provider, fallback to sample data if empty
    List<Workout> all = workoutProvider.allWorkouts;
    if (all.isEmpty && !_isLoading) {
      // Use sample workouts as fallback
      all = SampleWorkouts.allWorkouts;
    }

    // Filter workouts based on search query
    final filtered = _query.trim().isEmpty
        ? all
        : all.where((w) => ('${w.name} ${w.category} ${w.description}')
            .toLowerCase()
            .contains(_query.toLowerCase())).toList();

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with back button and title
            Padding(
              padding: const EdgeInsets.fromLTRB(AppSpacing.sm, AppSpacing.sm, AppSpacing.sm, AppSpacing.xs),
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppColors.surfaceDark,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColors.grayDark.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.arrow_back,
                        color: theme.colorScheme.onSurface,
                        size: 20,
                      ),
                      padding: EdgeInsets.zero,
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                  const SizedBox(width: AppSpacing.md),
                  Text(
                    'Search Workout',
                    style: textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.w700),
                  ),
                ],
              ),
            ),

            // Search field styled with theme
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
              child: Container(
                height: 48,
                decoration: BoxDecoration(
                  color: AppColors.surfaceDark,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppColors.grayDark),
                ),
                child: Row(
                  children: [
                    const SizedBox(width: AppSpacing.sm),
                    Expanded(
                      child: TextField(
                        controller: _searchController,
                        onChanged: (v) => setState(() => _query = v),
                        style: textTheme.bodyMedium,
                        decoration: InputDecoration(
                          hintText: 'Cardio workout',
                          hintStyle: textTheme.bodyMedium?.copyWith(color: AppColors.grayMedium),
                          border: InputBorder.none,
                        ),
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.only(right: AppSpacing.xs),
                      width: 36,
                      height: 36,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(color: theme.colorScheme.primary, width: 2),
                      ),
                      child: Icon(Icons.search, color: theme.colorScheme.primary, size: 18),
                    ),
                    const SizedBox(width: AppSpacing.xs),
                  ],
                ),
              ),
            ),

            const SizedBox(height: AppSpacing.sm),

            // Results count and sorting stub
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
              child: Row(
                children: [
                  Text(
                    '${filtered.length} Results Found.',
                    style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
                  ),
                  const Spacer(),
                  _SortDropdown(
                    onChanged: (sort) => setState(() {
                      // simple sorts without backend:
                      switch (sort) {
                        case SortType.popularity:
                          // no views yet; approximate using calories
                          filtered.sort((a, b) => b.caloriesBurn.compareTo(a.caloriesBurn));
                          break;
                        case SortType.duration:
                          filtered.sort((a, b) => b.duration.compareTo(a.duration));
                          break;
                        case SortType.alphabetical:
                          filtered.sort((a, b) => a.name.toLowerCase().compareTo(b.name.toLowerCase()));
                          break;
                      }
                    }),
                  ),
                ],
              ),
            ),

            const SizedBox(height: AppSpacing.xs),

            // List or Empty state
            Expanded(
              child: _isLoading
                  ? Center(
                      child: CircularProgressIndicator(
                        color: theme.colorScheme.primary,
                      ),
                    )
                  : filtered.isEmpty
                      ? _EmptySearchState(
                          query: _query,
                          onClear: () {
                            setState(() {
                              _query = '';
                              _searchController.clear();
                            });
                          },
                        )
                      : RefreshIndicator(
                          onRefresh: _loadWorkouts,
                          color: theme.colorScheme.primary,
                          child: ListView.builder(
                            padding: const EdgeInsets.all(AppSpacing.sm),
                            itemCount: filtered.length,
                            itemBuilder: (context, index) {
                              final w = filtered[index];
                              return _buildSearchResultCard(context, w);
                            },
                          ),
                        ),
            ),
          ],
        ),
      ),
    );
  }

  // New card design resembling the provided mock without breaking navigation
  Widget _buildSearchResultCard(BuildContext context, Workout workout) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(18),
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => WorkoutDetailScreen(workout: workout),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: AppSpacing.sm),
        height: 180,
        decoration: BoxDecoration(
          color: AppColors.surfaceDark,
          borderRadius: BorderRadius.circular(18),
          image: DecorationImage(
            image: AssetImage(workout.imageUrl),
            fit: BoxFit.cover,
            colorFilter: ColorFilter.mode(Colors.black.withValues(alpha: 0.5), BlendMode.darken),
          ),
          boxShadow: [
            BoxShadow(color: Colors.black.withValues(alpha: 0.25), blurRadius: 8, offset: const Offset(0, 4)),
          ],
        ),
        child: Stack(
          children: [
            // Category pill
            Positioned(
              left: AppSpacing.sm,
              top: AppSpacing.sm,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: AppSpacing.xs, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.backgroundDark.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  workout.category,
                  style: textTheme.bodySmall?.copyWith(color: AppColors.grayLight, fontWeight: FontWeight.w600),
                ),
              ),
            ),
            // Play button
            Positioned(
              right: AppSpacing.sm,
              bottom: AppSpacing.sm,
              child: Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(14),
                ),
                child: const Icon(Icons.play_arrow_rounded, color: Colors.white),
              ),
            ),
            // Text and stats
            Positioned.fill(
              child: Padding(
                padding: const EdgeInsets.all(AppSpacing.sm),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Spacer(),
                    Text(
                      workout.name,
                      style: textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w700,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    // Subtext line (author/trainer) not available in model; omit to avoid errors
                    const SizedBox.shrink(),
                    const SizedBox(height: AppSpacing.xs),
                    Row(
                      children: [
                        Icon(Icons.remove_red_eye_outlined, size: 16, color: Colors.white70),
                        const SizedBox(width: 4),
                        Text(
                          '—',
                          style: textTheme.bodySmall?.copyWith(color: Colors.white70),
                        ),
                        const SizedBox(width: AppSpacing.sm),
                        Icon(Icons.local_fire_department, size: 16, color: Colors.white70),
                        const SizedBox(width: 4),
                        Text(
                          '${workout.caloriesBurn}kcal',
                          style: textTheme.bodySmall?.copyWith(color: Colors.white70),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      ),
    );
  }

  // The old tab-based builders remain below (unused) to avoid breaking imports/refs.
  // They are not referenced in build() anymore, but kept for minimal invasiveness.
  Widget _buildForYouTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.sm),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Today's Workouts
          _buildSectionHeader('Today\'s Workouts', 'View All'),
          const SizedBox(height: AppSpacing.sm),
          SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: SampleWorkouts.todaysWorkouts.length,
              itemBuilder: (context, index) {
                final workout = SampleWorkouts.todaysWorkouts[index];
                return _buildTodayWorkoutCard(workout);
              },
            ),
          ),

          const SizedBox(height: AppSpacing.lg),

          // Categories
          _buildSectionHeader('Categories', ''),
          const SizedBox(height: AppSpacing.sm),
          _buildCategoriesGrid(),

          const SizedBox(height: AppSpacing.lg),

          // Recent Workouts
          _buildSectionHeader('Recent Workouts', 'View All'),
          const SizedBox(height: AppSpacing.sm),
          ...SampleWorkouts.recentWorkouts.map((workout) => 
            _buildRecentWorkoutCard(workout)
          ),
        ],
      ),
    );
  }

  Widget _buildProgramsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.sm),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppSpacing.sm),
          ...SampleWorkouts.programs.map((program) => 
            _buildProgramCard(program)
          ),
        ],
      ),
    );
  }

  Widget _buildLibraryTab() {
    // Unused legacy tab kept for compatibility; default to all workouts
    final filteredWorkouts = SampleWorkouts.allWorkouts;

    return Column(
      children: [
        // Category filter
        Container(
          height: 50,
          margin: const EdgeInsets.all(AppSpacing.sm),
          child: Row(
            children: [
              'All',
              'Strength',
              'Cardio',
              'HIIT',
              'Yoga'
            ].asMap().entries.map((entry) {
              final index = entry.key;
              final label = entry.value;
              final value = label.toLowerCase();

              return Expanded(
                child: Container(
                  margin: EdgeInsets.only(
                    right: index < 4 ? AppSpacing.xs : 0,
                  ),
                  child: _buildFilterChip(label, value),
                ),
              );
            }).toList(),
          ),
        ),
        
        // Workout list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
            itemCount: filteredWorkouts.length,
            itemBuilder: (context, index) {
              final workout = filteredWorkouts[index];
              return _buildLibraryWorkoutCard(workout);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSectionHeader(String title, String action) {
    final theme = Theme.of(context);
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w700,
          ),
        ),
        if (action.isNotEmpty)
          GestureDetector(
            onTap: () {},
            child: Text(
              action,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildTodayWorkoutCard(Workout workout) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => WorkoutDetailScreen(workout: workout),
          ),
        );
      },
      child: Container(
        width: 280,
        margin: const EdgeInsets.only(right: AppSpacing.sm),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          image: DecorationImage(
            image: AssetImage(workout.imageUrl),
            fit: BoxFit.cover,
            colorFilter: ColorFilter.mode(
              Colors.black.withValues(alpha: 0.4),
              BlendMode.darken,
            ),
          ),
        ),
        child: Container(
          padding: const EdgeInsets.all(AppSpacing.sm),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.transparent,
                Colors.black.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpacing.xs,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  workout.category,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(height: AppSpacing.xs),
              Text(
                workout.name,
                style: theme.textTheme.titleLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w700,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: Colors.white70,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${workout.duration} min',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.white70,
                    ),
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Icon(
                    Icons.local_fire_department,
                    size: 16,
                    color: Colors.white70,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${workout.caloriesBurn} cal',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoriesGrid() {
    return SizedBox(
      height: 100,
      child: Row(
        children: WorkoutCategories.all.asMap().entries.map((entry) {
          final index = entry.key;
          final category = entry.value;
          return Expanded(
            child: Container(
              margin: EdgeInsets.only(
                right: index < WorkoutCategories.all.length - 1 ? AppSpacing.sm : 0,
              ),
              child: _buildCategoryCard(category),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildCategoryCard(WorkoutCategory category) {
    final theme = Theme.of(context);

    return Container(
      height: 100,
      decoration: BoxDecoration(
        color: category.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: category.color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            category.icon,
            color: category.color,
            size: 32,
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            category.name,
            style: theme.textTheme.bodySmall?.copyWith(
              color: category.color,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentWorkoutCard(Workout workout) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.sm),
      padding: const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: AppColors.surfaceDark,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              image: DecorationImage(
                image: AssetImage(workout.imageUrl),
                fit: BoxFit.cover,
              ),
            ),
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  workout.name,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${workout.duration} min • ${workout.caloriesBurn} cal',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: AppColors.grayLight,
                  ),
                ),
              ],
            ),
          ),
          if (workout.isCompleted)
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.check,
                color: theme.colorScheme.primary,
                size: 16,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildProgramCard(WorkoutProgram program) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.sm),
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        image: DecorationImage(
          image: AssetImage(program.imageUrl),
          fit: BoxFit.cover,
          colorFilter: ColorFilter.mode(
            Colors.black.withValues(alpha: 0.4),
            BlendMode.darken,
          ),
        ),
      ),
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.sm),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withValues(alpha: 0.8),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              program.name,
              style: theme.textTheme.titleLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w700,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              program.description,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.white70,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: AppSpacing.xs),
            Row(
              children: [
                Text(
                  '${program.weeks} weeks',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.white70,
                  ),
                ),
                const SizedBox(width: AppSpacing.sm),
                Text(
                  '${program.workoutsPerWeek}x/week',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.white70,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpacing.xs,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    program.difficulty,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLibraryWorkoutCard(Workout workout) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => WorkoutDetailScreen(workout: workout),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: AppSpacing.sm),
        padding: const EdgeInsets.all(AppSpacing.sm),
        decoration: BoxDecoration(
          color: AppColors.surfaceDark,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                image: DecorationImage(
                  image: AssetImage(workout.imageUrl),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(width: AppSpacing.sm),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          workout.category,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      const Spacer(),
                      Icon(
                        workout.isFavorite ? Icons.favorite : Icons.favorite_border,
                        color: workout.isFavorite 
                          ? theme.colorScheme.primary 
                          : AppColors.grayMedium,
                        size: 20,
                      ),
                    ],
                  ),
                  const SizedBox(height: AppSpacing.xs),
                  Text(
                    workout.name,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    workout.description,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: AppColors.grayLight,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: AppSpacing.xs),
                  Row(
                    children: [
                      Icon(
                        Icons.access_time,
                        size: 14,
                        color: AppColors.grayMedium,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${workout.duration} min',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: AppColors.grayMedium,
                        ),
                      ),
                      const SizedBox(width: AppSpacing.sm),
                      Icon(
                        Icons.local_fire_department,
                        size: 14,
                        color: AppColors.grayMedium,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${workout.caloriesBurn} cal',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: AppColors.grayMedium,
                        ),
                      ),
                      const SizedBox(width: AppSpacing.sm),
                      Icon(
                        Icons.trending_up,
                        size: 14,
                        color: AppColors.grayMedium,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        workout.difficulty,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: AppColors.grayMedium,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    // kept for backward compatibility; not used in the simplified screen
    final theme = Theme.of(context);
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: AppColors.surfaceDark,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: AppColors.grayDark),
      ),
      child: Text(
        label,
        textAlign: TextAlign.center,
        style: theme.textTheme.bodyMedium?.copyWith(
          color: AppColors.grayLight,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
