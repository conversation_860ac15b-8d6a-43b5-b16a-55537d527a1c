import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/models/workout_data.dart';
import 'package:openfitv4/widgets/custom_buttons.dart';

class WorkoutCompleteScreen extends StatelessWidget {
  final Workout workout;
  final int duration;
  final List<WorkoutExercise> exercises;

  const WorkoutCompleteScreen({
    super.key,
    required this.workout,
    required this.duration,
    required this.exercises,
  });

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  int _getCompletedSets() {
    int total = 0;
    for (var exercise in exercises) {
      total += exercise.completedSets;
    }
    return total;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpacing.sm),
          child: Column(
            children: [
              const SizedBox(height: AppSpacing.xl),

              // Success icon
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: const Color(0xFF10B981).withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check_circle,
                  color: Color(0xFF10B981),
                  size: 60,
                ),
              ),

              const SizedBox(height: AppSpacing.lg),

              // Great work text
              Text(
                'Great Work!',
                style: textTheme.displaySmall?.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),

              const SizedBox(height: AppSpacing.xs),

              Text(
                'You crushed today\'s ${workout.name} workout!',
                style: textTheme.bodyLarge?.copyWith(
                  color: AppColors.grayLight,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: AppSpacing.xl),

              // Workout Summary Card
              Container(
                padding: const EdgeInsets.all(AppSpacing.lg),
                decoration: BoxDecoration(
                  color: AppColors.surfaceDark,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  children: [
                    Text(
                      'Workout Summary',
                      style: textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.lg),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatItem(
                          Icons.access_time,
                          _formatTime(duration),
                          'minutes',
                          theme,
                        ),
                        _buildStatItem(
                          Icons.local_fire_department,
                          '${workout.caloriesBurn}',
                          'kcal burned',
                          theme,
                        ),
                        _buildStatItem(
                          Icons.check_circle_outline,
                          '${_getCompletedSets()}',
                          'sets completed',
                          theme,
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppSpacing.lg),

              // Progress Update Card
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpacing.sm,
                  vertical: AppSpacing.xs,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFF10B981).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(30),
                  border: Border.all(
                    color: const Color(0xFF10B981).withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.trending_up,
                      color: Color(0xFF10B981),
                      size: 20,
                    ),
                    const SizedBox(width: AppSpacing.xs),
                    Text(
                      'Progress Update',
                      style: textTheme.bodyMedium?.copyWith(
                        color: const Color(0xFF10B981),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppSpacing.xs),

              Text(
                '2 more sets than last time! You\'re getting stronger.',
                style: textTheme.bodyMedium?.copyWith(
                  color: AppColors.grayLight,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: AppSpacing.xl),

              // Exercise Breakdown
              Container(
                padding: const EdgeInsets.all(AppSpacing.sm),
                decoration: BoxDecoration(
                  color: AppColors.surfaceDark,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(AppSpacing.xs),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Exercise Breakdown',
                            style: textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              _showExerciseBreakdown(context);
                            },
                            child: Text(
                              'View Details',
                              style: textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: AppSpacing.sm),
                    ...exercises.take(3).map((exercise) => 
                      _buildExerciseRow(exercise, theme)
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppSpacing.xl),

              // Action buttons
              CustomElevatedButton(
                onPressed: () {
                  Navigator.of(context).popUntil((route) => route.isFirst);
                },
                text: 'Done',
                borderRadius: 16,
              ),

              const SizedBox(height: AppSpacing.sm),

              CustomOutlinedButton(
                onPressed: () {
                  // TODO: Implement share functionality
                },
                text: 'Share Progress',
                isPrimary: true,
                borderRadius: 16,
              ),

              const SizedBox(height: AppSpacing.xl),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String value, String label, ThemeData theme) {
    return Column(
      children: [
        Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Icon(
            icon,
            color: theme.colorScheme.primary,
            size: 28,
          ),
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          value,
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w700,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: AppColors.grayLight,
          ),
        ),
      ],
    );
  }

  Widget _buildExerciseRow(WorkoutExercise exercise, ThemeData theme) {
    final completedSets = exercise.completedSets;
    final totalSets = exercise.totalSets;
    final isCompleted = exercise.isCompleted;

    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.xs),
      padding: const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: AppColors.backgroundDark,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          // Exercise name
          Expanded(
            child: Text(
              exercise.exercise.name,
              style: theme.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // Sets completed
          Text(
            '$completedSets/$totalSets sets',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppColors.grayLight,
            ),
          ),

          const SizedBox(width: AppSpacing.sm),

          // Completion indicator
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: isCompleted
                  ? const Color(0xFF10B981).withValues(alpha: 0.2)
                  : AppColors.grayDark.withValues(alpha: 0.3),
              shape: BoxShape.circle,
            ),
            child: Icon(
              isCompleted ? Icons.check : Icons.close,
              color: isCompleted ? const Color(0xFF10B981) : AppColors.grayMedium,
              size: 16,
            ),
          ),
        ],
      ),
    );
  }

  void _showExerciseBreakdown(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ExerciseBreakdownSheet(
        exercises: exercises,
      ),
    );
  }
}

class ExerciseBreakdownSheet extends StatelessWidget {
  final List<WorkoutExercise> exercises;

  const ExerciseBreakdownSheet({
    super.key,
    required this.exercises,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return DraggableScrollableSheet(
      initialChildSize: 0.7,
      maxChildSize: 0.9,
      minChildSize: 0.5,
      builder: (context, scrollController) {
        return Container(
          decoration: const BoxDecoration(
            color: AppColors.backgroundDark,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
                decoration: BoxDecoration(
                  color: AppColors.grayDark,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Title
              Padding(
                padding: const EdgeInsets.all(AppSpacing.sm),
                child: Text(
                  'Exercise Breakdown',
                  style: textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),

              // Exercise list
              Expanded(
                child: ListView.builder(
                  controller: scrollController,
                  padding: const EdgeInsets.all(AppSpacing.sm),
                  itemCount: exercises.length,
                  itemBuilder: (context, index) {
                    final exercise = exercises[index];
                    return _buildDetailedExerciseCard(exercise, theme);
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDetailedExerciseCard(WorkoutExercise workoutExercise, ThemeData theme) {
    final exercise = workoutExercise.exercise;
    final sets = workoutExercise.sets;

    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.sm),
      padding: const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: AppColors.surfaceDark,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Exercise header
          Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  image: DecorationImage(
                    image: AssetImage(exercise.imageUrl),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              const SizedBox(width: AppSpacing.sm),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      exercise.name,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      exercise.muscle,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: AppColors.grayLight,
                      ),
                    ),
                  ],
                ),
              ),
              // Progress badge
              if (workoutExercise.isCompleted)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpacing.xs,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFF10B981).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '+1 rep',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: const Color(0xFF10B981),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),

          const SizedBox(height: AppSpacing.sm),

          // Sets details
          ...sets.asMap().entries.map((entry) {
            final index = entry.key;
            final set = entry.value;
            return Container(
              margin: const EdgeInsets.only(bottom: 4),
              padding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.xs,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: set.isCompleted
                    ? theme.colorScheme.primary.withValues(alpha: 0.05)
                    : AppColors.backgroundDark,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  SizedBox(
                    width: 40,
                    child: Text(
                      'Set ${index + 1}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: AppColors.grayMedium,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Text(
                    '${set.reps} reps',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (set.weight != null) ...[
                    const SizedBox(width: AppSpacing.xs),
                    Text(
                      '@ ${set.weight?.toStringAsFixed(0)} lbs',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: AppColors.grayLight,
                      ),
                    ),
                  ],
                  const Spacer(),
                  if (set.isCompleted)
                    Icon(
                      Icons.check_circle,
                      color: theme.colorScheme.primary,
                      size: 16,
                    )
                  else
                    Icon(
                      Icons.circle_outlined,
                      color: AppColors.grayDark,
                      size: 16,
                    ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }
}