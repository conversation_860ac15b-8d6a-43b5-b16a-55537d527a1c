import 'package:flutter/material.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/models/workout_data.dart';
import 'package:openfitv4/screens/workout/pre_workout_overview_screen.dart';
import 'package:openfitv4/widgets/custom_buttons.dart';

class WorkoutDetailScreen extends StatefulWidget {
  final Workout workout;

  const WorkoutDetailScreen({
    super.key,
    required this.workout,
  });

  @override
  State<WorkoutDetailScreen> createState() => _WorkoutDetailScreenState();
}

class _WorkoutDetailScreenState extends State<WorkoutDetailScreen> {
  bool _isFavorite = false;

  @override
  void initState() {
    super.initState();
    _isFavorite = widget.workout.isFavorite;
  }

  void _toggleFavorite() {
    setState(() {
      _isFavorite = !_isFavorite;
    });
  }

  void _startWorkout() {
    // Navigate to new pre-workout overview screen first
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PreWorkoutOverviewScreen(workout: widget.workout),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: Stack(
        children: [
          // Hero image
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(widget.workout.imageUrl.isNotEmpty ? widget.workout.imageUrl : 'assets/images/chest_press.png'),
                  fit: BoxFit.cover,
                  colorFilter: ColorFilter.mode(Colors.black.withValues(alpha: 0.35), BlendMode.darken),
                ),
              ),
            ),
          ),
          // Top controls
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm, vertical: AppSpacing.sm),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _roundedIconButton(
                    context: context,
                    icon: Icons.arrow_back,
                    onTap: () => Navigator.pop(context),
                  ),
                  _roundedIconButton(
                    context: context,
                    icon: _isFavorite ? Icons.favorite : Icons.favorite_border,
                    color: _isFavorite ? theme.colorScheme.primary : Colors.white,
                    onTap: _toggleFavorite,
                  ),
                ],
              ),
            ),
          ),
          // Center title block
          Align(
            alignment: Alignment(0, -0.05),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.white.withValues(alpha: 0.5)),
                  ),
                  child: Text(
                    '${widget.workout.exercises.length} Total',
                    style: textTheme.labelLarge?.copyWith(color: Colors.white, fontWeight: FontWeight.w600),
                  ),
                ),
                const SizedBox(height: AppSpacing.sm),
                Text(
                  widget.workout.name,
                  textAlign: TextAlign.center,
                  style: textTheme.headlineMedium?.copyWith(color: Colors.white, fontWeight: FontWeight.w800),
                ),
                const SizedBox(height: 6),
                Text(
                  'With OpenFit Coach',
                  style: textTheme.bodyMedium?.copyWith(color: Colors.white70),
                ),
              ],
            ),
          ),
          // Bottom stats & CTAs
          Align(
            alignment: Alignment.bottomCenter,
            child: SafeArea(
              top: false,
              child: Padding(
                padding: const EdgeInsets.all(AppSpacing.sm),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Stats row
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.lg, vertical: AppSpacing.md),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.35),
                        borderRadius: BorderRadius.circular(24),
                        border: Border.all(color: Colors.white.withValues(alpha: 0.12)),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _statItem(icon: Icons.access_time, value: '${widget.workout.duration}min', label: 'Time', theme: theme),
                          _divider(),
                          _statItem(icon: Icons.local_fire_department, value: '${widget.workout.caloriesBurn}kcal', label: 'Calorie', theme: theme),
                          _divider(),
                          _statItem(icon: Icons.repeat, value: '3x4', label: 'Sets', theme: theme),
                        ],
                      ),
                    ),
                    const SizedBox(height: AppSpacing.md),
                    // CTA row
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            style: OutlinedButton.styleFrom(
                              side: BorderSide(color: Colors.white.withValues(alpha: 0.4)),
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              textStyle: textTheme.titleMedium,
                              backgroundColor: Colors.white.withValues(alpha: 0.08),
                            ),
                            onPressed: () {
                              // Navigate to details: pre-start per confirmation
                              _startWorkout();
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.info_outline, size: 20),
                                const SizedBox(width: 8),
                                Text('Details', style: textTheme.titleMedium?.copyWith(color: Colors.white, fontWeight: FontWeight.w600)),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(width: AppSpacing.sm),
                        Expanded(
                          child: CustomElevatedButton(
                            onPressed: _startWorkout,
                            text: 'Start',
                            icon: const Icon(Icons.timer_outlined, size: 22),
                            borderRadius: 16,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // New stat item used in bottom row
  Widget _statItem({required IconData icon, required String value, required String label, required ThemeData theme}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: Colors.white, size: 20),
        const SizedBox(height: 6),
        Text(value, style: theme.textTheme.titleMedium?.copyWith(color: Colors.white, fontWeight: FontWeight.w700)),
        const SizedBox(height: 2),
        Text(label, style: theme.textTheme.bodySmall?.copyWith(color: Colors.white70)),
      ],
    );
  }

  Widget _divider() => Container(width: 1, height: 36, color: Colors.white.withValues(alpha: 0.15));

  Widget _roundedIconButton({required BuildContext context, required IconData icon, Color? color, required VoidCallback onTap}) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.12)),
      ),
      child: IconButton(
        icon: Icon(icon, color: color ?? Colors.white),
        onPressed: onTap,
      ),
    );
  }

  // Removed unused exercise list UI for this simplified mock-based screen.

  Widget _buildDetailChip(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: AppColors.surfaceDark,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: AppColors.grayLight,
        ),
      ),
    );
  }
}
