import 'package:flutter/material.dart';
import 'package:openfitv4/models/workout_data.dart';
import 'package:openfitv4/screens/workout/active_workout_screen.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/widgets/custom_buttons.dart';

class PreWorkoutOverviewScreen extends StatelessWidget {
  final Workout workout;

  const PreWorkoutOverviewScreen({super.key, required this.workout});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpacing.sm),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header image with curved card below look
              ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: Stack(
                  children: [
                    AspectRatio(
                      aspectRatio: 16 / 9,
                      child: Image.asset(
                        workout.imageUrl,
                        fit: BoxFit.cover,
                      ),
                    ),
                    Positioned.fill(
                      child: DecoratedBox(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.black.withValues(alpha: 0.2),
                              Colors.black.withValues(alpha: 0.5),
                            ],
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      top: 12,
                      left: 12,
                      child: _roundedIcon(Icon(Icons.arrow_back, color: Colors.white), onTap: () {
                        Navigator.pop(context);
                      }),
                    ),
                    Positioned(
                      top: 12,
                      right: 12,
                      child: _roundedIcon(const Icon(Icons.settings, color: Colors.white)),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppSpacing.md),

              // Description
              Text(
                'Prepare to transform your chest muscles with our targeted and effective chest workout routine tailored for you.',
                style: textTheme.bodyLarge?.copyWith(color: AppColors.grayLight, height: 1.5),
              ),

              const SizedBox(height: AppSpacing.md),

              // Stats row styled like mock
              Container(
                padding: const EdgeInsets.symmetric(horizontal: AppSpacing.lg, vertical: AppSpacing.md),
                decoration: BoxDecoration(
                  color: AppColors.surfaceDark,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: AppColors.grayDark.withValues(alpha: 0.2)),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _statItem(context, Icons.access_time, '${workout.duration}min', 'Time'),
                    _divider(),
                    _statItem(context, Icons.local_fire_department, '${workout.caloriesBurn}kcal', 'Calorie'),
                    _divider(),
                    _statItem(context, Icons.repeat, '3x4', 'Sets'),
                  ],
                ),
              ),

              const SizedBox(height: AppSpacing.md),

              // Exercises preview list
              ...workout.exercises.asMap().entries.map((entry) {
                final idx = entry.key + 1;
                final we = entry.value;
                // Derive an estimated duration from sets/rest since WorkoutExercise has no explicit duration field.
                final seconds = (we.sets.fold<int>(0, (sum, s) => sum + (s.duration ?? 30)) +
                        ((we.sets.length - 1).clamp(0, 1000) * we.restTime));
                final duration = _formatDuration(seconds);
                return _exercisePreviewCard(context, idx, we.exercise.name, we.exercise.imageUrl, duration);
              }),
              const SizedBox(height: AppSpacing.xl),
            ],
          ),
        ),
      ),
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.sm),
          child: CustomElevatedButton(
            onPressed: () {
              // Go directly to ActiveWorkoutScreen instead of the older pre-start “Let's Go!” page
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => ActiveWorkoutScreen(workout: workout),
                ),
              );
            },
            text: 'Start Workout',
            icon: const Icon(Icons.timer_outlined, size: 22),
            borderRadius: 16,
          ),
        ),
      ),
    );
  }

  Widget _statItem(BuildContext context, IconData icon, String value, String label) {
    final theme = Theme.of(context);
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: Colors.white, size: 20),
        const SizedBox(height: 6),
        Text(value, style: theme.textTheme.titleMedium?.copyWith(color: Colors.white, fontWeight: FontWeight.w700)),
        const SizedBox(height: 2),
        Text(label, style: theme.textTheme.bodySmall?.copyWith(color: Colors.white70)),
      ],
    );
  }

  Widget _divider() => Container(width: 1, height: 36, color: Colors.white.withValues(alpha: 0.12));

  Widget _roundedIcon(Widget icon, {VoidCallback? onTap}) {
    final child = Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.12)),
      ),
      child: IconButton(icon: icon, onPressed: onTap),
    );
    return child;
  }

  Widget _exercisePreviewCard(BuildContext context, int index, String title, String image, String duration) {
    final theme = Theme.of(context);
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.sm),
      padding: const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: AppColors.surfaceDark,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.asset(image, width: 64, height: 64, fit: BoxFit.cover),
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.08),
                    borderRadius: BorderRadius.circular(14),
                    border: Border.all(color: Colors.white.withValues(alpha: 0.12)),
                  ),
                  child: Text('Exercise $index', style: theme.textTheme.labelLarge?.copyWith(color: Colors.white)),
                ),
                const SizedBox(height: 8),
                Text(title, style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w700)),
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(Icons.access_time, size: 16, color: AppColors.grayLight),
                    const SizedBox(width: 6),
                    Text(duration, style: theme.textTheme.bodyMedium?.copyWith(color: AppColors.grayLight)),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(width: AppSpacing.sm),
          Container(
            width: 44,
            height: 44,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.08),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.white.withValues(alpha: 0.12)),
            ),
            child: const Icon(Icons.play_arrow, color: Colors.white),
          ),
        ],
      ),
    );
  }

  String _formatDuration(int? seconds) {
    if (seconds == null || seconds <= 0) return '05:00';
    final m = (seconds ~/ 60).toString().padLeft(2, '0');
    final s = (seconds % 60).toString().padLeft(2, '0');
    return '$m:$s';
  }
}
