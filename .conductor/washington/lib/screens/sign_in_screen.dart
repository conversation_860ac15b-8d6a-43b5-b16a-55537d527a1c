
import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:openfitv4/theme/app_theme.dart';
import 'package:openfitv4/providers/auth_provider.dart';

class SignInScreen extends StatefulWidget {
  const SignInScreen({super.key});

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();
  bool _emailFocused = false;
  bool _passwordFocused = false;

  @override
  void initState() {
    super.initState();
    _emailFocusNode.addListener(() {
      setState(() {
        _emailFocused = _emailFocusNode.hasFocus;
      });
    });
    _passwordFocusNode.addListener(() {
      setState(() {
        _passwordFocused = _passwordFocusNode.hasFocus;
      });
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  Future<void> _handleSignIn() async {
    print('🔥 SIGN IN BUTTON TAPPED - START');
    developer.log('🔐 SignInScreen._handleSignIn() called', name: 'SignInScreen');
    
    if (!_formKey.currentState!.validate()) {
      print('❌ FORM VALIDATION FAILED');
      developer.log('  ❌ Form validation failed', name: 'SignInScreen');
      return;
    }

    print('✅ FORM VALIDATION PASSED');
    print('📧 Email: ${_emailController.text.trim()}');
    print('🔑 Password length: ${_passwordController.text.length}');
    developer.log('  ✅ Form validation passed', name: 'SignInScreen');
    developer.log('  📧 Email: ${_emailController.text.trim().isNotEmpty ? 'provided' : 'empty'}', name: 'SignInScreen');
    developer.log('  🔑 Password: ${_passwordController.text.isNotEmpty ? 'provided' : 'empty'}', name: 'SignInScreen');
    
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    print('📱 AuthProvider retrieved: ${authProvider.runtimeType}');
    developer.log('  📱 AuthProvider retrieved: ${authProvider != null ? 'success' : 'null'}', name: 'SignInScreen');
    developer.log('  ⏳ Current loading state: ${authProvider.isLoading}', name: 'SignInScreen');
    
    print('📞 CALLING AuthProvider.signIn()...');
    developer.log('  📞 Calling AuthProvider.signIn()...', name: 'SignInScreen');
    final success = await authProvider.signIn(
      email: _emailController.text.trim(),
      password: _passwordController.text,
    );
    print('✅ AuthProvider.signIn() returned: $success');
    print('📋 AuthProvider error: ${authProvider.error}');
    print('👤 AuthProvider user: ${authProvider.user}');
    developer.log('  ✅ AuthProvider.signIn() returned: $success', name: 'SignInScreen');
    developer.log('  📋 AuthProvider error: ${authProvider.error}', name: 'SignInScreen');
    developer.log('  👤 AuthProvider user: ${authProvider.user != null ? 'present' : 'null'}', name: 'SignInScreen');

    if (success) {
      developer.log('  🎉 Sign in successful - performing explicit navigation', name: 'SignInScreen');
      // Explicitly navigate based on onboarding status to avoid staying on /signin
      if (!mounted) return;
      final nextRoute = authProvider.hasCompletedOnboarding ? '/home' : '/onboarding';
      developer.log('  🧭 Next route: $nextRoute', name: 'SignInScreen');
      Navigator.of(context).pushNamedAndRemoveUntil(nextRoute, (route) => false);
    } else if (mounted) {
      developer.log('  ❌ Sign in failed, showing error message', name: 'SignInScreen');
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(authProvider.error ?? 'Sign in failed'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    } else {
      developer.log('  ⚠️ Sign in failed but widget unmounted - no error display', name: 'SignInScreen');
    }
    
    developer.log('🔐 SignInScreen._handleSignIn() completed', name: 'SignInScreen');
    print('🔥 SIGN IN BUTTON TAPPED - END');
  }

  Future<void> _handleForgotPassword() async {
    if (_emailController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter your email address first'),
        ),
      );
      return;
    }

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    
    final success = await authProvider.resetPassword(_emailController.text.trim());

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success
                ? 'Password reset email sent. Check your inbox.'
                : authProvider.error ?? 'Failed to send reset email',
          ),
          backgroundColor: success
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your email';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your password';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: Stack(
        children: [
          // Background image
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: 256,
            child: Stack(
              children: [
                // Chest press image
                Container(
                  decoration: const BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage('assets/images/chest_press.png'),
                      fit: BoxFit.cover,
                      alignment: Alignment.center,
                    ),
                  ),
                ),
                // Gradient overlay
                Container(
                  decoration: const BoxDecoration(
                    gradient: AppColors.overlayGradient,
                  ),
                ),
              ],
            ),
          ),
          
          // Content
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
              child: Form(
                key: _formKey,
                child: Column(
                children: [
                  const SizedBox(height: 96),
                  
                  // Logo and title section
                  Column(
                    children: [
                      // Logo container
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary,
                          borderRadius: BorderRadius.circular(AppBorderRadius.md),
                        ),
                        child: Icon(
                          Icons.add,
                          color: theme.colorScheme.onPrimary,
                          size: 32,
                        ),
                      ),
                      
                      const SizedBox(height: AppSpacing.sm),
                      
                      // Title
                      Text(
                        'Sign In To OpenFit',
                        style: textTheme.headlineMedium?.copyWith(
                          fontSize: 30,
                          height: 1.27,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      
                      const SizedBox(height: AppSpacing.xs),
                      
                      // Subtitle
                      Text(
                        'Let\'s personalize your fitness with AI',
                        style: textTheme.bodyLarge?.copyWith(
                          color: AppColors.grayLight,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppSpacing.xxl),
                  
                  // Input fields
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Email field
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Email Address',
                            style: textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          const SizedBox(height: AppSpacing.xs),
                          TextFormField(
                            controller: _emailController,
                            focusNode: _emailFocusNode,
                            keyboardType: TextInputType.emailAddress,
                            style: textTheme.bodyLarge,
                            validator: _validateEmail,
                            decoration: InputDecoration(
                              hintText: 'Enter your email',
                              prefixIcon: Icon(
                                Icons.email_outlined,
                                color: theme.colorScheme.onSurface,
                              ),
                              filled: true,
                              fillColor: AppColors.surfaceDark,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(19),
                                borderSide: BorderSide(
                                  color: _emailFocused 
                                    ? theme.colorScheme.primary 
                                    : Colors.transparent,
                                  width: 2,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(19),
                                borderSide: BorderSide.none,
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(19),
                                borderSide: BorderSide(
                                  color: theme.colorScheme.primary,
                                  width: 2,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: AppSpacing.md),
                      
                      // Password field
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Password',
                            style: textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          const SizedBox(height: AppSpacing.xs),
                          TextFormField(
                            controller: _passwordController,
                            focusNode: _passwordFocusNode,
                            obscureText: _obscurePassword,
                            style: textTheme.bodyLarge,
                            validator: _validatePassword,
                            decoration: InputDecoration(
                              hintText: 'Enter your password',
                              prefixIcon: Icon(
                                Icons.lock_outline,
                                color: theme.colorScheme.onSurface,
                              ),
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscurePassword 
                                    ? Icons.visibility_outlined 
                                    : Icons.visibility_off_outlined,
                                  color: theme.colorScheme.onSurface,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscurePassword = !_obscurePassword;
                                  });
                                },
                              ),
                              filled: true,
                              fillColor: AppColors.surfaceDark,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(19),
                                borderSide: BorderSide(
                                  color: _passwordFocused 
                                    ? theme.colorScheme.primary 
                                    : Colors.transparent,
                                  width: 2,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(19),
                                borderSide: BorderSide.none,
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(19),
                                borderSide: BorderSide(
                                  color: theme.colorScheme.primary,
                                  width: 2,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: AppSpacing.md),
                      
                      // Sign In button
                      SizedBox(
                        width: double.infinity,
                        height: 56,
                        child: ElevatedButton(
                          onPressed: authProvider.isLoading ? null : _handleSignIn,
                          style: theme.elevatedButtonTheme.style?.copyWith(
                            shape: WidgetStateProperty.all(
                              RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(19),
                              ),
                            ),
                          ),
                          child: authProvider.isLoading
                              ? SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      theme.colorScheme.onPrimary,
                                    ),
                                  ),
                                )
                              : Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      'Sign In',
                                      style: textTheme.titleMedium,
                                    ),
                                    const SizedBox(width: AppSpacing.sm),
                                    const Icon(
                                      Icons.arrow_forward,
                                      size: 20,
                                    ),
                                  ],
                                ),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppSpacing.xl),
                  
                  // Social login section
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildSocialButton(
                        context,
                        icon: Icons.camera_alt, // Instagram placeholder
                        onTap: () {},
                      ),
                      const SizedBox(width: AppSpacing.xs),
                      _buildSocialButton(
                        context,
                        icon: Icons.facebook, // Facebook
                        onTap: () {},
                      ),
                      const SizedBox(width: AppSpacing.xs),
                      _buildSocialButton(
                        context,
                        icon: Icons.business, // LinkedIn placeholder
                        onTap: () {},
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppSpacing.xxl),
                  
                  // Sign up and forgot password links
                  Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Don\'t have an account? ',
                            style: textTheme.bodyMedium?.copyWith(
                              color: AppColors.grayLight,
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              Navigator.pushNamed(context, '/signup');
                            },
                            child: Text(
                              'Sign Up.',
                              style: textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.primary,
                                decoration: TextDecoration.underline,
                                decorationColor: theme.colorScheme.primary,
                              ),
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: AppSpacing.sm),
                      
                      GestureDetector(
                        onTap: authProvider.isLoading ? null : _handleForgotPassword,
                        child: Text(
                          'Forgot Password',
                          style: textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.primary,
                            decoration: TextDecoration.underline,
                            decorationColor: theme.colorScheme.primary,
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppSpacing.lg),
                ]),
              ),
            ),
          ),
          
          // Home indicator (iOS)
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: SafeArea(
              top: false,
              child: Center(
                child: Container(
                  margin: const EdgeInsets.only(bottom: AppSpacing.xs),
                  width: 134,
                  height: 5,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(2.5),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
        );
      },
    );
  }
  
  Widget _buildSocialButton(
    BuildContext context, {
    required IconData icon,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 56,
        height: 56,
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(19),
          border: Border.all(
            color: AppColors.grayMedium,
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          color: theme.colorScheme.onSurface,
          size: 24,
        ),
      ),
    );
  }
}
