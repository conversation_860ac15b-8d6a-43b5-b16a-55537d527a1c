import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:openfitv4/models/theme_config.dart';
import 'package:openfitv4/theme/app_theme.dart';

class ThemeProvider extends ChangeNotifier {
  static const String _themeKey = 'selected_theme';
  
  ThemeOption _currentTheme = ThemeOption.orange;
  late SharedPreferences _prefs;
  bool _isInitialized = false;

  ThemeOption get currentTheme => _currentTheme;
  bool get isInitialized => _isInitialized;

  ThemeProvider() {
    _loadTheme();
  }

  Future<void> _loadTheme() async {
    _prefs = await SharedPreferences.getInstance();
    final savedTheme = _prefs.getString(_themeKey);
    
    if (savedTheme != null) {
      try {
        _currentTheme = ThemeOption.values.firstWhere(
          (theme) => theme.name == savedTheme,
          orElse: () => ThemeOption.orange,
        );
      } catch (e) {
        _currentTheme = ThemeOption.orange;
      }
    }
    
    _isInitialized = true;
    notifyListeners();
  }

  Future<void> setTheme(ThemeOption theme) async {
    if (_currentTheme == theme) return;
    
    _currentTheme = theme;
    await _prefs.setString(_themeKey, theme.name);
    notifyListeners();
  }

  ThemeData getTheme(BuildContext context) {
    return AppTheme.darkTheme.copyWith(
      colorScheme: AppTheme.darkTheme.colorScheme.copyWith(
        primary: _currentTheme.color,
        secondary: _currentTheme.color.withValues(alpha: 0.8),
        tertiary: _currentTheme.color.withValues(alpha: 0.6),
        primaryContainer: _currentTheme.color.withValues(alpha: 0.12),
        secondaryContainer: _currentTheme.color.withValues(alpha: 0.08),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: _currentTheme.color,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
          ),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: _currentTheme.color,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderRadius.sm),
          ),
          textStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: _currentTheme.color,
          side: BorderSide(color: _currentTheme.color, width: 1.5),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
          ),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: _currentTheme.color,
        foregroundColor: Colors.white,
      ),
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return _currentTheme.color;
          }
          return AppColors.grayMedium;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return _currentTheme.color.withValues(alpha: 0.5);
          }
          return AppColors.grayDark;
        }),
      ),
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return _currentTheme.color;
          }
          return Colors.transparent;
        }),
        checkColor: WidgetStateProperty.all(Colors.white),
      ),
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return _currentTheme.color;
          }
          return AppColors.grayMedium;
        }),
      ),
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: _currentTheme.color,
        linearTrackColor: _currentTheme.color.withValues(alpha: 0.2),
        circularTrackColor: _currentTheme.color.withValues(alpha: 0.2),
      ),
      sliderTheme: SliderThemeData(
        activeTrackColor: _currentTheme.color,
        inactiveTrackColor: _currentTheme.color.withValues(alpha: 0.2),
        thumbColor: _currentTheme.color,
        overlayColor: _currentTheme.color.withValues(alpha: 0.12),
      ),
    );
  }
}