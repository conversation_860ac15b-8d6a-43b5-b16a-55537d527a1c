import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/workout_data.dart';

class DatabaseService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Get current user ID
  String? get _userId => _supabase.auth.currentUser?.id;

  // EXERCISES
  
  // Get all exercises
  Future<List<Exercise>> getExercises({
    String? muscleGroup,
    String? equipment,
    String? category,
    String? searchQuery,
  }) async {
    try {
      var query = _supabase.from('exercises').select();

      if (muscleGroup != null) {
        query = query.eq('primary_muscle', muscleGroup);
      }
      if (equipment != null) {
        query = query.eq('equipment', equipment);
      }
      if (category != null) {
        query = query.eq('category', category);
      }
      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.ilike('name', '%$searchQuery%');
      }

      final response = await query.order('name');
      
      return (response as List)
          .map((data) => ExerciseSupabase.fromSupabase(data))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch exercises: $e');
    }
  }

  // Get exercise by ID
  Future<Exercise?> getExercise(String exerciseId) async {
    try {
      final response = await _supabase
          .from('exercises')
          .select()
          .eq('id', exerciseId)
          .single();

      return ExerciseSupabase.fromSupabase(response);
    } catch (e) {
      return null;
    }
  }

  // WORKOUTS
  
  // Get user's workouts
  Future<List<Map<String, dynamic>>> getUserWorkouts() async {
    try {
      if (_userId == null) throw Exception('User not authenticated');

      final response = await _supabase
          .from('workouts')
          .select('''
            *,
            workout_exercises!inner(
              id,
              exercise_id,
              sets,
              reps,
              weight,
              order_index,
              rest_interval,
              name,
              exercises!inner(
                id,
                name,
                primary_muscle,
                equipment,
                instructions,
                video_url
              )
            )
          ''')
          .eq('user_id', _userId!)
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('Failed to fetch workouts: $e');
    }
  }

  // Create a new workout
  Future<String> createWorkout({
    required String name,
    String? notes,
    String? aiDescription,
  }) async {
    try {
      if (_userId == null) throw Exception('User not authenticated');

      final response = await _supabase
          .from('workouts')
          .insert({
            'user_id': _userId,
            'name': name,
            'notes': notes,
            'ai_description': aiDescription,
            'is_active': true,
            'is_completed': false,
          })
          .select('id')
          .single();

      return response['id'] as String;
    } catch (e) {
      throw Exception('Failed to create workout: $e');
    }
  }

  // Add exercise to workout
  Future<void> addExerciseToWorkout({
    required String workoutId,
    required String exerciseId,
    required String exerciseName,
    required int sets,
    required List<int> reps,
    List<double>? weights,
    int? restInterval,
    int? orderIndex,
  }) async {
    try {
      await _supabase.from('workout_exercises').insert({
        'workout_id': workoutId,
        'exercise_id': exerciseId,
        'name': exerciseName,
        'sets': sets,
        'reps': reps,
        'weight': weights,
        'rest_interval': restInterval ?? 60,
        'order_index': orderIndex ?? 0,
      });
    } catch (e) {
      throw Exception('Failed to add exercise to workout: $e');
    }
  }

  // Start workout
  Future<void> startWorkout(String workoutId) async {
    try {
      await _supabase
          .from('workouts')
          .update({
            'start_time': DateTime.now().toIso8601String(),
            'is_active': true,
          })
          .eq('id', workoutId);
    } catch (e) {
      throw Exception('Failed to start workout: $e');
    }
  }

  // Complete workout
  Future<void> completeWorkout({
    required String workoutId,
    int? rating,
    String? notes,
  }) async {
    try {
      final now = DateTime.now();
      
      await _supabase
          .from('workouts')
          .update({
            'end_time': now.toIso8601String(),
            'is_active': false,
            'is_completed': true,
            'rating': rating,
            'notes': notes,
          })
          .eq('id', workoutId);

      // Also create a completed workout record
      await _supabase.from('completed_workouts').insert({
        'workout_id': workoutId,
        'user_id': _userId,
        'date_completed': now.toIso8601String(),
        'rating': rating,
        'user_feedback_completed_workout': notes,
      });
    } catch (e) {
      throw Exception('Failed to complete workout: $e');
    }
  }

  // Log completed set
  Future<void> logCompletedSet({
    required String workoutId,
    required String exerciseId,
    required int setNumber,
    required int repsCompleted,
    double? weight,
    String? difficulty,
  }) async {
    try {
      await _supabase.from('completed_sets').insert({
        'workout_id': workoutId,
        'workout_exercise_id': exerciseId, // This should be workout_exercise_id
        'performed_set_order': setNumber,
        'performed_reps': repsCompleted,
        'performed_weight': weight?.round(),
        'set_feedback_difficulty': difficulty,
      });
    } catch (e) {
      throw Exception('Failed to log completed set: $e');
    }
  }

  // Get workout history
  Future<List<Map<String, dynamic>>> getWorkoutHistory({
    int? limit,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      if (_userId == null) throw Exception('User not authenticated');

      var query = _supabase
          .from('completed_workouts')
          .select('''
            *,
            workouts!inner(
              id,
              name,
              duration,
              ai_description
            )
          ''')
          .eq('user_id', _userId!)
          .order('date_completed', ascending: false);

      if (limit != null) {
        query = query.limit(limit);
      }

      // TODO: Add date filtering when Supabase API supports it
      // if (startDate != null) {
      //   query = query.gte('date_completed', startDate.toIso8601String());
      // }
      // if (endDate != null) {
      //   query = query.lte('date_completed', endDate.toIso8601String());
      // }

      final response = await query;
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('Failed to fetch workout history: $e');
    }
  }

  // Get exercise categories
  Future<List<String>> getExerciseCategories() async {
    try {
      final response = await _supabase
          .from('exercises')
          .select('category')
          .not('category', 'is', null);

      final categories = (response as List)
          .map((item) => item['category'] as String?)
          .where((category) => category != null && category.isNotEmpty)
          .cast<String>()
          .toSet()
          .toList();

      categories.sort();
      return categories;
    } catch (e) {
      return [];
    }
  }

  // Get muscle groups
  Future<List<String>> getMuscleGroups() async {
    try {
      final response = await _supabase
          .from('exercises')
          .select('primary_muscle')
          .not('primary_muscle', 'is', null);

      final muscles = (response as List)
          .map((item) => item['primary_muscle'] as String?)
          .where((muscle) => muscle != null && muscle.isNotEmpty)
          .cast<String>()
          .toSet()
          .toList();

      muscles.sort();
      return muscles;
    } catch (e) {
      return [];
    }
  }

  // Get equipment types
  Future<List<String>> getEquipmentTypes() async {
    try {
      final response = await _supabase
          .from('exercises')
          .select('equipment')
          .not('equipment', 'is', null);

      final equipment = (response as List)
          .map((item) => item['equipment'] as String?)
          .where((eq) => eq != null && eq.isNotEmpty)
          .cast<String>()
          .toSet()
          .toList();

      equipment.sort();
      return equipment;
    } catch (e) {
      return [];
    }
  }
}

// Extension to add Supabase mapping to Exercise model
extension ExerciseSupabase on Exercise {
  static Exercise fromSupabase(Map<String, dynamic> data) {
    return Exercise(
      id: data['id'].toString(),
      name: data['name'] as String,
      muscle: data['primary_muscle'] as String? ?? '',
      equipment: data['equipment'] as String? ?? '',
      difficulty: 'intermediate', // Default as not in DB
      instructions: data['instructions'] as String? ?? '',
      imageUrl: '', // Not in current DB schema
      videoUrl: data['video_url'] as String?,
      tips: [], // Not in current DB schema
    );
  }
}