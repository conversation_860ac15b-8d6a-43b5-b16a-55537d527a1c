import 'dart:developer' as developer;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:openfitv4/models/workout_data.dart';

/// WorkoutService fetches and mutates workout data in Supabase and maps it to
/// the existing Workout / WorkoutExercise / WorkoutSet / Exercise models.
///
/// Confirmed schema:
/// - workouts(id, user_id, name, category, duration, calories_burn, created_at)
/// - workout_exercises(id, workout_id, exercise_id, order, rest_time)
/// - workout_sets(id, workout_exercise_id, order, reps, weight)
/// - exercises(id, name, description, video_url, primary_muscle, equipment, secondary_muscle, instructions, category, vertical_video, created_at)
class WorkoutService {
  WorkoutService(this._client);

  final SupabaseClient _client;

  // ========================= READ =========================

  Future<List<Workout>> getUserWorkouts(String userId) async {
    developer.log('📥 getUserWorkouts(userId=$userId)', name: 'WorkoutService');

    try {
      final workoutsRes = await _client
          .from('workouts')
          .select('id, user_id, name, category, duration, calories_burn, created_at')
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      if (workoutsRes is! List) return [];

      final workouts = <Workout>[];
      for (final w in workoutsRes.cast<Map<String, dynamic>>()) {
        final workoutId = w['id'] as String;

        final mappedExercises = await _fetchExercisesForWorkout(workoutId);

        workouts.add(
          Workout(
            id: workoutId,
            name: (w['name'] as String?) ?? 'Workout',
            description: 'Personalized session',
            imageUrl: mappedExercises.isNotEmpty ? mappedExercises.first.exercise.imageUrl : 'assets/images/chest_press.png',
            category: (w['category'] as String?) ?? 'General',
            difficulty: 'Intermediate',
            duration: (w['duration'] as num?)?.toInt() ?? _estimateDuration(mappedExercises),
            caloriesBurn: (w['calories_burn'] as num?)?.toInt() ?? _estimateCalories(mappedExercises),
            equipment: _collectEquipment(mappedExercises),
            muscleGroups: _collectMuscles(mappedExercises),
            exercises: mappedExercises,
          ),
        );
      }

      return workouts;
    } catch (e, st) {
      developer.log('❌ getUserWorkouts error: $e', name: 'WorkoutService');
      developer.log('📍 $st', name: 'WorkoutService');
      return [];
    }
  }

  Future<Workout?> getWorkoutById(String workoutId) async {
    developer.log('📥 getWorkoutById($workoutId)', name: 'WorkoutService');

    try {
      final wRows = await _client
          .from('workouts')
          .select('id, user_id, name, category, duration, calories_burn, created_at')
          .eq('id', workoutId)
          .limit(1);

      if (wRows is! List || wRows.isEmpty) return null;
      final w = wRows.first as Map<String, dynamic>;

      final mappedExercises = await _fetchExercisesForWorkout(workoutId);

      return Workout(
        id: w['id'] as String,
        name: (w['name'] as String?) ?? 'Workout',
        description: 'Personalized session',
        imageUrl: mappedExercises.isNotEmpty ? mappedExercises.first.exercise.imageUrl : 'assets/images/chest_press.png',
        category: (w['category'] as String?) ?? 'General',
        difficulty: 'Intermediate',
        duration: (w['duration'] as num?)?.toInt() ?? _estimateDuration(mappedExercises),
        caloriesBurn: (w['calories_burn'] as num?)?.toInt() ?? _estimateCalories(mappedExercises),
        equipment: _collectEquipment(mappedExercises),
        muscleGroups: _collectMuscles(mappedExercises),
        exercises: mappedExercises,
      );
    } catch (e, st) {
      developer.log('❌ getWorkoutById error: $e', name: 'WorkoutService');
      developer.log('📍 $st', name: 'WorkoutService');
      return null;
    }
  }

  Future<List<WorkoutExercise>> _fetchExercisesForWorkout(String workoutId) async {
    final exRows = await _client
        .from('workout_exercises')
        .select('id, workout_id, exercise_id, order, rest_time, exercises ( id, name, description, primary_muscle, equipment, instructions )')
        .eq('workout_id', workoutId)
        .order('order', ascending: true);

    final mappedExercises = <WorkoutExercise>[];

    if (exRows is List) {
      for (final row in exRows.cast<Map<String, dynamic>>()) {
        final exerciseMeta = row['exercises'] as Map<String, dynamic>?;

        final exercise = Exercise(
          id: (exerciseMeta?['id'] ?? row['exercise_id']).toString(),
          name: (exerciseMeta?['name'] as String?) ?? 'Exercise',
          muscle: (exerciseMeta?['primary_muscle'] as String?)?.toLowerCase() ?? 'full_body',
          equipment: (exerciseMeta?['equipment'] as String?)?.toLowerCase() ?? 'bodyweight',
          difficulty: 'beginner',
          instructions: (exerciseMeta?['instructions'] as String?) ??
              (exerciseMeta?['description'] as String?) ??
              'Follow proper form. Breathe and control tempo.',
          imageUrl: 'assets/images/chest_press.png', // fallback asset, no image column in DB
        );

        final weId = row['id'];
        final setsRows = await _client
            .from('workout_sets')
            .select('id, workout_exercise_id, order, reps, weight')
            .eq('workout_exercise_id', weId)
            .order('order', ascending: true);

        final sets = <WorkoutSet>[];
        if (setsRows is List) {
          for (final s in setsRows.cast<Map<String, dynamic>>()) {
            sets.add(
              WorkoutSet(
                setNumber: (s['order'] as num?)?.toInt() ?? (sets.length + 1),
                reps: (s['reps'] as num?)?.toInt() ?? 0,
                weight: (s['weight'] as num?)?.toDouble(),
              ),
            );
          }
        }

        mappedExercises.add(
          WorkoutExercise(
            exercise: exercise,
            sets: sets,
            restTime: (row['rest_time'] as num?)?.toInt() ?? 0,
            sourceId: (row['id'] as String?),
          ),
        );
      }
    }

    return mappedExercises;
  }

  // ========================= WRITE (Reorder / Delete / Replace) =========================

  /// Reorder exercises within a workout by updating the "order" column.
  /// orderedWorkoutExerciseIds is a list of workout_exercises.id in the desired 0-based order.
  Future<bool> reorderWorkoutExercises({
    required String workoutId,
    required List<String> orderedWorkoutExerciseIds,
  }) async {
    developer.log('🔧 reorderWorkoutExercises($workoutId, ${orderedWorkoutExerciseIds.length} items)', name: 'WorkoutService');
    try {
      for (int i = 0; i < orderedWorkoutExerciseIds.length; i++) {
        final weId = orderedWorkoutExerciseIds[i];
        await _client
            .from('workout_exercises')
            .update({'order': i})
            .eq('id', weId)
            .eq('workout_id', workoutId);
      }
      return true;
    } catch (e, st) {
      developer.log('❌ reorderWorkoutExercises error: $e', name: 'WorkoutService');
      developer.log('📍 $st', name: 'WorkoutService');
      return false;
    }
  }

  /// Delete an exercise from a workout (assumes ON DELETE CASCADE for workout_sets).
  Future<bool> deleteWorkoutExercise({
    required String workoutExerciseId,
  }) async {
    developer.log('🗑️ deleteWorkoutExercise($workoutExerciseId)', name: 'WorkoutService');
    try {
      await _client.from('workout_exercises').delete().eq('id', workoutExerciseId);
      return true;
    } catch (e, st) {
      developer.log('❌ deleteWorkoutExercise error: $e', name: 'WorkoutService');
      developer.log('📍 $st', name: 'WorkoutService');
      return false;
    }
  }

  /// Replace the exercise_id for a workout_exercises row. Optionally regenerate sets.
  Future<bool> replaceWorkoutExercise({
    required String workoutExerciseId,
    required String newExerciseId,
    bool regenerateSets = false,
  }) async {
    developer.log('♻️ replaceWorkoutExercise(we=$workoutExerciseId -> ex=$newExerciseId, regen=$regenerateSets)', name: 'WorkoutService');
    try {
      await _client
          .from('workout_exercises')
          .update({'exercise_id': newExerciseId})
          .eq('id', workoutExerciseId);

      if (regenerateSets) {
        final currentSets = await _client
            .from('workout_sets')
            .select('id, order')
            .eq('workout_exercise_id', workoutExerciseId)
            .order('order');

        if (currentSets is List) {
          await _client.from('workout_sets').delete().eq('workout_exercise_id', workoutExerciseId);
          for (final s in currentSets.cast<Map<String, dynamic>>()) {
            final ord = (s['order'] as num?)?.toInt() ?? 0;
            await _client.from('workout_sets').insert({
              'workout_exercise_id': workoutExerciseId,
              'order': ord,
              'reps': 10,
              'weight': null,
            });
          }
        }
      }

      return true;
    } catch (e, st) {
      developer.log('❌ replaceWorkoutExercise error: $e', name: 'WorkoutService');
      developer.log('📍 $st', name: 'WorkoutService');
      return false;
    }
  }

  /// Suggest exercises by primary muscle, with optional search query.
  Future<List<Map<String, dynamic>>> suggestExercisesByMuscle({
    required String primaryMuscle,
    String? query,
    int limit = 25,
  }) async {
    try {
      final base = _client
          .from('exercises')
          .select('id, name, primary_muscle, equipment, instructions, description')
          .eq('primary_muscle', primaryMuscle);

      final rows = await (query != null && query.trim().isNotEmpty
              ? (base as PostgrestFilterBuilder).filter('name', 'ilike', '%${query.trim()}%')
              : base)
          .limit(limit);
      if (rows is List) {
        return rows.cast<Map<String, dynamic>>();
      }
      return [];
    } catch (e, st) {
      developer.log('❌ suggestExercisesByMuscle error: $e', name: 'WorkoutService');
      developer.log('📍 $st', name: 'WorkoutService');
      return [];
    }
  }

  /// Free text search across all exercises.
  Future<List<Map<String, dynamic>>> searchExercises({
    String? query,
    int limit = 50,
  }) async {
    try {
      final base = _client
          .from('exercises')
          .select('id, name, primary_muscle, equipment, instructions, description');

      final rows = await (query != null && query.trim().isNotEmpty
              ? (base as PostgrestFilterBuilder).filter('name', 'ilike', '%${query.trim()}%')
              : base)
          .limit(limit);
      if (rows is List) {
        return rows.cast<Map<String, dynamic>>();
      }
      return [];
    } catch (e, st) {
      developer.log('❌ searchExercises error: $e', name: 'WorkoutService');
      developer.log('📍 $st', name: 'WorkoutService');
      return [];
    }
  }

  // ========================= HELPERS =========================

  List<String> _collectEquipment(List<WorkoutExercise> exs) {
    final set = <String>{};
    for (final we in exs) {
      if (we.exercise.equipment.isNotEmpty) set.add(we.exercise.equipment);
    }
    return set.toList();
  }

  List<String> _collectMuscles(List<WorkoutExercise> exs) {
    final set = <String>{};
    for (final we in exs) {
      if (we.exercise.muscle.isNotEmpty) set.add(we.exercise.muscle);
    }
    return set.toList();
  }

  int _estimateDuration(List<WorkoutExercise> exs) {
    int seconds = 0;
    for (final we in exs) {
      final sets = we.sets.length;
      seconds += sets * (45 + (we.restTime));
    }
    return (seconds / 60).round().clamp(10, 120);
  }

  int _estimateCalories(List<WorkoutExercise> exs) {
    int base = exs.fold<int>(0, (acc, we) => acc + we.sets.length * 6);
    return base.clamp(50, 1200);
  }
}
