<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="fastlane.lanes">
    
    
    
      
      <testcase classname="fastlane.lanes" name="0: default_platform" time="0.000184">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="1: app_store_connect_api_key" time="0.001655">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="2: latest_testflight_build_number" time="0.765779">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="3: cd /Users/<USER>/Downloads/project/Itrations/openfitv4 &amp;&amp; flutter pub get" time="1.875645">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="4: cd /Users/<USER>/Downloads/project/Itrations/openfitv4 &amp;&amp; flutter build ipa --release --build-number 202508101955 --export-options-plist=/Users/<USER>/Downloads/project/Itrations/openfitv4/ExportOptionsAppStore.plist" time="90.037411">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="5: pilot" time="233.197313">
        
      </testcase>
    
  </testsuite>
</testsuites>
