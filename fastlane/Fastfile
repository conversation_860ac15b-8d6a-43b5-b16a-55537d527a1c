default_platform(:ios)

platform :ios do
  desc "Build IPA with the next build number automatically"
  lane :build_ipa do
    require 'yaml'

    app_identifier = "com.abenezernuro.agenticfit"
    root_dir = File.expand_path('..', __dir__)
    fastlane_dir = __dir__

    # Read version/build from pubspec.yaml (e.g., 1.0.0+3)
    pubspec = YAML.load_file(File.join(root_dir, 'pubspec.yaml'))
    raw_version = pubspec['version'].to_s
    app_version, current_build = raw_version.split('+')
    current_build_i = (current_build || '0').to_i
    local_next_build = current_build_i + 1

    latest_testflight = nil
    begin
      # Optional: Use App Store Connect API key if provided to fetch latest TestFlight build
      if ENV['ASC_KEY_ID'] && ENV['ASC_ISSUER_ID'] && File.exist?(File.join(fastlane_dir, "AuthKey_#{ENV['ASC_KEY_ID']}.p8"))
        app_store_connect_api_key(
          key_id: ENV['ASC_KEY_ID'],
          issuer_id: ENV['ASC_ISSUER_ID'],
          key_filepath: File.join(fastlane_dir, "AuthKey_#{ENV['ASC_KEY_ID']}.p8"),
          duration: 1200,
          in_house: false
        )
        latest_testflight = latest_testflight_build_number(
          app_identifier: app_identifier,
          version: app_version
        )
      end
    rescue => e
      UI.important("Skipping TestFlight lookup: #{e.message}")
    end

    # Fallback to a timestamp-based build to guarantee monotonic increase without TestFlight lookup
    timestamp_build = Time.now.strftime("%Y%m%d%H%M").to_i
    next_build = [local_next_build, (latest_testflight || 0).to_i + 1, timestamp_build].max
    UI.message("Using app version #{app_version} with build #{next_build}")

    sh("cd #{root_dir} && flutter pub get")

    # Build the IPA with the computed build number
    export_plist = File.join(root_dir, 'ExportOptionsAppStore.plist')
    sh("cd #{root_dir} && flutter build ipa --release --build-number #{next_build} --export-options-plist=#{export_plist}")

    UI.success("Built IPA at build/ios/ipa with build #{next_build}")
  end

  desc "Build and upload to TestFlight with optional groups and changelog"
  lane :release_testflight do |options|
    require 'yaml'

    app_identifier = "com.abenezernuro.agenticfit"
    root_dir = File.expand_path('..', __dir__)
    fastlane_dir = __dir__

    pubspec = YAML.load_file(File.join(root_dir, 'pubspec.yaml'))
    raw_version = pubspec['version'].to_s
    app_version, current_build = raw_version.split('+')
    current_build_i = (current_build || '0').to_i
    local_next_build = current_build_i + 1

    if ENV['ASC_KEY_ID'] && ENV['ASC_ISSUER_ID'] && File.exist?(File.join(fastlane_dir, "AuthKey_#{ENV['ASC_KEY_ID']}.p8"))
      app_store_connect_api_key(
        key_id: ENV['ASC_KEY_ID'],
        issuer_id: ENV['ASC_ISSUER_ID'],
        key_filepath: File.join(fastlane_dir, "AuthKey_#{ENV['ASC_KEY_ID']}.p8"),
        duration: 1200,
        in_house: false
      )
      latest_testflight = latest_testflight_build_number(
        app_identifier: app_identifier,
        version: app_version
      )
    end

    timestamp_build = Time.now.strftime("%Y%m%d%H%M").to_i
    next_build = [local_next_build, (latest_testflight || 0).to_i + 1, timestamp_build].max

    sh("cd #{root_dir} && flutter pub get")

    export_plist = File.join(root_dir, 'ExportOptionsAppStore.plist')
    sh("cd #{root_dir} && flutter build ipa --release --build-number #{next_build} --export-options-plist=#{export_plist}")

    ipa_path = Dir[File.join(root_dir, 'build/ios/ipa/*.ipa')].first
    UI.user_error!("IPA not found") unless ipa_path && File.exist?(ipa_path)

    groups_opt = (options[:groups] || ENV['TESTFLIGHT_GROUPS'] || '').to_s
    groups_arr = groups_opt.split(',').map(&:strip).reject(&:empty?)
    changelog = (options[:changelog] || ENV['TESTFLIGHT_CHANGELOG'] || "").to_s

    pilot(
      ipa: ipa_path,
      app_identifier: app_identifier,
      skip_submission: true,
      skip_waiting_for_build_processing: false,
      distribute_external: groups_arr.any?,
      groups: groups_arr,
      changelog: changelog
    )

    UI.success("Uploaded #{File.basename(ipa_path)} to TestFlight (build #{next_build})")
  end
end


