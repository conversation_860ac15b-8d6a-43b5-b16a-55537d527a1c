---
alwaysApply: false
description: End-to-end deployment guide for TestFlight (iOS) and Firebase Hosting (Flutter Web) for this repo
---

# Deployment Playbook (TestFlight + Firebase Hosting)

This rule documents the exact, working deployment process configured in this repository for:

- TestFlight (iOS IPA build/upload)
- Firebase Hosting (Flutter Web CI/CD)

Key project files referenced in this rule:

- [Fastfile](mdc:fastlane/Fastfile)
- [ExportOptionsAppStore.plist](mdc:ExportOptionsAppStore.plist)
- [Info.plist](mdc:ios/Runner/Info.plist)
- [pubspec.yaml](mdc:pubspec.yaml)
- [firebase.json](mdc:firebase.json)
- [.firebaserc](mdc:.firebaserc)
- [firebase-hosting.yml](mdc:.github/workflows/firebase-hosting.yml)

## TestFlight (iOS)

Requirements:

- Xcode, CocoaPods, Flutter installed locally
- App Store Connect API key placed at `fastlane/AuthKey_<KEY_ID>.p8`
- Env vars exported in your shell:

```
export ASC_KEY_ID=<KEY_ID>
export ASC_ISSUER_ID=<ISSUER_ID>
```

Notes:

- The lane in [Fastfile](mdc:fastlane/Fastfile) auto-computes a safe next build number using TestFlight (if API key present) and falls back to a timestamp to guarantee monotonic increase. It reads app version from [pubspec.yaml](mdc:pubspec.yaml).
- `ITSAppUsesNonExemptEncryption=false` is set in [Info.plist](mdc:ios/Runner/Info.plist) to suppress the export compliance prompt.

Common commands:

```
# Build IPA only (no upload)
fastlane ios build_ipa

# Build and upload to TestFlight (waits for processing)
fastlane ios release_testflight

# Optional parameters
APP_IDENTIFIER=com.your.bundle fastlane ios release_testflight \
  groups:"External Testers" \
  changelog:"Bug fixes and improvements"
```

Artifacts:

- IPA output: `build/ios/ipa/AgenticFit.ipa`
- Bundle ID: `com.abenezernuro.agenticfit` (update in Xcode project settings if needed)

## Firebase Hosting (Flutter Web)

We deploy the static Flutter Web build to Firebase Hosting via GitHub Actions. Do not use Firebase App Hosting for this app (it expects a server runtime/buildpack).

Configured files:

- Hosting config: [firebase.json](mdc:firebase.json)
- Project mapping: [.firebaserc](mdc:.firebaserc)
- CI workflow: [firebase-hosting.yml](mdc:.github/workflows/firebase-hosting.yml)

One-time GitHub setup:

1. In your repo → Settings → Secrets and variables → Actions → New secret
2. Name: `FIREBASE_SERVICE_ACCOUNT_OPENFIT_AI`
3. Value: JSON for a service account with roles:
   - Firebase Hosting Admin
   - Service Account Token Creator

Deploy triggers:

- Push to `main` branch
- Or manually run the workflow “Deploy Flutter Web to Firebase Hosting”

What the workflow does:

1. Checks out the repo
2. Sets up Flutter (stable channel)
3. Runs `flutter pub get`
4. Builds web: `flutter build web --release`
5. Deploys Hosting with `firebase-tools` using project `po2vf2ae7tal9invaj7jkf4a06hsac`

Hosting URL:

- `https://po2vf2ae7tal9invaj7jkf4a06hsac.web.app`

## Troubleshooting

TestFlight:

- Build number collision: The lane uses timestamp fallback; rerun `fastlane ios build_ipa`.
- Export compliance prompt: Confirm `ITSAppUsesNonExemptEncryption=false` in [Info.plist](mdc:ios/Runner/Info.plist).

Hosting:

- JSON parse error in CI: Ensure [firebase.json](mdc:firebase.json) and [.firebaserc](mdc:.firebaserc) are valid single JSON objects (no merge markers or duplicate objects).
- Missing credentials: Add/refresh `FIREBASE_SERVICE_ACCOUNT_OPENFIT_AI` secret.
- Wrong branch: Update `branches: [ main ]` in [firebase-hosting.yml](mdc:.github/workflows/firebase-hosting.yml) if your default branch differs.

