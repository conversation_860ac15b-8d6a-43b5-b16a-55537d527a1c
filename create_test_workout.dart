// Simple script to create a test workout directly in the database
// This will help us verify the app can retrieve workouts properly

import 'package:supabase_flutter/supabase_flutter.dart';

Future<void> createTestWorkout() async {
  try {
    final client = Supabase.instance.client;
    final userId = client.auth.currentUser?.id;
    
    if (userId == null) {
      print('❌ No user logged in');
      return;
    }
    
    print('🔧 Creating test workout for user: $userId');
    
    // Create a test workout directly in the database
    final workoutResponse = await client
        .from('workouts')
        .insert({
          'user_id': userId,
          'name': 'Test Workout - Direct Insert',
          'description': 'A test workout created directly in the database',
          'duration': 45,
          'calories_burn': 300,
          'difficulty': 'Intermediate',
          'muscle_groups': ['chest', 'shoulders', 'triceps'],
          'equipment': ['dumbbells', 'bench'],
          'exercises': [
            {
              'name': 'Dumbbell Bench Press',
              'sets': 3,
              'reps': [10, 8, 6],
              'weight': [25, 30, 35],
              'rest_interval': 90,
              'order_index': 1
            },
            {
              'name': 'Dumbbell Shoulder Press',
              'sets': 3,
              'reps': [12, 10, 8],
              'weight': [15, 20, 25],
              'rest_interval': 60,
              'order_index': 2
            }
          ],
          'is_active': true,
        })
        .select()
        .single();
    
    print('✅ Test workout created successfully:');
    print('   ID: ${workoutResponse['id']}');
    print('   Name: ${workoutResponse['name']}');
    print('   User ID: ${workoutResponse['user_id']}');
    print('   Active: ${workoutResponse['is_active']}');
    
    // Also create a workout log entry
    await client
        .from('workout_logs')
        .insert({
          'user_id': userId,
          'workout_id': workoutResponse['id'],
          'workout_name': workoutResponse['name'],
          'workout_date': DateTime.now().toIso8601String(),
          'planned_workout': {
            'workout_name': workoutResponse['name'],
            'exercises': workoutResponse['exercises']
          },
          'actual_workout': {
            'workout_name': workoutResponse['name'],
            'exercises': []
          },
          'feedback': 'Test workout created via direct database insert',
          'additional_metrics': {
            'duration': 45,
            'calories_burned': 300
          }
        });
    
    print('✅ Workout log entry created');
    
  } catch (e) {
    print('❌ Error creating test workout: $e');
    print('   Error type: ${e.runtimeType}');
  }
}

// Call this function from your app to create a test workout