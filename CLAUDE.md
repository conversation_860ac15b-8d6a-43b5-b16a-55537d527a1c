# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🏋️ Project Overview

**OpenFit** is a Flutter-based AI-powered fitness and nutrition application with full Supabase backend integration. The app features user authentication, personalized workout management, onboarding flow, and a comprehensive design system based on the Sandow UI Kit.

### Tech Stack
- **Framework**: Flutter SDK ^3.8.1
- **Backend**: Supabase (Auth, Database, Real-time)
- **State Management**: Provider pattern
- **Design System**: Dark theme with Material 3
- **Platforms**: iOS, Android, Web, macOS, Linux, Windows

### Key Dependencies
- `supabase_flutter: ^2.3.4` - Backend services
- `provider: ^6.1.1` - State management
- `shared_preferences: ^2.2.3` - Local storage
- `cupertino_icons: ^1.0.8` - iOS-style icons

## 🏗️ Architecture

### State Management Architecture
The app uses Provider pattern with the following key providers:
- **AuthProvider** (`lib/providers/auth_provider.dart`) - User authentication and profile management
- **WorkoutProvider** (`lib/providers/workout_provider.dart`) - Workout data and state management
- **ThemeProvider** (`lib/providers/theme_provider.dart`) - Theme switching and persistence

### Service Layer
- **AuthService** (`lib/services/auth_service.dart`) - Supabase auth operations
- **DatabaseService** (`lib/services/database_service.dart`) - Profile and user data operations
- **WorkoutService** (`lib/services/workout_service.dart`) - Workout CRUD operations with Supabase

### Database Schema
Key tables in Supabase:
- `profiles` - User profiles with fitness data
- `workouts` - User workout definitions
- `workout_exercises` - Exercises within workouts
- `workout_sets` - Sets within exercises
- `exercises` - Exercise library
- `completed_workouts` - Workout history

## 📱 Navigation Flow

1. **Welcome Screen** (`/welcome`) → Sign Up/Sign In
2. **Sign Up** (`/signup`) → Onboarding → Home
3. **Sign In** (`/signin`) → Home (skips onboarding)
4. **Onboarding** (`/onboarding`) - 6-page assessment flow
5. **Home Screen** (`/home`) - Main dashboard with bottom nav

### Workout Flow
```
Home → Workout Screen → Workout Detail → Pre-Workout Overview → Active Workout → Workout Complete
```

## 🚨 CRITICAL DEVELOPMENT RULES

### 1. Theme-First Development (MANDATORY)
**NEVER hardcode design values. ALWAYS use theme configuration.**

```dart
// ✅ CORRECT
final theme = Theme.of(context);
Text('OpenFit', style: theme.textTheme.headlineMedium);

// ❌ WRONG
Text('OpenFit', style: TextStyle(fontSize: 28, color: Colors.white));
```

### 2. App Name
The app name is **OpenFit** (not OpenFit v4, not openfitv4).

### 3. Design System Compliance
- Use `AppColors`, `AppSpacing`, `AppBorderRadius` from `lib/theme/app_theme.dart`
- Follow DESIGN_GUIDE.md for all UI implementations
- Primary Color: `#F97316` (Orange)
- Background: `#111214` (Dark)

### 4. Code Quality Requirements
- Run `dart analyze` before marking any task complete - ZERO errors allowed
- Test all UI changes on both iOS and Android simulators
- Maintain database compatibility (other apps use the same Supabase instance)

### 5. Supabase Integration Rules
- Always use the service layer (WorkoutService, AuthService) for database operations
- Never expose Supabase credentials in frontend code
- Handle null safety for all database responses
- Use proper error handling with try-catch blocks
- Log operations with `developer.log()` instead of `print()`


## 🛠️ Common Development Commands

### Running the application
```bash
flutter run
# For specific platforms:
flutter run -d chrome      # Web
flutter run -d macos       # macOS
flutter run -d ios         # iOS (requires Xcode)
flutter run -d android     # Android (requires Android Studio/emulator)
```

### Build the application
```bash
flutter build apk          # Android APK
flutter build appbundle    # Android App Bundle
flutter build ios          # iOS (requires Xcode, macOS)
flutter build web          # Web
flutter build macos        # macOS desktop
flutter build linux        # Linux desktop
flutter build windows      # Windows desktop
```

### Testing
```bash
flutter test               # Run all tests
flutter test test/widget_test.dart  # Run specific test file
```

### Code Analysis & Formatting
```bash
dart analyze               # Run static analysis (MUST have zero errors)
flutter analyze            # Alternative static analysis
dart format lib/           # Format code
```

### Dependency Management
```bash
flutter pub get            # Install dependencies
flutter pub upgrade        # Upgrade dependencies
flutter pub outdated       # Check for outdated packages
```

### Development Tools
```bash
flutter doctor             # Check Flutter installation
flutter clean              # Clean build artifacts
flutter pub cache repair   # Repair pub cache if corrupted
```

### App Icons & Splash Screen
```bash
flutter pub run flutter_launcher_icons   # Generate app icons
flutter pub run flutter_native_splash:create  # Generate splash screen
```

## 📁 Key Files and Patterns

### Provider Setup (main.dart)
```dart
MultiProvider(
  providers: [
    ChangeNotifierProvider(create: (_) => ThemeProvider()),
    ChangeNotifierProvider(create: (_) => AuthProvider()),
    ChangeNotifierProxyProvider<AuthProvider, WorkoutProvider>(
      create: (context) => WorkoutProvider(),
      update: (context, auth, previous) => previous!..updateAuth(auth),
    ),
  ],
  child: MyApp(),
)
```

### Service Pattern Example
```dart
// WorkoutService pattern for database operations
final workouts = await _workoutService.getUserWorkouts(userId);
final success = await _workoutService.reorderWorkoutExercises(
  workoutId: workoutId,
  orderedWorkoutExerciseIds: ids,
);
```

### Widget Patterns
- Use `Consumer<Provider>` or `context.watch<Provider>()` for reactive UI
- Wrap forms in `Form` widget with `GlobalKey<FormState>`
- Use `CustomElevatedButton` and `CustomSecondaryButton` for consistent CTAs
- Implement loading states with `CircularProgressIndicator`

## 🔌 MCP Integration

The project has MCP (Model Context Protocol) tools configured:
- **Supabase MCP** - Database operations and management
- **Browser MCP** - Web testing and debugging
- **Simulator MCP** - iOS simulator control

Access Supabase MCP with prefix: `mcp__supabase_openfit_mcp-`

## 🐛 Common Issues and Solutions

### BoxConstraints Forces Infinite Height
- Never use `CrossAxisAlignment.stretch` in BottomSheets
- Wrap lists in height-constrained containers
- Use `mainAxisSize: MainAxisSize.min` in modal Columns

### Supabase Auth Issues
- Check `SupabaseConfig` for correct project URL and anon key
- Ensure user is authenticated before database operations
- Handle session expiry with proper error messages

### Image Assets Not Found
- All images must be in `assets/images/` directory
- Update `pubspec.yaml` assets section if adding new folders
- Use existing fallback images: `chest_press.png`, `yoga_woman.png`