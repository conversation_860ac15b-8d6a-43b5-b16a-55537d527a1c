import 'dart:developer' as developer;
import 'package:supabase_flutter/supabase_flutter.dart';

void main() async {
  print('🧪 Testing workout creation flow...');
  
  // Initialize Supabase
  await Supabase.initialize(
    url: 'https://ixqhqvvskdwjnxevqgvl.supabase.co',
    anon<PERSON>ey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml4cWhxdnZza2R3am54ZXZxZ3ZsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0NzI4NzQsImV4cCI6MjA1MDA0ODg3NH0.Ej7Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8',
  );
  
  final client = Supabase.instance.client;
  
  try {
    // Test 1: Check if we can query exercises table
    print('📋 Testing exercises table access...');
    final exercisesRes = await client
        .from('exercises')
        .select('id, name')
        .limit(5);
    
    print('✅ Found ${exercisesRes.length} exercises');
    for (final exercise in exercisesRes) {
      print('  - ${exercise['name']} (ID: ${exercise['id']})');
    }
    
    // Test 2: Create a test workout
    print('\n🏋️ Creating test workout...');
    final testUserId = 'test-user-${DateTime.now().millisecondsSinceEpoch}';
    
    final workoutRes = await client
        .from('workouts')
        .insert({
          'user_id': testUserId,
          'name': 'Test Workout Flow',
          'duration': 30,
          'is_active': true,
        })
        .select('id')
        .single();
    
    final workoutId = workoutRes['id'] as String;
    print('✅ Created workout with ID: $workoutId');
    
    // Test 3: Add exercises to the workout
    print('\n💪 Adding exercises to workout...');
    final exercises = [
      {
        'name': 'Push-ups',
        'sets': 3,
        'reps': [12, 10, 8],
        'weight': [0, 0, 0],
        'rest_interval': 60,
        'order_index': 0,
      },
      {
        'name': 'Squats',
        'sets': 3,
        'reps': [15, 12, 10],
        'weight': [0, 0, 0],
        'rest_interval': 60,
        'order_index': 1,
      },
    ];
    
    for (final exercise in exercises) {
      // Get a random exercise ID
      final randomExercise = await client
          .from('exercises')
          .select('id')
          .limit(1)
          .single();
      
      final exerciseId = randomExercise['id'] as String;
      
      await client
          .from('workout_exercises')
          .insert({
            'workout_id': workoutId,
            'exercise_id': exerciseId,
            'name': exercise['name'],
            'sets': exercise['sets'],
            'reps': exercise['reps'],
            'weight': exercise['weight'],
            'rest_interval': exercise['rest_interval'],
            'order_index': exercise['order_index'],
          });
      
      print('✅ Added ${exercise['name']} to workout');
    }
    
    // Test 4: Verify the workout was created correctly
    print('\n🔍 Verifying workout creation...');
    final verifyRes = await client
        .from('workout_exercises')
        .select('name, sets, reps, weight, rest_interval')
        .eq('workout_id', workoutId);
    
    print('✅ Workout verification:');
    for (final ex in verifyRes) {
      print('  - ${ex['name']}: ${ex['sets']} sets, reps: ${ex['reps']}, weight: ${ex['weight']}');
    }
    
    print('\n🎉 All tests passed! Workout creation flow is working correctly.');
    
  } catch (e, stackTrace) {
    print('❌ Test failed: $e');
    print('Stack trace: $stackTrace');
  }
}