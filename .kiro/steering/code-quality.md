---
inclusion: always
---

# Code Quality and Analysis Requirements

## MANDATORY: Post-Task Analysis

**CRITICAL RULE**: After completing ANY code modification task, you MUST run `flutter analyze` to ensure zero syntax errors.

### Required Workflow
1. Complete the requested code changes
2. **IMMEDIATELY** run `flutter analyze`
3. If errors found: Fix them before considering task complete
4. Only mark task as complete when analysis shows zero errors

### Analysis Command
```bash
flutter analyze
```

### Expected Output
```
Analyzing openfitv4...
No issues found! (ran in X.Xs)
```

### If Errors Found
- **DO NOT** mark task as complete
- Fix all reported errors immediately
- Re-run `flutter analyze` until clean
- Explain what was fixed

### Error Categories to Watch For
- **Syntax Errors**: Missing semicolons, brackets, parentheses
- **Import Errors**: Missing or incorrect imports
- **Type Errors**: Incorrect type annotations or assignments
- **Null Safety**: Missing null checks or incorrect nullable types
- **Unused Variables**: Remove or prefix with underscore
- **Linting Issues**: Follow Flutter/Dart style guidelines

### Quality Standards
- **Zero tolerance** for syntax errors
- **Zero tolerance** for analyzer warnings in new code
- **Maintain compatibility** with existing codebase
- **Follow project conventions** established in existing files

### Additional Quality Checks
When making significant changes, also consider:
```bash
dart format lib/                    # Format code
flutter test                       # Run tests if available
flutter doctor                     # Check environment health
```

### Exception Handling
If analyzer reports errors in existing code (not your changes):
1. Note the pre-existing issues
2. Focus on ensuring YOUR changes don't add new errors
3. Mention the pre-existing issues to the user
4. Suggest fixing them in a separate task if needed

## Code Modification Best Practices

### Before Making Changes
- Understand the existing code structure
- Check imports and dependencies
- Review similar patterns in the codebase

### During Changes
- Follow existing naming conventions
- Maintain consistent indentation and formatting
- Add necessary imports immediately
- Use proper null safety practices

### After Changes
- **MANDATORY**: Run `flutter analyze`
- Verify the changes work as expected
- Check that no functionality was broken
- Ensure all imports are used and necessary

This rule ensures that every code modification maintains the highest quality standards and prevents syntax errors from being introduced into the OpenFit codebase.