# Project Structure & Architecture

## Architecture Pattern
The app follows a **Provider + Service Layer** architecture with clear separation of concerns:

- **Providers**: State management using Flutter Provider pattern
- **Services**: Business logic and external API interactions
- **Models**: Data structures and entity definitions
- **Screens**: UI components organized by feature
- **Widgets**: Reusable UI components

## Directory Structure

```
lib/
├── config/           # App configuration (Supabase, etc.)
├── data/            # Static data and sample content
├── models/          # Data models and entity classes
├── providers/       # State management (Provider pattern)
├── screens/         # UI screens organized by feature
│   ├── onboarding/  # User onboarding flow
│   └── workout/     # Workout-related screens
├── services/        # Business logic and API services
├── theme/           # App theming and styling
└── widgets/         # Reusable UI components
```

## Key Architecture Components

### Providers (State Management)
- `AuthProvider` - Authentication state and user session
- `ThemeProvider` - App theming and dark/light mode
- `WorkoutProvider` - Workout data and exercise state

### Services (Business Logic)
- `AuthService` - Supabase authentication operations
- `DatabaseService` - Database operations and data persistence
- `WorkoutService` - Workout-related business logic

### Models
- `UserModel` - User profile and preferences
- `WorkoutData` - Exercise and workout data structures
- `OnboardingData` - User onboarding information

## Navigation & Routing
- Uses named routes defined in `main.dart`
- Route structure: `/welcome`, `/signin`, `/signup`, `/onboarding`, `/home`
- Conditional navigation based on authentication and onboarding state

## Naming Conventions
- **Files**: snake_case (e.g., `auth_provider.dart`)
- **Classes**: PascalCase (e.g., `AuthProvider`)
- **Variables/Methods**: camelCase (e.g., `isSignedIn`)
- **Constants**: SCREAMING_SNAKE_CASE (e.g., `SUPABASE_URL`)

## Code Organization Rules
- One class per file
- Group imports: Flutter SDK, third-party packages, local imports
- Use relative imports for local files
- Prefer composition over inheritance
- Keep widgets focused and single-purpose