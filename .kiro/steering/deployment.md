---
inclusion: manual
---

# Deployment Guidance for OpenFit

This steering provides deployment guidance for the OpenFit Flutter application across multiple platforms.

## Project Configuration

### App Details
- **App Name**: OpenFit (not OpenFit v4 or openfitv4)
- **Bundle ID**: `com.abenezernuro.agenticfit`
- **Package Name**: `openfitv4`
- **Current Version**: 1.0.0+3

### Platform Support
- **Primary**: iOS (TestFlight)
- **Secondary**: Android, Web (Firebase Hosting)
- **Desktop**: macOS, Linux, Windows

## TestFlight Deployment (iOS)

### Prerequisites
- Xcode, CocoaPods, Flutter installed locally
- App Store Connect API key at `fastlane/AuthKey_<KEY_ID>.p8`
- Environment variables:
  ```bash
  export ASC_KEY_ID=<KEY_ID>
  export ASC_ISSUER_ID=<ISSUER_ID>
  ```

### Deployment Commands
```bash
# Build IPA only (no upload)
fastlane ios build_ipa

# Build and upload to TestFlight
fastlane ios release_testflight

# With optional parameters
APP_IDENTIFIER=com.abenezernuro.agenticfit fastlane ios release_testflight \
  groups:"External Testers" \
  changelog:"Bug fixes and improvements"
```

### Key Files
- **Fastfile**: `fastlane/Fastfile` - Deployment automation
- **Export Options**: `ExportOptionsAppStore.plist` - Build configuration
- **Info.plist**: `ios/Runner/Info.plist` - App metadata
- **Version Source**: `pubspec.yaml` - Version management

### Build Artifacts
- **IPA Output**: `build/ios/ipa/AgenticFit.ipa`
- **Auto Build Numbers**: Uses TestFlight API or timestamp fallback

## Firebase Hosting (Web)

### Configuration
- **Project ID**: `po2vf2ae7tal9invaj7jkf4a06hsac`
- **Hosting URL**: `https://po2vf2ae7tal9invaj7jkf4a06hsac.web.app`
- **Config Files**: `firebase.json`, `.firebaserc`
- **CI/CD**: `.github/workflows/firebase-hosting.yml`

### GitHub Actions Setup
1. **Secret Required**: `FIREBASE_SERVICE_ACCOUNT_OPENFIT_AI`
2. **Service Account Roles**:
   - Firebase Hosting Admin
   - Service Account Token Creator

### Deployment Triggers
- Push to `main` branch
- Manual workflow dispatch

### Build Process
1. Flutter setup (stable channel)
2. `flutter pub get`
3. `flutter build web --release`
4. Firebase deploy via `firebase-tools`

## Development Commands

### Build Commands
```bash
# Platform-specific builds
flutter build apk --release          # Android APK
flutter build ios --release          # iOS
flutter build web --release          # Web
flutter build macos --release        # macOS
flutter build linux --release        # Linux
flutter build windows --release      # Windows
```

### Testing & Quality
```bash
flutter test                         # Run tests
flutter analyze                      # Static analysis (must be zero errors)
dart format lib/                     # Code formatting
flutter doctor                       # Environment check
```

### Asset Generation
```bash
flutter pub run flutter_launcher_icons        # Generate app icons
flutter pub run flutter_native_splash:create  # Generate splash screens
```

## Troubleshooting

### TestFlight Issues
- **Build Number Collision**: Rerun `fastlane ios build_ipa` (uses timestamp fallback)
- **Export Compliance**: Ensure `ITSAppUsesNonExemptEncryption=false` in Info.plist
- **API Key Issues**: Verify ASC_KEY_ID and ASC_ISSUER_ID environment variables

### Firebase Hosting Issues
- **JSON Parse Error**: Validate `firebase.json` and `.firebaserc` syntax
- **Missing Credentials**: Check `FIREBASE_SERVICE_ACCOUNT_OPENFIT_AI` secret
- **Wrong Branch**: Ensure workflow targets correct branch (default: `main`)

### General Build Issues
- **Dependencies**: Run `flutter clean && flutter pub get`
- **Platform Issues**: Check `flutter doctor` for missing dependencies
- **Version Conflicts**: Update `pubspec.yaml` SDK constraints if needed

## Version Management

### Current Configuration
- **SDK Constraint**: `^3.6.0` (adjusted for compatibility)
- **Version Format**: `major.minor.patch+build`
- **Auto-increment**: Build numbers managed by Fastlane

### Release Process
1. Update version in `pubspec.yaml`
2. Test locally with `flutter run`
3. Run static analysis: `flutter analyze`
4. Deploy via appropriate method (TestFlight/Firebase)
5. Verify deployment on target platform

## Security Considerations

### API Keys & Secrets
- Never commit API keys to repository
- Use environment variables for sensitive data
- Store Firebase service account JSON as GitHub secret
- App Store Connect API keys in `fastlane/` directory (gitignored)

### App Store Compliance
- Export compliance handled via Info.plist
- Privacy policy required for App Store submission
- TestFlight beta review process for external testers

This deployment configuration supports the full OpenFit application lifecycle from development to production across all supported platforms.