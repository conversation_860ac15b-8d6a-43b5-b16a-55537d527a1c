# Technology Stack & Build System

## Core Technologies
- **Flutter/Dart**: Cross-platform mobile development framework
- **Supabase**: Backend-as-a-Service for authentication, database, and real-time features
- **Firebase**: Web hosting and additional backend services
- **Provider**: State management pattern for Flutter

## Key Dependencies
- `supabase_flutter: ^2.3.4` - Supabase integration
- `provider: ^6.1.1` - State management
- `shared_preferences: ^2.2.3` - Local storage
- `http: ^1.2.2` - HTTP requests
- `crypto: ^3.0.3` - Cryptographic functions

## Development Tools
- `flutter_lints: ^5.0.0` - Dart/Flutter linting rules
- `flutter_launcher_icons: ^0.14.4` - App icon generation
- `flutter_native_splash: ^2.4.6` - Splash screen generation

## Build & Development Commands

### Basic Flutter Commands
```bash
# Install dependencies
flutter pub get

# Run app in debug mode
flutter run

# Build for production
flutter build apk --release          # Android APK
flutter build ios --release          # iOS
flutter build web --release          # Web

# Run tests
flutter test

# Analyze code
flutter analyze

# Clean build artifacts
flutter clean
```

### Deployment
- **iOS**: Uses Fastlane for automated builds and TestFlight deployment
- **Web**: Firebase hosting (`firebase deploy`)
- **Android**: Standard APK/AAB builds

## Configuration Files
- `pubspec.yaml` - Dependencies and app metadata
- `analysis_options.yaml` - Dart linting configuration
- `firebase.json` - Firebase hosting configuration
- `fastlane/Fastfile` - iOS deployment automation