{"enabled": true, "name": "Kiro Response Sound", "description": "Plays a sound notification whenever <PERSON><PERSON> completes a response", "version": "1", "when": {"type": "userTriggered", "patterns": [".kiro/**/*"]}, "then": {"type": "askAgent", "prompt": "Play a notification sound to indicate that <PERSON><PERSON> has finished responding. Use a pleasant, subtle sound that signals completion without being disruptive."}}