{"mcpServers": {"n8n-mcp": {"command": "npx", "args": ["n8n-mcp"], "env": {"MCP_MODE": "stdio", "LOG_LEVEL": "error", "DISABLE_CONSOLE_OUTPUT": "true", "N8N_API_URL": "https://sciwell.app.n8n.cloud", "N8N_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI5Zjk5NDZmYi03Y2RiLTRkZWYtODRkYi1mN2RjOGJlYTIzZDIiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU0NjE1MjUxfQ.GzMVLSgMJOXBB-qbpDt0UNi5NChQs9i3tmfqQdK_-s4"}, "disabled": false, "autoApprove": ["n8n_*", "n8n_list_available_tools", "tools_documentation", "n8n_list_workflows", "n8n_get_workflow_structure", "n8n_get_workflow_details", "n8n_list_executions", "n8n_trigger_webhook_workflow", "n8n_get_execution", "n8n_update_partial_workflow", "n8n_update_full_workflow", "get_node_essentials", "search_nodes", "n8n_get_workflow"]}, "supabase_openfit_mcp": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"], "disabled": false, "autoApprove": ["list_organizations", "get_organization", "list_projects", "get_project", "list_branches", "list_tables", "list_extensions", "list_migrations", "get_user", "list_users", "list_edge_functions", "get_logs", "get_advisors", "get_project_url", "get_anon_key", "generate_typescript_types", "search_docs", "list_tables", "execute_sql"]}}}