
The supabase id is xtazgqpcaujwwaswzeoh
This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 📱 Navigation Flow

### Complete App Flow Structure
1. **Welcome Screen** (`/welcome`) - Initial landing page
   - "Get Started" button → Sign Up Screen
   - "Sign In" link → Sign In Screen

2. **Sign Up Screen** (`/signup`) - New user registration
   - On successful sign up → Onboarding Screen

3. **Sign In Screen** (`/signin`) - Existing user login
   - On successful sign in → Home Screen (skips onboarding)
   - "Sign Up" link → Sign Up Screen
   - "Forgot Password" → (Future implementation)

4. **Onboarding Screen** (`/onboarding`) - New user assessment
   - 6 sequential screens with progress indicator:
     1. Gender Selection (Male/Female)
     2. Age Input (Picker 1-100)
     3. Weight Input (kg/lbs toggle)
     4. Height Input (cm or ft/in toggle)
     5. Activity Level (5 options)
     6. Fitness Goal (6 options)
   - On completion → Home Screen

5. **Home Screen** (`/home`) - Main app dashboard
   - Bottom navigation with 5 tabs:
     - Home (Dashboard)
     - Workout
     - Nutrition
     - Progress
     - Profile
   - Profile tab "Sign Out" → Welcome Screen

## 🚨 CRITICAL DEVELOPMENT RULES

### 1. Theme-First Development (MANDATORY)
**NEVER hardcode design values. ALWAYS use theme configuration.**

```dart
// ✅ CORRECT
final theme = Theme.of(context);
Text('OpenFit', style: theme.textTheme.headlineMedium);

// ❌ WRONG
Text('OpenFit', style: TextStyle(fontSize: 28, color: Colors.white));
```

### 2. App Name
The app name is **OpenFit** (not OpenFit v4, not openfitv4). Use this consistently everywhere.

### 3. Design System Compliance
- Follow DESIGN_GUIDE.md for all UI implementations
- Use `AppColors`, `AppSpacing`, `AppBorderRadius` from `lib/theme/app_theme.dart`
- All text must use predefined text styles from theme
- All colors must come from the theme's color scheme

### 4. Code Quality Requirements
- Run `dart analyze` before marking any task complete - ZERO errors allowed
- Follow Flutter best practices and conventions
- Create reusable widgets for repeated patterns
- Keep widgets focused and single-responsibility

### 5. BottomSheet/Layout Stability Rules (NEW)
To prevent infinite-height and “BoxConstraints forces an infinite height” errors in modals and constrained parents:
- Never set `crossAxisAlignment: CrossAxisAlignment.stretch` on a `Row` inside a BottomSheet, Dialog, or any unconstrained vertical container unless children have explicit heights.
- Prefer direct buttons with explicit `height` parameters over wrapping them in `SizedBox(height: ...)` inside stretched Rows. If a fixed height is needed, pass it to the button widgets (e.g., `CustomElevatedButton(height: 56)`).
- Avoid nesting `SizedBox(height: ...)` within a `Row` with `CrossAxisAlignment.stretch` inside BottomSheets.
- When using `Expanded` children in a BottomSheet, ensure vertical size is constrained by:
  - Parent padding and content, and
  - Buttons taking explicit heights (no unbounded vertical growth).
- Keep BottomSheet content within a `Column(mainAxisSize: MainAxisSize.min)` so it only occupies needed height.
- If using lists inside sheets, wrap them in a height-constrained widget (e.g., `SizedBox(height: 300, child: ListView(...))`).
- Use `SafeArea(top: false)` and internal padding instead of relying on stretching for height.

### 6. Pre-Commit Checks (NEW)
Always before marking a task complete:
- Run `dart analyze` and resolve all errors. Warnings should be addressed when practical, especially unused imports/elements introduced by refactors.
- Manually test BottomSheets and Dialogs on at least one iOS and one Android simulator size:
  - Open the sheet
  - Interact with all CTAs
  - Rotate or resize if applicable
- Verify navigation flow matches the Navigation Flow section (e.g., Workout Detail → Pre-Workout Overview → Active Workout).

## 🎨 Figma MCP Integration

### Using Two Figma MCPs Effectively
We have two Figma MCP tools that serve different purposes:

#### 1. **figma-dev-mode-mcp-server** (Primary for Design Understanding)
Best for:
- Getting visual previews of designs (`get_image`)
- Understanding layout and component structure (`get_code`)
- Quick design exploration
- Initial code structure reference

**Workflow**: Always call `get_image` after `get_code` to see the visual context

#### 2. **framelink-figma-mcp** (Primary for Asset Management)
Best for:
- Downloading actual image assets (backgrounds, icons)
- Getting detailed node data and measurements
- Extracting specific design tokens

### Recommended Figma Workflow
1. Start with `mcp__figma-dev-mode-mcp-server__get_code` to understand structure
2. Always follow with `mcp__figma-dev-mode-mcp-server__get_image` for visual context
3. Use `mcp__framelink-figma-mcp__download_figma_images` for actual assets
4. Convert React/HTML structure to Flutter widgets using theme configuration

## Project Overview

**OpenFit** is a Flutter-based AI-powered fitness and nutrition application. The project uses a dark theme design system based on the Sandow UI Kit from Figma. It targets iOS, Android, Web, and desktop platforms (macOS, Linux, Windows).

## Tech Stack

- **Framework**: Flutter (SDK ^3.8.1)
- **Language**: Dart
- **Platforms**: iOS, Android, Web, macOS, Linux, Windows
- **Dependencies**: 
  - cupertino_icons: ^1.0.8
- **Dev Dependencies**:
  - flutter_test (for testing)
  - flutter_lints: ^5.0.0 (for code analysis)

## Common Development Commands

### Run the application
```bash
flutter run
# For specific platforms:
flutter run -d chrome      # Web
flutter run -d macos       # macOS
flutter run -d ios         # iOS (requires Xcode)
flutter run -d android     # Android (requires Android Studio/emulator)
```

### Build the application
```bash
flutter build apk          # Android APK
flutter build appbundle    # Android App Bundle
flutter build ios          # iOS (requires Xcode, macOS)
flutter build web          # Web
flutter build macos        # macOS desktop
flutter build linux        # Linux desktop
flutter build windows      # Windows desktop
```

### Testing
```bash
flutter test               # Run all tests
flutter test test/widget_test.dart  # Run specific test file
```

### Code Analysis & Formatting
```bash
flutter analyze            # Run static analysis
dart analyze               # Alternative static analysis (no errors allowed)
dart format .              # Format all Dart files
dart format lib/           # Format specific directory
```

**IMPORTANT**: Always run `dart analyze` before completing any task to ensure there are no static analysis errors. The project must pass static analysis with zero errors.

### Dependency Management
```bash
flutter pub get            # Install dependencies
flutter pub upgrade        # Upgrade dependencies
flutter pub outdated       # Check for outdated packages
```

### Development Tools
```bash
flutter doctor             # Check Flutter installation and dependencies
flutter clean              # Clean build artifacts
flutter pub cache repair   # Repair pub cache if corrupted
```

## Project Structure

The project follows standard Flutter conventions:

- `lib/` - Main application code
  - `main.dart` - Entry point containing MyApp and MyHomePage widgets
- `test/` - Unit and widget tests
- `android/` - Android platform-specific code
- `ios/` - iOS platform-specific code
- `web/` - Web platform-specific code
- `macos/`, `linux/`, `windows/` - Desktop platform-specific code
- `pubspec.yaml` - Project configuration and dependencies
- `analysis_options.yaml` - Dart analyzer configuration using flutter_lints

## Key Development Notes

1. **Hot Reload**: Use 'r' in terminal or IDE hot reload button during `flutter run` for instant UI updates
2. **Hot Restart**: Use 'R' for full restart when state needs to be reset
3. **State Management**: Currently uses basic StatefulWidget with setState()
4. **Linting**: Project uses flutter_lints package for code quality
5. **Material Design**: App uses Material 3 design with ColorScheme.fromSeed()

## Testing Approach

The project includes a basic widget test in `test/widget_test.dart`. When adding new features:
1. Add corresponding widget tests in the `test/` directory
2. Run `flutter test` to ensure all tests pass before committing changes

## Design System

The project follows a comprehensive design system based on the Sandow UI Kit. Key resources:

- **DESIGN_GUIDE.md** - Complete design specifications including colors, typography, spacing, and component guidelines
- **lib/theme/app_theme.dart** - Flutter theme implementation with all design tokens

### Quick Reference
- Primary Color: `#F97316` (Orange)
- Background: `#111214` (Dark)
- Font: Work Sans
- Spacing: 8px grid system
- See DESIGN_GUIDE.md for complete specifications
