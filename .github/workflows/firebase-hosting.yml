name: Deploy Flutter Web to Firebase Hosting

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          channel: stable

      - name: Flutter pub get
        run: flutter pub get

      - name: Build Flutter Web
        run: flutter build web --release --base-href "/"

      - name: Deploy to Firebase Hosting
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: ${{ secrets.GITHUB_TOKEN }}
          firebaseServiceAccount: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_OPENFIT_AI }}
          channelId: live
          projectId: po2vf2ae7tal9invaj7jkf4a06hsac


